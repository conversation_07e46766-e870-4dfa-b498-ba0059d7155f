/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Accordion: typeof import('./src/components/Accordion/index.vue')['default']
    AddressCard: typeof import('./src/components/AddressCard/index.vue')['default']
    AddressContent: typeof import('./src/components/AddressCard/components/AddressContent.vue')['default']
    AddressForm: typeof import('./src/components/Address/AddressForm.vue')['default']
    AddressHeader: typeof import('./src/components/AddressCard/components/AddressHeader.vue')['default']
    AddressListDialog: typeof import('./src/components/AddressCard/components/AddressListDialog.vue')['default']
    AITextarea: typeof import('./src/components/AITextarea/index.vue')['default']
    BlocksModal: typeof import('./src/components/BuildingBlocks/BlocksModal.vue')['default']
    BottonExcel: typeof import('./src/components/BottonExcel/index.vue')['default']
    BtnComponent: typeof import('./src/components/BtnComponent/index.vue')['default']
    BulkModal: typeof import('./src/components/BulkSetting/bulkModal.vue')['default']
    BulkSetting: typeof import('./src/components/BulkSetting/index.vue')['default']
    CascaderAddress: typeof import('./src/components/CascaderAddress/index.vue')['default']
    Child: typeof import('./src/components/LayOut/modules/gloableSide/components/child.vue')['default']
    ComponentSelectDialog: typeof import('./src/components/SelectDialog/ComponentSelectDialog.vue')['default']
    Container: typeof import('./src/components/AddressCard/Container.vue')['default']
    CoverImage: typeof import('./src/components/UploadFile/CoverImage/index.vue')['default']
    CustomRequest: typeof import('./src/components/CustomRequest/index.vue')['default']
    DataAuth: typeof import('./src/components/DataAuth/index.vue')['default']
    DataAuthDetail: typeof import('./src/components/DataAuth/DataAuthDetail.vue')['default']
    DataChange: typeof import('./src/components/DataChange/index.vue')['default']
    DeliveryCard: typeof import('./src/components/AddressCard/DeliveryCard.vue')['default']
    DepartmentTree: typeof import('./src/components/DepartmentTree/index.vue')['default']
    DescriptionsFormItem: typeof import('./src/components/DescriptionsFormItem.vue')['default']
    DetailPageInfo: typeof import('./src/components/DetailPageInfo/index.vue')['default']
    ElAffix: typeof import('element-plus/es')['ElAffix']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAnchor: typeof import('element-plus/es')['ElAnchor']
    ElAnchorLink: typeof import('element-plus/es')['ElAnchorLink']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCheckTag: typeof import('element-plus/es')['ElCheckTag']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectComponent: typeof import('./src/components/SelectMergeComponent/ElSelectComponent/index.vue')['default']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExelImport: typeof import('./src/components/ExelImport/index.vue')['default']
    FibreCount: typeof import('./src/components/FibreCount/index.vue')['default']
    FildCard: typeof import('./src/components/FildCard.vue')['default']
    FileCard: typeof import('./src/components/UploadFile/FileCard/index.vue')['default']
    FlashButton: typeof import('./src/components/AITextarea/components/FlashButton.vue')['default']
    GloableHeader: typeof import('./src/components/LayOut/modules/gloableHeader/index.vue')['default']
    GloableSide: typeof import('./src/components/LayOut/modules/gloableSide/index.vue')['default']
    GloableTab: typeof import('./src/components/LayOut/modules/gloableTab/index.vue')['default']
    GridTable: typeof import('./src/components/GridTable/index.vue')['default']
    GroupingTable: typeof import('./src/components/GroupingTable/index.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Icons: typeof import('./src/components/Icons/index.vue')['default']
    'Index copy': typeof import('./src/components/SelectDate/index copy.vue')['default']
    Index_v1: typeof import('./src/components/LayOut/modules/gloableSide/index_v1.vue')['default']
    InputSelect: typeof import('./src/components/InputSelect/index.vue')['default']
    Item: typeof import('./src/components/BulkSetting/item.vue')['default']
    LayOut: typeof import('./src/components/LayOut/index.vue')['default']
    List: typeof import('./src/components/Address/List.vue')['default']
    ListCard: typeof import('./src/components/ListCard/index.vue')['default']
    Loader: typeof import('./src/components/AITextarea/components/Loader.vue')['default']
    'Menu copy': typeof import('./src/components/LayOut/modules/gloableSide/menu copy.vue')['default']
    MenuSelect: typeof import('./src/components/LayOut/modules/gloableHeader/components/menuSelect.vue')['default']
    MiddleMenu: typeof import('./src/components/LayOut/modules/gloableSide/components/middleMenu.vue')['default']
    Modal: typeof import('./src/components/FibreCount/modal/index.vue')['default']
    ModeBox: typeof import('./src/components/ModeBox.vue')['default']
    NavTab: typeof import('./src/components/navTab.vue')['default']
    OrderStatusTag: typeof import('./src/components/OrderStatusTag/index.vue')['default']
    Output: typeof import('./src/components/AITextarea/components/Output.vue')['default']
    PrintBtn: typeof import('./src/components/PrintBtn/index.vue')['default']
    PrintPopoverBtn: typeof import('./src/components/PrintPopoverBtn/index.vue')['default']
    ProductType: typeof import('./src/components/SelectCascader/productType.vue')['default']
    QywxPopover: typeof import('./src/components/SelectDialog/QywxPopover.vue')['default']
    QyxwBindingProvider: typeof import('./src/components/SelectDialog/QyxwBindingProvider.vue')['default']
    RecurrenceMenu: typeof import('./src/components/LayOut/modules/gloableSide/components/recurrenceMenu.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScanerInput: typeof import('./src/components/ScanerInput/index.vue')['default']
    SearchTable: typeof import('./src/components/SearchTable.vue')['default']
    SelectAddress: typeof import('./src/components/SelectAddress/index.vue')['default']
    SelectBottomBar: typeof import('./src/components/SelectComponents/SelectBottomBar.vue')['default']
    SelectBusinessDialog: typeof import('./src/components/SelectBusinessDialog/index.vue')['default']
    SelectCascader: typeof import('./src/components/SelectCascader/index.vue')['default']
    SelectComponents: typeof import('./src/components/SelectComponents/index.vue')['default']
    SelectCustomerDialog: typeof import('./src/components/SelectCustomerDialog/index.vue')['default']
    SelectDate: typeof import('./src/components/SelectDate/index.vue')['default']
    SelectDialog: typeof import('./src/components/SelectDialog/index.vue')['default']
    SelectGreyFabricDialog: typeof import('./src/components/SelectGreyFabricDialog/index.vue')['default']
    SelectMergeComponent: typeof import('./src/components/SelectMergeComponent/index.vue')['default']
    SelectOptions: typeof import('./src/components/SelectOptions/index.vue')['default']
    SelectProductColorDialog: typeof import('./src/components/SelectProductColorDialog/index.vue')['default']
    SelectProductDialog: typeof import('./src/components/SelectProductDialog/index.vue')['default']
    SelectRawMaterial: typeof import('./src/components/SelectRawMaterial/index.vue')['default']
    SelectRawMaterialColorDialog: typeof import('./src/components/SelectRawMaterialColorDialog/index.vue')['default']
    SelectRawMaterialDialog: typeof import('./src/components/SelectRawMaterialDialog/index.vue')['default']
    SelectReturnAddress: typeof import('./src/components/SelectReturnAddress/index.vue')['default']
    SelectRole: typeof import('./src/components/SelectRole/index.vue')['default']
    SelectSaleMode: typeof import('./src/components/SelectSaleMode/index.vue')['default']
    SelectSaleSystemDialog: typeof import('./src/components/SelectSaleSystemDialog/index.vue')['default']
    SelectSettleAccountDialog: typeof import('./src/components/SelectSettleAccountDialog/index.vue')['default']
    SelectSettleTypeDialog: typeof import('./src/components/SelectSettleTypeDialog/index.vue')['default']
    SelectTable: typeof import('./src/components/SelectTable.vue')['default']
    SelectTypeCapitalExpensesDialog: typeof import('./src/components/SelectTypeCapitalExpensesDialog/index.vue')['default']
    SelectVirtualOptions: typeof import('./src/components/SelectVirtualOptions/index.vue')['default']
    SideTree: typeof import('./src/components/SideTree.vue')['default']
    Status: typeof import('./src/components/Status/index.vue')['default']
    StatusBtn: typeof import('./src/components/StatusBtn/index.vue')['default']
    StatusColumn: typeof import('./src/components/StatusColumn/index.vue')['default']
    StatusTag: typeof import('./src/components/StatusTag/index.vue')['default']
    StockScanerInput: typeof import('./src/components/ScanerInput/stockScanerInput.vue')['default']
    SubMenu: typeof import('./src/components/LayOut/modules/gloableSide/components/subMenu.vue')['default']
    Svglcon: typeof import('./src/components/Svglcon/index.vue')['default']
    Table: typeof import('./src/components/Table.vue')['default']
    TableField: typeof import('./src/components/TableField/index.vue')['default']
    Textarea: typeof import('./src/components/AITextarea/components/Textarea.vue')['default']
    TextureMapWall: typeof import('./src/components/TextureMapWall/index.vue')['default']
    Tour: typeof import('./src/components/Tour/index.vue')['default']
    Upload: typeof import('./src/components/Upload/index.vue')['default']
    UploadFile: typeof import('./src/components/UploadFile/index.vue')['default']
    UserSelection: typeof import('./src/components/UserSelection/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
