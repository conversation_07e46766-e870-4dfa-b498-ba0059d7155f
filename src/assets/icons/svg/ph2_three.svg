<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="26px" viewBox="0 0 20 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 13备份 2</title>
    <defs>
        <linearGradient x1="25.3427667%" y1="23.553719%" x2="70.3997683%" y2="76.446281%" id="linearGradient-1">
            <stop stop-color="#FE5B36" offset="0%"></stop>
            <stop stop-color="#FE5E32" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="25.3427667%" y1="23.553719%" x2="76.3554608%" y2="72.1548151%" id="linearGradient-2">
            <stop stop-color="#FE8E4B" offset="0%"></stop>
            <stop stop-color="#FE853E" offset="100%"></stop>
        </linearGradient>
        <circle id="path-3" cx="10" cy="16" r="10"></circle>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.786930692   0 0 0 0 0.675279735   0 0 0 0 0.535716039  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <circle id="path-5" cx="10" cy="16" r="7"></circle>
        <filter x="-14.3%" y="-14.3%" width="128.6%" height="128.6%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.718859686   0 0 0 0 0.568697473   0 0 0 0 0.392897809  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-1" dy="-1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.753224248   0 0 0 0 0.585438745   0 0 0 0 0.389006937  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-273.000000, -1280.000000)" id="编组-14备份">
            <g transform="translate(218.000000, 1088.000000)">
                <g id="编组-13备份-2" transform="translate(55.000000, 192.000000)">
                    <polygon id="矩形" fill="url(#linearGradient-1)" points="0 8.52651283e-14 6.6 8.52651283e-14 11 8 4.4 8"></polygon>
                    <polygon id="矩形备份-33" fill="url(#linearGradient-2)" transform="translate(14.500000, 4.000000) scale(-1, 1) translate(-14.500000, -4.000000) " points="9 8.52651283e-14 15.6 8.52651283e-14 20 8 13.4 8"></polygon>
                    <g id="椭圆形">
                        <use fill="#EBCFAC" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    </g>
                    <g id="椭圆形备份-3">
                        <use fill="#D7AE7E" fill-rule="evenodd" xlink:href="#path-5"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    </g>
                    <text id="3" font-family="DINAlternate-Bold, DIN Alternate" font-size="12" font-weight="bold" fill="#FFFDF3">
                        <tspan x="7.12011719" y="20">3</tspan>
                    </text>
                </g>
            </g>
        </g>
    </g>
</svg>