<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 46</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页-全展示" transform="translate(-1455.000000, -305.000000)">
            <g id="编组-46" transform="translate(1455.000000, 305.000000)">
                <rect id="矩形备份-17" fill="#5DD6B1" x="0" y="0" width="30" height="30" rx="4"></rect>
                <g id="组件/图标/功能类/快递备份-9" transform="translate(3.000000, 3.000000)">
                    <rect id="点击热区" fill="#D8D8D8" opacity="0" x="0" y="0" width="24" height="24"></rect>
                    <path d="M22.125,4.875 C22.7463203,4.875 23.25,5.37867966 23.25,6 C23.25,6.62132034 22.7463203,7.125 22.125,7.125 L21.228,7.125 L20.7116282,15.0166278 C20.5052616,18.17156 17.8860973,20.625 14.7244229,20.625 L9.27557711,20.625 C6.11390275,20.625 3.49473836,18.17156 3.28837178,15.0166278 L2.772,7.125 L1.875,7.125 C1.25367966,7.125 0.75,6.62132034 0.75,6 C0.75,5.37867966 1.25367966,4.875 1.875,4.875 L22.125,4.875 Z M15,14.25 L9,14.25 C8.58578644,14.25 8.25,14.5857864 8.25,15 C8.25,15.4142136 8.58578644,15.75 9,15.75 L15,15.75 C15.4142136,15.75 15.75,15.4142136 15.75,15 C15.75,14.5857864 15.4142136,14.25 15,14.25 Z" id="形状结合" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </g>
</svg>