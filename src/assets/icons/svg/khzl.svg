<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 37</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页-全展示" transform="translate(-375.000000, -305.000000)">
            <g id="编组-37" transform="translate(375.000000, 305.000000)">
                <rect id="矩形备份-2" fill="#7D98FC" x="0" y="0" width="30" height="30" rx="4"></rect>
                <g id="组件/图标/功能类/快递" transform="translate(3.000000, 3.000000)">
                    <rect id="点击热区" fill="#D8D8D8" opacity="0" x="0" y="0" width="24" height="24"></rect>
                    <path d="M12,1.5 C9.30761184,1.5 7.125,3.68261184 7.125,6.375 C7.125,9.06738816 9.30761184,11.25 12,11.25 C14.6923882,11.25 16.875,9.06738816 16.875,6.375 C16.875,3.68261184 14.6923882,1.5 12,1.5 Z M8.23049901,21.375 L15.769501,21.375 C17.3803556,21.375 18.8438389,20.4373067 19.5170241,18.9738606 L20.1375,17.625 C20.8826523,16.0051037 20.1735322,14.087853 18.5536359,13.3427007 C18.3155018,13.233159 18.0649138,13.1530254 17.8074055,13.1040695 L16.93125,12.9375 C13.6729303,12.3180476 10.3270697,12.3180476 7.06875,12.9375 L6.19259447,13.1040695 C4.44090579,13.4370902 3.29084814,15.127081 3.6238688,16.8787697 C3.67282474,17.1362779 3.75295834,17.386866 3.8625,17.625 L4.48297589,18.9738606 C5.15616106,20.4373067 6.61964441,21.375 8.23049901,21.375 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>