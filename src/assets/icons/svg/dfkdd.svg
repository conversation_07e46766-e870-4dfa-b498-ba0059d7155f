<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 30</title>
    <defs>
        <linearGradient x1="29.4733927%" y1="0%" x2="70.0714029%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3591FD" offset="0%"></stop>
            <stop stop-color="#3667EF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="9.04016821%" y1="5.69809245%" x2="76.4379839%" y2="80.69626%" id="linearGradient-2">
            <stop stop-color="#F5FBFE" stop-opacity="0.755896935" offset="0%"></stop>
            <stop stop-color="#1C72F1" stop-opacity="0.342189549" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页-全展示" transform="translate(-1128.000000, -503.000000)">
            <g id="编组-30" transform="translate(1128.000000, 503.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="48" height="48"></rect>
                <path d="M35.5098039,8 C36.3328165,8 37,8.66718353 37,9.49019612 L37,38.5098039 C37,39.3328165 36.3328165,40 35.5098039,40 L12.4901961,40 C11.6671835,40 11,39.3328165 11,38.5098039 L11,9.49019612 C11,8.66718353 11.6671835,8 12.4901961,8 L35.5098039,8 Z M30.585729,30.6862745 L17.414271,30.6862745 C17.0027647,30.6862745 16.6691729,31.0198663 16.6691729,31.4313725 L16.6691729,32.9215686 C16.6691729,33.3330749 17.0027647,33.6666667 17.414271,33.6666667 L30.585729,33.6666667 C30.9972353,33.6666667 31.3308271,33.3330749 31.3308271,32.9215686 L31.3308271,31.4313725 C31.3308271,31.0198663 30.9972353,30.6862745 30.585729,30.6862745 Z M23.1831364,14.7254902 L19.6015038,14.7254902 L20.0622986,15.2731241 L22.8689581,20.7284002 L19.9575725,20.7284002 L19.6852846,22.7293702 L22.8480129,22.7293702 L22.3034372,26.6470588 L25.4871106,26.6470588 L26.0316864,22.7293702 L29.2153598,22.7293702 L29.4876477,20.7284002 L26.5762621,20.7284002 L31.3308271,14.7254902 L27.9149158,14.7254902 L25.0053706,18.7906187 L23.1831364,14.7254902 Z" id="形状结合" fill="url(#linearGradient-1)" fill-rule="nonzero" opacity="0.949999988"></path>
                <rect id="矩形" stroke="url(#linearGradient-2)" stroke-width="0.5" x="30.75" y="31.75" width="11.5" height="11.5" rx="5.75"></rect>
            </g>
        </g>
    </g>
</svg>