<script setup lang="ts" name="UpImage">
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import FildCard from '@/components/FildCard.vue'
import UploadFile from '@/components/UploadFile/index.vue'

const state = reactive({
  screen: 'test',
  name: '',
})

const uploadFileRef = ref()
function handUpload(row: any) {}
function onUploadImage() {
  if (!state.screen)
    return ElMessage.warning('场景值不能为空')
  uploadFileRef.value.uploadFun()
}
</script>

<template>
  <FildCard :tool-bar="false">
    <div class="mb-[10px]">
      <el-input v-model="state.screen" class="w-[200px] mr-[2px]" placeholder="输入场景值" />
      <el-input v-model="state.name" class="w-[200px] mr-[2px]" placeholder="输入文件名称" />
      <el-button type="primary" @click="onUploadImage">
        上传测试环境
      </el-button>
      <el-button type="primary">
        上传正式环境
      </el-button>
    </div>
    <UploadFile ref="uploadFileRef" :show-submit-btn="false" :scene="state.screen" :file-name="state.name" :auto-upload="false" @on-upload-success="handUpload" />
  </FildCard>
</template>

<style lang="scss" scoped></style>
