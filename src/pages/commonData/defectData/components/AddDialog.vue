<script setup lang="ts">
import { reactive, ref } from 'vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const emits = defineEmits(['handleSure'])

// const checkSpace = (rule: any, value: any, callback: any) => {
//   const reg = /^\S.*$/
//   if (reg.test(value)) {
//     callback()
//   } else {
//     callback(new Error('不允许输入空格'))
//   }
// }

const state = reactive({
  showModal: false,
  modalName: '验布疵点定义',
  form: {
    code: '',
    measurement_unit_id: 0,
    name: '',
    sort: 0,
    remark: '',
    id: -1,
  },
  fromRules: {
    // number: [{ required: true, message: '请填写编号', trigger: 'blur' }, { validator: checkSpace }],
    // name: [{ required: true, message: '请填写名称', trigger: 'blur' }, { validator: checkSpace }],
  },
})

function handCancel() {
  state.showModal = false
}

const ruleFormRef = ref()
async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state.form)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="600" height="400" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" size="default" :model="state.form" label-width="90px" label-position="right" :rules="state.fromRules">
      <el-form-item label="疵点编号：">
        <el-input v-model="state.form.code" />
      </el-form-item>
      <el-form-item label="疵点名称：" prop="name">
        <el-input v-model="state.form.name" />
      </el-form-item>
      <el-form-item label="单位名称：" prop="name">
        <SelectComponents v-model="state.form.measurement_unit_id" style="width: 200px" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable />
      </el-form-item>
      <el-form-item label="排序：" prop="name">
        <el-input v-model="state.form.sort" type="number" />
      </el-form-item>
      <el-form-item label="备注：" prop="remark">
        <el-input v-model="state.form.remark" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
