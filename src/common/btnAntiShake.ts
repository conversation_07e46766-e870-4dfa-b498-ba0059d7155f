import { ElMessage } from 'element-plus'
import { type App, type DirectiveBinding } from 'vue'

// 防抖按钮自定义指令
export default (app: App<Element>) => {
  app.directive('btnAntiShake', {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
      let timer: NodeJS.Timeout | null = null
      el.addEventListener('click', () => {
        const firstClick = !timer

        if (firstClick) {
          binding.value()
        }
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          timer = null
          if (!firstClick) {
            ElMessage.warning('请勿频繁点击')
            // binding.value()
          }
        }, 1000)
      })
    },
  })
}
