import { ElMessage } from 'element-plus'
import { UPLOAD_CDN_URL } from './constant'
import { GetSignApi } from '@/api/cdn'
import api from '@/util/http'

const { fetchData: GetSign, success, data: resData, msg, code } = GetSignApi()

// 上传图片 获取auth，Policy
/*
 scene 场景值，区分上传文件的根路径
 type  类型值，区分上传业务bucket
 name 文件名称
 status: 1 测试， 2正式
*/
export enum UploadWay {
  TEST = 1,
  PROD = 2,
}
function getSecret(scene: string, name: string = '', status = UploadWay.TEST) {
  return new Promise(async (resolve, reject) => {
    // const SAVE_PATH = name ? `/${scene}/${name}` : `/${scene}/{filemd5}{day}{hour}{min}{sec}{.suffix}`
    const SAVE_PATH = `/${scene}/{filemd5}/${name}`

    const params = {
      method: 'post',
      save_key: SAVE_PATH,
      status,
    }

    // 获取签名
    await GetSign(params)
    if (success.value) {
      //
      resolve(resData.value)
    }
    else {
      reject({
        code: code.value || '9999',
        msg: msg.value,
      })
    }
  })
}

// 图片类型
export const imgTypes= ['gif', 'jpeg', 'jpg', 'bmp', 'png', 'jfif']
export const imgTypeAccept= imgTypes.map(item => `.${item}`).join(',')

// 视频类型
export const videoTypes = ['avi', 'wmv','mkv','mp4','mov', 'rm', '3gp', 'flv','mpg', 'rmvb', 'quicktime']
export const videoTypeAccept= videoTypes.map(item => `.${item}`).join(',')

/**
 * 获取文件类型，这里取得地址后缀前几位进行匹配
 * @param name
 * @returns
 */
export function getFileType(name: string) {
  if (!name)
    return false
  const suffixStr = name.slice(((name.lastIndexOf('.') - 1) >>> 0) + 2)
  const imgType = imgTypes
  const videoType = videoTypes
  const officeType = ['xls', 'XLS', 'xlsx', 'XLSX', 'doc', 'DOC', 'docx', 'DOCX', 'pdf', 'PDF', 'PPT', 'PPTX', 'ppt', 'pptx']
  if (RegExp(`^(${imgType.join('|')})`, 'i').test(suffixStr?.toLowerCase()))
    return 'image'
  else if (RegExp(`^(${videoType.join('|')})`, 'i').test(suffixStr?.toLowerCase()))
    return 'video'
  else if (RegExp(`^(${officeType.join('|')})`, 'i').test(suffixStr?.toLowerCase()))
    return 'office'
  else
    return false
}

// 判断文件是否为pdf
export function fileIsPdf(name: string) {
  if (!name)
    return false
  const suffixStr = name.slice(((name.lastIndexOf('.') - 1) >>> 0) + 2)
  const officeType = ['pdf', 'PDF']
  if (RegExp(`^(${officeType.join('|')})`, 'i').test(suffixStr?.toLowerCase()))
    return 'office'
  else
    return false
}

/**
 *
 * @param {*} file 传入文件
 * @param {string} secene 传入 'product'
 * @param name
 * @param {UploadWay} status
 * @returns
 */
function uploadCDNImg(file: any, secene: string = 'product', name: string = '', status = UploadWay.TEST) {
  // var file = event.target.files[0];
  // var filetype = file.type

  return new Promise((resolve: any, reject: any) => {
    const filetype = file.type

    if (!getFileType(file.name)) {
      ElMessage({
        message: '上传文件类型错误',
        type: 'error',
      })
      return false
    }

    getSecret(secene, name, status)
      .then((result: any) => {
        const formdata = new FormData()
        formdata.append('authorization', result.authorization)
        formdata.append('policy', result.policy)
        formdata.append('file', file)
        let message: any
        message = ElMessage({
          message: '上传中...',
          type: 'info',
          duration: 0,
        })
        // ${UPLOAD_CDN_URL}admin/cdn/token
        api({
          baseURL: IS_BUILD_ENV ? `${UPLOAD_CDN_URL}` : `/upyun`,
          url: `/${result.bucket}`,
          method: 'post',
          headers: { 'Content-Type': 'multipart/form-data' },
          data: formdata,
          onUploadProgress: (progress) => {},
          transformRequest: [
            (data, headers) => {
              delete headers.Platform // 删除 platform 头 再请求
              // delete headers.common.Platform // 删除 platform 头 再请求
              return data
            },
          ],
        })
          .then((res) => {
            message.close()
            resolve(res.data)
          })
          .catch((error) => {
            reject(error)
          })
      })
      .catch((result) => {
        reject(result)
        ElMessage({
          message: '获取密钥失败！',
          type: 'error',
        })
      })
  })
}

/**
 *
 * @param {*} file 传入文件
 * @param {string} secene 传入 'product'
 * @param {string} type  传入 'product'
 * @returns
 */
export function uploadCDNFile(file: any, secene: 'product', type: 'product') {
  return new Promise((resolve: any, reject: any) => {
    const filetype = file.type
    let messages: any
    getSecret(secene)
      .then((result: any) => {
        const formdata = new FormData()
        formdata.append('authorization', result.authorization)
        formdata.append('policy', result.policy)
        formdata.append('file', file)
        messages = ElMessage({
          message: '上传中...',
          type: 'info',
          duration: 0,
        })
        api({
          baseURL: IS_BUILD_ENV ? `${UPLOAD_CDN_URL}` : `/upyun`,
          url: `/${result.bucket}`,
          method: 'post',
          headers: { 'Content-Type': 'multipart/form-data' },
          data: formdata,
          onUploadProgress: (progress) => {},
          transformRequest: [
            (data, headers) => {
              delete headers.Platform // 删除 platform 头 再请求
              delete headers.common.Platform // 删除 platform 头 再请求
              return data
            },
          ],
        }).then((res) => {
          messages.close()
          resolve(res.data)
          // resolve(JSON.parse(`${res.data}`))
        })
      })
      .catch((result) => {
        messages.close()
        reject(result)
        ElMessage({
          message: '获取密钥失败！',
          type: 'error',
        })
      })
  })
}

export default uploadCDNImg
