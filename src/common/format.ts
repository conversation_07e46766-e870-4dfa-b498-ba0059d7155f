import dayjs from 'dayjs'
import * as math from 'mathjs'
import currency from 'currency.js'

// import { number } from 'mathjs'
// 正则 匹配 尾部井号
const WellNumberReg = /\#$/

/**
 * 移除井号
 * @param {string} val code 编码
 * @returns
 */
export function formatRemoveHashTag(val = '') {
  //
  return WellNumberReg.test(val) ? val.replace('#', '') : val
}

/**
 * 格式化编码+名称显示方式，若code和name一致则只显示一个
 * @param {string} code 编码
 * @param {string} name 名称
 * @param {*} mode 模式 both:code + 名称 name: 仅显示名称
 * @returns
 */
export function formatHashTag(code = '', name = '', mode = 'both') {
  if (!code && !name)
    return ''
  if (mode === 'both')
    return code === name ? code : `${formatRemoveHashTag(code)}# ${name}`
  else if (mode === 'name')
    return `${name}`
}

/**
 *  格式化时间
 * @param {string} val 时间字符串，为空返回空
 * @returns
 */
export function formatTime(val: dayjs.ConfigType, emptyTips = '') {
  return val === '' || val == null ? emptyTips : dayjs(val).format('YYYY-MM-DD HH:mm:ss')
}

/**
 *  格式化时间
 * @param {string} val 时间字符串，为空返回空
 * @returns
 */
export function formatDate(val: string | number | Date = '', emptyTips = '') {
  return val === '' || val == null ? emptyTips : dayjs(val).format('YYYY-MM-DD')
}
/**
 *  格式化时间 vxe-table专用
 * @param {object} dateObj
 * @param {string} dateObj.cellValue 时间字符串，为空返回空
 * @returns
 */
export function formatTimeWithVXE({ cellValue }: { cellValue: dayjs.ConfigType }) {
  return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : cellValue
}
/**
 *  格式化时间 vxe-table专用
 * @param {object} dateObj
 * @param {string} dateObj.cellValue 时间字符串，为空返回空
 * @returns
 */
export function formatDateWithVXE({ cellValue }: { cellValue: dayjs.ConfigType }) {
  return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : cellValue
}
/**
 *  czm格式化时间（筛选用）
 * @param {Array} val 开始时间和结束时间数组
 * @returns {object} 返回处理后的数组
 */
export function searchFormatDate(val: dayjs.ConfigType[]) {
  const arr = { date_min: '', date_max: '' }
  if (val) {
    arr.date_min = `${dayjs(val[0]).format('YYYY-MM-DD')} 00:00:00`
    arr.date_max = `${dayjs(val[1]).add(1, 'day').format('YYYY-MM-DD')} 00:00:00`
  }
  return arr
}

// 当返回单个时间用
export function searchOneFormatDate(val: dayjs.ConfigType) {
  return `${dayjs(val).format('YYYY-MM-DD')} 00:00:00`
}

const DateAddr = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

/**
 *
 * @param {string} timestamp 时间戳或者Date格式
 * @param {string} str YYYY-MM-dd hh:mm;ss
 * @param {string} lang zh | en  输出中文还是英文
 * @returns
 */
export function formatTimeCustomized(timestamp: number | string | Date, str = 'yyyy-MM-dd hh:mm:ss', lang = 'zh') {
  const currentTime = new Date(timestamp)
  const o = {
    'y+': currentTime.getFullYear(), // 年
    'M+': currentTime.getMonth() + 1,
    'd+': currentTime.getDate(),
    'h+': currentTime.getHours(),
    'm+': currentTime.getMinutes(),
    's+': currentTime.getSeconds(),
  }
  if (lang === 'zh') {
    let item: keyof typeof o
    for (item in o) {
      if (new RegExp(`(${item})`).test(str)) {
        let temp = ''
        if (RegExp.$1.length === 1)
          temp = `${o[item]}`
        else
          temp = (`00${o[item]}`).split('').reverse().slice(0, RegExp.$1.length).reverse().join('')

        str = str.replace(RegExp.$1, temp)
      }
    }
  }
  else if (lang === 'en') {
    let newStr = ''
    let item
    for (item in o) {
      if (new RegExp(`(${item})`).test(str)) {
        let temp = ''
        if (item === 'h+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
        if (item === 'm+') {
          temp = `${o[item]}`
          newStr = `:${newStr}${temp}`
        }
        if (item === 's+') {
          temp = `${o[item]}`
          newStr = `:${newStr}${temp}`
        }
        if (item === 'M+') {
          temp = DateAddr[o[item] - 1]
          newStr = `${newStr + temp} `
        }
        if (item === 'd+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
        if (item === 'y+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
      }
    }
    str = newStr
  }
  return str
}

/**
 * 厘米进退位单位
 */
export const meterDigit = 100

/**
 * 长度进退位单位
 */
export const lengthDigit = 10000

/**
 * 数量 进退位 单位
 */
export const weightDigit = 10000

/**
 * 价格 单位
 */
export const priceDigit = 100
/**
 * 单价
 */
export const unitDigit = 10000

// 匹数
export const rollDigit = 100

/**
 * 格式化价格 (乘以)
 * @param {number} val
 * @returns
 */
export function formatPriceMul(val: string | number, digit = priceDigit) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化价格 (除以)
 * @param {number} val
 * @returns
 */
export function formatPriceDiv(val: number, digit = priceDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化长度 (乘以)
 * @param {number} val
 * @returns
 */
export function formatMeterMul(val: number, digit = meterDigit) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化长度 (除以)
 * @param {*} val
 */
export function formatMeterDiv(val: number, digit = meterDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化单位长度 (乘以) length
 * @param {number} val
 * @returns
 */
export function formatLengthMul(val: number, digit = lengthDigit) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化单位长度  (除以) length
 * @param {*} val
 */
export function formatLengthDiv(val: number, digit = lengthDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化数量单位 (乘以)
 * @param {number} val
 * @returns
 */
export function formatWeightMul(val: number, digit = weightDigit) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化单价单位 (乘以)
 * @param {number} val
 * @returns
 */
export function formatUnitPriceMul(val: number, digit = unitDigit) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化两位小数 (乘以)
 * @param {number} val
 * @returns
 */
export function formatTwoDecimalsMul(val: number, digit = 100) {
  return printFn(Number(val * digit)) || 0
}
/**
 * 格式化两位小数 (除以)
 * @param {number} val
 * @returns
 */
export function formatTwoDecimalsDiv(val: number, digit = 100) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化单价单位 (除以以)
 * @param {number} val
 * @returns
 */
export function formatUnitPriceDiv(val: number, digit = unitDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化数量单位 (除以)
 * @param {*} val
 */
export function formatWeightDiv(val: number, digit = weightDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化匹数 (乘以)
 * @param {number} val
 * @returns
 */
export function formatRollMul(val: string | number, digit = rollDigit) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化匹数 (除以)
 * @param {number} val
 * @returns
 */
export function formatRollDiv(val: number, digit = rollDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化欠磅率单位 (除以)
 * @param {number} val
 * @returns
 */
export function formatUnderweightRateDiv(val: number) {
  return printFn(Number(val / 100)) || 0
}

export const rateDigit = 100 // 税率
/**
 * 格式化税率 (乘以)
 * @param {number} val
 * @returns
 */
export function formatRateMul(val: string | number, digit = rateDigit) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化税率 (除以)
 * @param {number} val
 * @returns
 */
export function formatRateDiv(val: number, digit = rateDigit) {
  return printFn(Number(val / digit)) || 0
}

/**
 * @description 数字转中文数码
 *
 * @param {number | string}   num     数字[正整数]
 * @param {string}          type    文本类型，lower|upper，默认upper
 *
 * @example number2text(*********) => "壹亿元整"
 */

export function number2text(number: number, type: 'lower' | 'upper' | 'decimal' | 'maxNumber' = 'upper') {
  // 配置
  const confs = {
    lower: {
      num: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
      unit: ['', '十', '百', '千', '万'],
      level: ['', '万', '亿'],
    },
    upper: {
      num: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'],
      unit: ['', '拾', '佰', '仟'],
      level: ['', '万', '亿'],
    },
    decimal: {
      unit: ['分', '角'],
    },
    maxNumber: 999999999999.99,
  }

  // 过滤不合法参数
  if (Number(number) > confs.maxNumber) {
    console.error(`The maxNumber is ${confs.maxNumber}. ${number} is bigger than it!`)
    return false
  }

  const conf: any = confs[type]
  const numbers = String(Number(number).toFixed(2)).split('.')
  const integer = numbers[0].split('')
  const decimal = Number(numbers[1]) === 0 ? [] : numbers[1].split('')

  // 四位分级
  const levels = integer.reverse().reduce((pre: any[], item, idx) => {
    const level = pre[0] && pre[0].length < 4 ? pre[0] : []
    const value = item === '0' ? conf.num[item] : conf.num[item] + conf.unit[idx % 4]
    level.unshift(value)

    if (level.length === 1)
      pre.unshift(level)
    else
      pre[0] = level

    return pre
  }, [])

  // 整数部分
  const _integer = levels.reduce((pre, item, idx) => {
    let _level: any = conf.level[levels.length - idx - 1]
    let _item = item.join('').replace(/(零)\1+/g, '$1') // 连续多个零字的部分设置为单个零字

    // 如果这一级只有一个零字，则去掉这级
    if (_item === '零') {
      _item = ''
      _level = ''

      // 否则如果末尾为零字，则去掉这个零字
    }
    else if (_item[_item.length - 1] === '零') {
      _item = _item.slice(0, _item.length - 1)
    }

    return pre + _item + _level
  }, '')

  // 小数部分
  let _decimal = decimal
    .map((item, idx) => {
      const unit = confs.decimal.unit
      const _unit = item !== '0' ? unit[unit.length - idx - 1] : ''

      return `${conf.num[item]}${_unit}`
    })
    .join('')

  // 如果小数最后一项是零，就去掉
  if (_decimal[_decimal.length - 1] === '零')
    _decimal = _decimal.slice(0, _decimal.length - 1)

  // 如果是整数，则补个整字
  return `${_integer}元${_decimal || '整'}`
}

// 格式化取整
export function printFn(val: number) {
  const precision = 14
  return Number(math.format(val, precision))
}

// 合计
export function sumNum(list: any[] = [], field = '') {
  let count = currency(0)

  if (list?.length === 0 || !list)
    return 0
  let currentValue
  for (let i = 0, ls = list.length - 1; i <= ls; i++) {
    currentValue = list[i][field]
    if (!currentValue)
      continue

    count = count.add(currentValue)
  }
  return count.value
}

// 求和
export function sumTotal(arr = [], keyName = '') {
  const sum = arr.reduce((acc, cur: any) => {
    const num = Number.parseFloat(cur[keyName])
    if (!Number.isNaN(num))
      return acc + num
    else
      return acc
  }, 0)
  return sum
}

export function formatBoolean(val: number) {
  if (val !== null)
    return val === 1 ? '是' : '否'
  else
    return ''
}

// 格式化日期时间
export function formatDateTime({ cellValue, format = 'YYYY-MM-DD HH:mm:ss' }: any) {
  return cellValue ? dayjs(cellValue).format(format) : cellValue
}

/**
 * 用于table组件
 * 年月日转换:
 * @param {number} val
 * @returns
 */
export function formatDateTable({ cellValue }: any) {
  //
  return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : cellValue
}

/**
 * 用于table组件
 * 格式化价格 (除以):
 * @param {number} val
 * @param {string} prefix 前缀
 * @returns
 */
export function formatPriceDivTable({ cellValue }: any, digit = priceDigit, prefix = '') {
  return `${prefix}${printFn(Number(cellValue / digit)) || 0}`
}

/**
 * 用于table组件
 * 格式化匹数 (除以):
 * @param {number} val
 * @returns
 */
export function formatRollDivTable({ cellValue }: any, digit = rollDigit) {
  return printFn(Number(cellValue / digit)) || 0
}

/**
 * 用于table组件
 * 格式化数量单位 (除以)
 * @param {*} val
 */
export function formatWeightDivTable({ cellValue }: any, digit = weightDigit) {
  return printFn(Number(cellValue / digit)) || 0
}

/**
 * 用于table组件
 * 格式化单价单位 (除以)
 * @param {*} val
 * @param {string} prefix 前缀
 */
export function formatUnitPriceDivTable({ cellValue }: any, digit = unitDigit, prefix = '') {
  return `${prefix}${printFn(Number(cellValue / digit)) || 0}`
}

/**
 * 用于table组件
 * 格式化长度单位 (除以)
 * @param {*} val
 */
export function formatLengthDivTable({ cellValue }: any, digit = lengthDigit) {
  return printFn(Number(cellValue / digit)) || 0
}

/**
 * 用于table组件
 * 格式化单价单位 (除以)
 * @param {*} val
 */
export function formatUnderweightRateDivTable({ cellValue }: any) {
  return printFn(Number(cellValue / 100)) || 0
}

/**
 * 格式化数据显示，常用于价格显示
 * @param {number} value 值
 * @param {boolean} showZero 是否显示0
 * @param {string} suffix 后缀
 * @param {string} prefix 前缀 *
 * @returns prefix=‘￥’传入 10120.10 返回 ￥10,120.1
 */
export function formatPriceSymbol({
  value = 0,
  showZero = false,
  suffix = '',
  prefix = '',
}: {
  value: number
  prefix?: string
  suffix?: string
  showZero?: boolean
}) {
  if (value === 0) {
    return showZero ? `${value}` : ''
  }
  else {
    // const absVal = Math.abs(value)
    // return value > 0 ? `${prefix}${absVal}${suffix}` : `-${prefix}${absVal}${suffix}`
    let formatValue = currency(value, { symbol: prefix, precision: 2 }).format() // 格式化
    formatValue = formatValue.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '') // 去除小数点后面的0
    if (suffix)
      formatValue += suffix

    return formatValue
  }
}

// 合并单位-结算数量
export function mergeWeightUnit(list: any, valueKey = 'weight', unitKey = 'measurement_unit_name') {
  const mergeWeightInfoMap = list.reduce((acc: any, curr: any) => {
    acc[curr[unitKey]] = !(curr[unitKey] in acc)
      ? curr[valueKey]
      : currency(acc[curr[unitKey]]).add(curr[valueKey]).value
    return acc
  }, {})
  return Object.entries(mergeWeightInfoMap).reduce((acc, [key, value]) => {
    const spaceStr = acc === '' ? '' : '、'
    // 去除单位默认值"-"
    key = key.replace(/-/g, '')
    acc += value === 0 && !key ? '' : `${spaceStr}${value}${key}`
    return acc
  }, '')
}
/**
 * 现主要用作价格计算
 * 将数字向上进位到指定小数位数-现目前处理逻辑
 * @param {number} num 要处理的数字
 * @param {number} decimal 保留的小数位数
 * @returns {number} 处理后的数字
 */
export function formatCalculate(num: number, decimal: number = 2): number {
  // 处理精度丢失
  num = printFn(num)
  // 获取当前数字的小数部分字符串
  const decimalPart = String(num).split('.')[1] || ''

  // 如果小数位数不超过指定位数，直接返回原数字
  if (decimalPart.length <= decimal)
    return num

  const multiplier = 10 ** decimal
  return Math.ceil(num * multiplier) / multiplier
}
/**
 * 格式化数字显示前缀+/-,保留两位小数
 * @param {number} num 要格式化的数字
 * @param {decimal} num 保留的小数位数
 * @returns {string} 格式化后的字符串
 */
export function formatNumberPrefix(num: number | string = 0, decimal = 2): string {
  const newNum = Number(num)
  if (newNum > 0)
    return `+${newNum.toFixed(decimal)}`

  else if (newNum < 0)
    return `${newNum.toFixed(decimal)}`

  else
    return '0'
}
