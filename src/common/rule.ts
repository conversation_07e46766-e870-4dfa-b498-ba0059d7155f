// 手机号校验
export const PHONE_REGEXP = /^1[3456789]\d{9}$/
// 传真号、座机号校验
export const FAX_REGEXP = /^(\d{3,4}-)?\d{7,8}$/
type Scenes = 'phone' | 'email' | 'name' | 'checkSpace'
// 正则验证
export function verification({ scenes, val }: { scenes: Scenes, val: string }) {
  const list = {
    phone: /^1[3456789]\d{9}$/, // 手机号码
    email: /^[A-Za-z0-9\u4E00-\u9FA5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, // 邮箱地址
    name: /^[\u4E00-\u9FA5A-Za-z0-9]+$/, // 用户名
    checkSpace: /^\S.*$/, // 检查空格
  }
  if (scenes && val)
    return list[scenes].test(val)

  return false
}

// form 验证
export const formValidatePass = {
  phone: (msg = '格式不正确') => {
    return (rule: any, value: any, callback: any) => {
      if (!value)
        return callback()
      if (!verification({ scenes: 'phone', val: value }))
        callback(new Error(msg))
      else
        callback()
    }
  },
  // 判断两时间范围
  timeRange: (start_time: string, end_time: string, msg: string) => {
    return (rule: any, value: any, callback: any) => {
      if (!start_time || !end_time)
        return callback()
      if (Date.parse(start_time) > Date.parse(end_time))
        callback(new Error(msg))
      else
        callback()
    }
  },
  sale_system_id: (msg = '请选择营销系统') => {
    return (rule: any, value: any, callback: any) => {
      if (!Number(value))
        callback(new Error(msg))
      else
        callback()
    }
  },
  /**
   * id 校验， 考虑到部分枚举的值可以为0
   * @param zeroIsPass 0是否检验通过，默认false，true:通过,false:不通过,
   */
  id: (zeroIsPass = false) => {
    return (rule: any, value: any, callback: any) => {
      const isZero = Number(value) === 0
      const isEmpty = !value || !Number(value)
      if (zeroIsPass && isEmpty && !isZero)
        callback(new Error('请选择'))
      else if (!zeroIsPass && isEmpty)
        callback(new Error('请选择'))
      else
        callback()
    }
  },
}
