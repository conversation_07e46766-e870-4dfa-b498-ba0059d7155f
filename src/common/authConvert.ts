const AuthList = [
  // {
  //   authName: '客户名称',
  //   key: 'decryption_purchaser_name',
  // },
  // {
  //   authName: '客户联系人',
  //   key: 'decryption_purchaser_contact',
  // },
  // {
  //   authName: '客户联系地址',
  //   key: 'decryption_purchaser_address',
  // },
  // {
  //   authName: '客户联系电话',
  //   key: 'decryption_purchaser_phone',
  // },
  // {
  //   authName: '客户收货信息',
  //   key: 'decryption_purchaser_receipt',
  // },
  {
    authName: '供应商名称',
    key: 'decryption_supplier_name',
  },
  // {
  //   authName: '供应商联系人',
  //   key: 'decryption_supplier_legal_person',
  // },
  // {
  //   authName: '供应商联系电话',
  //   key: 'decryption_supplier_phone',
  // },
  // {
  //   authName: '供应商地址',
  //   key: 'decryption_supplier_address',
  // },
  // {
  //   authName: '毛利',
  //   key: 'decryption_gross_profit',
  // },
  // {
  //   authName: '成本',
  //   key: 'decryption_cost_price',
  // },
  // {
  //   authName: '采购价',
  //   key: 'decryption_purchaser_price',
  // },
  // {
  //   authName: '销售价',
  //   key: 'decryption_sale_price',
  // },
]

function extractValues<T, K extends keyof T>(objects: T[], keyName: K): T[K][] {
  return objects.map(obj => obj[keyName])
}

const authNames = extractValues(AuthList, 'authName')

/**
 * 预处理名称，对名字的处理放到这里做
 * @param name
 */
function namePreprocessing(name: string): string {
  // 去掉最后面的:或者：
  name = name.replace(/([:：])$/g, '')

  if (name === '供应商' || name === '客户')
    return `${name}名称`

  return name
}

function getAuthKeyByAuthName(authName: string): string | undefined {
  return AuthList.find(auth => auth.authName === authName)?.key
}

function isHasAuth(authName: string, data_access_scope: string[] | null): boolean {
  if (data_access_scope == null)
    return false

  authName = namePreprocessing(authName)

  const hasAuth = authNames.includes(authName)
  if (!hasAuth) {
    console.error(`权限名称 ${authName} 匹配失败，请检查是否匹配规则`)
    return false
  }

  const authKey = getAuthKeyByAuthName(authName)
  if (!authKey) {
    console.error(`权限value: ${authName} 匹配失败，请检查是否匹配规则`)
    return false
  }

  return data_access_scope.includes(authKey)
}

export { AuthList, isHasAuth }
