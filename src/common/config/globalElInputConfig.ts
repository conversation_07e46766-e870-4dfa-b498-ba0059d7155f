import { ElInput } from 'element-plus'
import { INPUT_PLACEHOLDER, INPUT_SHOW_WHEEL } from './index'
// 定义全局的 el-input 配置
const globalElInputConfig = {
  props: {
    placeholder: {
      default: INPUT_PLACEHOLDER,
    },
  },
}

// 应用全局配置
ElInput.props = {
  ...ElInput.props,
  ...globalElInputConfig.props,
}

// 禁止数字输入框的滚轮事件
function hideInputWheel() {
  const inputMount = ElInput.mounted
  ElInput.mounted = function () {
    inputMount?.()
    // 禁止数字输入框的滚轮事件
    if (this.$el && this.type === 'number') {
      const input = this.$el.querySelector('input')
      input.addEventListener('wheel', (e: any) => {
        e.preventDefault()
      })

      if (input) {
      // 往 input 元素添加类名
        input.classList.add('hide-input-wheel')
      }
    }
  }
}

if (!INPUT_SHOW_WHEEL)
  hideInputWheel()
