import { useDictionaryStore } from '@/pinia/modules/dictionary'
//  获取字典方法 使用示例 getDict('sex').then(res)  或者 async函数下 const res = await getDict('sex')
export const getDict = async (type: any) => {
  const dictionaryStore = useDictionaryStore()
  await dictionaryStore.getDictionary(type)
  return dictionaryStore.dictionaryMap[type]
}

//  字典文字展示方法
export const showDictLabel = (dict: any[], code: string) => {
  if (!dict) {
    return ''
  }
  const dictMap: any = {}
  dict.forEach(item => {
    dictMap[item.value] = item.label
  })
  return dictMap[code]
}
