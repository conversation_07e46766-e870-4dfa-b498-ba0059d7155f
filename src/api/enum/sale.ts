import { useRequest } from '@/use/useRequest'

// 获取退回供方列表
export function GetReturnSupplierTypeReverseIntMap() {
  return useRequest({
    url: '/admin/v1/sale/enum/getReturnSupplierTypeReverseIntMap',
    method: 'get',
  })
}
// 获取退回供方列表
export function GetDNFTypeEnum() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/dnf_type_enum',
    method: 'get',
  })
}
// 获取退回供方列表
export function GetOrderTypeEnum() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/order_type_enum',
    method: 'get',
  })
}
// 获取退回供方列表
export function GetSrcTypeEnum() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/src_type_enum',
    method: 'get',
  })
}
