import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取成品报表的成品汇总报表 http://192.168.1.24:50001/hcscm/admin/v1/fpmReportForms/getSummaryReportList?start_date=2024-11-01&end_date=2024-11-09
export function getSummaryReportList() {
  return useRequest({
    url: '/admin/v1/fpmReportForms/getSummaryReportList',
    method: 'get',
  })
}

export function getSummaryReportListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/fpmReportForms/getSummaryReportList',
    method: 'get',
    nameFile,
  })
}

// 获取成品报表的成品颜色汇总 /hcscm/admin/v1/fpmReportForms/getFinishProductReportList
export function GetFinishProductReportList() {
  return useRequest({
    url: '/admin/v1/fpmReportForms/getFinishProductReportList',
    method: 'get',
  })
}

// 获取成品报表的成品颜色汇总 - 导出
export function GetFinishProductReportListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/fpmReportForms/getFinishProductReportList',
    method: 'get',
    nameFile,
  })
}

// 获取成品报表的成品缸号汇总 /hcscm/admin/v1/fpmReportForms/getFinishProductDetailsReportList
export function GetFinishProductDetailsReportList() {
  return useRequest({
    url: '/admin/v1/fpmReportForms/getFinishProductDetailsReportList',
    method: 'get',
  })
}

// 获取成品细码报表 - 导出
export function GetFinishProductDetailsReportListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/fpmReportForms/getFinishProductDetailsReportList',
    method: 'get',
    nameFile,
  })
}

// 获取成品细码报表
export function getDetailReportList() {
  return useRequest({
    url: '/admin/v1/fpmReportForms/getFinishProductItemFcReportList',
    method: 'get',
  })
}

// 成品成本核算列表
export function getCostCalculationFpmList() {
  return useRequest({
    url: '/admin/v1/payable/costCalculation/fpmList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
export function getCostCalculationFpmListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/payable/costCalculation/fpmList',
    method: 'get',
    nameFile,
    nameFileTime: false,
  })
}
// 成品成本核算详情
export function getCostCalculationDetail() {
  return useRequest({
    url: '/admin/v1/payable/costCalculation/detail',
    method: 'get',
  })
}
// 成品销售成本详情
export function getShouldCollectOrderCalculationList() {
  return useRequest<Api.GetShouldCollectOrderCalculationList.Request, Api.GetShouldCollectOrderCalculationList.Response>({
    url: '/admin/v1/should_collect_order/calculation/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export function ExportShouldCollectOrderCalculationList({ nameFile }: any) {
  return useDownLoad<any>({
    url: '/admin/v1/should_collect_order/calculation/getList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}
