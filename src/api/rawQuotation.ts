import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const raw_matl_quotelist = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const dyeinglistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_matl_quote/dyeing/list',
    method: 'get',
    nameFile,
  })
}

// 新增
export const raw_matl_quotedyeing = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing',
    method: 'post',
  })
}

// 编辑
export const raw_matl_quotedyeingput = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing',
    method: 'put',
  })
}

// 详情
export const raw_matl_quotedyeingdetail = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/detail',
    method: 'get',
  })
}

// 审核
export const dyeingpass = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/pass',
    method: 'put',
  })
}

// 消审
export const dyeingcancel = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/cancel',
    method: 'put',
  })
}

// 作废
export const dyeingvoid = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/void',
    method: 'put',
  })
}

// 驳回
export const dyeingreject = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/reject',
    method: 'put',
  })
}

// 获取调价单items信息
export const raw_matl_quotedetail_items = () => {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/detail_items',
    method: 'get',
  })
}
