import type { RequestOptions } from '@/use/useRequest'
import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 发票抬头
export function GetInfoPurchaseInvoiceHeaderListUseByOther() {
  return useRequest({
    url: `/admin/v1/info_basic_data/infoPurchaseInvoiceHeader/getInfoPurchaseInvoiceHeaderListUseByOther`,
    method: 'get',
  })
}

// 添加坯布采购
export function AddPurchaseGreyFarbric() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/addPurchaseGreyFarbric`,
    method: 'post',
  })
}

// 编辑坯布采购
export function UpdatePurchaseGreyFarbric() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbric`,
    method: 'put',
  })
}

// 坯布采购列表
export function GetPurchaseGreyFabricItemList() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/getItemAddrEnumList`,
    method: 'get',
  })
}

// 坯布采购列表
export function GetPurchaseGreyFarbricList() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/getPurchaseGreyFarbricList`,
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 坯布采购列表--导出
export function GetPurchaseGreyFarbricList_epxort({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/purchase/purchaseGreyFarbric/getPurchaseGreyFabricForOutPut',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 获取坯布采购
export function GetPurchaseGreyFarbricById() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/getPurchaseGreyFarbric`,
    method: 'get',
  })
}

// 采购订单状态
export function GetAuditStatusEnum() {
  return useRequest({
    url: `/admin/v1/purchase/enum/getAuditStatusEnum`,
    method: 'get',
  })
}

// 修改订单状态
export function UpdatePurchaseGreyFarbricStatus() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbricStatus`,
    method: 'put',
  })
}

// 客户列表枚举
export function GetCustomerEnumList(config: RequestOptions) {
  return useRequest({
    url: `/admin/v1/business_unit/customer/enum_list`,
    method: 'get',
    pagination: true,
    ...config,
  })
}

// 作废
export function UpdateGFMPurchaseReceiveStatusCancel() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbricStatusCancel`,
    method: 'put',
  })
}
// 更新坯布采购业务状态
export function UpdateGFMPurchaseReceiveBusinessClose() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updateBusinessClose`,
    method: 'put',
  })
}

// 审核
export function UpdateGFMPurchaseReceiveStatusPass() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbricStatusPass`,
    method: 'put',
  })
}

// 驳回
export function UpdateGFMPurchaseReceiveStatusReject() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbricStatusReject`,
    method: 'put',
  })
}

// 消审
export function UpdateGFMPurchaseReceiveStatusWait() {
  return useRequest({
    url: `/admin/v1/purchase/purchaseGreyFarbric/updatePurchaseGreyFarbricStatusWait`,
    method: 'put',
  })
}
