import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 成品采购订单列表
export function FinishProductList() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/list`,
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 成品采购订单列表--导出
export function FinishProductList_export({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/purchase_order/finish_product/outPut',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 成品采购订单详情
export function FinishProductDetail() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/detail`,
    method: 'get',
  })
}

// 成品采购订单编辑
export function FinishProductEdit() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product`,
    method: 'put',
  })
}

// 成品采购订单添加
export function FinishProductAdd() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product`,
    method: 'post',
  })
}

// 成品采购订单消审
export function FinishProductCancel() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/cancel`,
    method: 'put',
  })
}

// 成品采购订单审核
export function FinishProductPass() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/pass`,
    method: 'put',
  })
}

// 成品采购订单驳回
export function FinishProductReject() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/reject`,
    method: 'put',
  })
}

// 成品采购订单作废
export function FinishProductVoid() {
  return useRequest({
    url: `/admin/v1/purchase_order/finish_product/void`,
    method: 'put',
  })
}

// 成品采购业务状态
export function FinishProductBusinessClose() {
  return useRequest({
    url: '/admin/v1/purchase_order/finish_product/business_close',
    method: 'put',
  })
}
