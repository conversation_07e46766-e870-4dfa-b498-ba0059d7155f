import { useRequest } from '@/use/useRequest'

// 获取列表
export const getGreyFabricInfoList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表
export const GetGreyFabricInfoListUseByOthers = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoListUseByOthers',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表-枚举
export const GetGreyFabricInfoListUseByOthersMenu = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoListUseByOthers',
    method: 'get',
  })
}

// 新增数据

export const addGreyFabricInfo = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/addGreyFabricInfo',
    method: 'post',
  })
}

// 更新状态
export const updateGreyFabricInfoStatus = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/updateGreyFabricInfoStatus',
    method: 'put',
  })
}

// 删除数据
export const deleteGreyFabricInfo = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/deleteGreyFabricInfo',
    method: 'delete',
  })
}

// 织造工艺枚举
export const getWeavingProcess = () => {
  return useRequest({
    url: '/admin/v1/basic_data/enum/getWeavingProcess',
    method: 'get',
  })
}

// 详情

export const getGreyFabricInfoDetail = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfo',
    method: 'get',
  })
}

// 更新数据

export const updateGreyFabricInfo = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/updateGreyFabricInfo',
    method: 'put',
  })
}
