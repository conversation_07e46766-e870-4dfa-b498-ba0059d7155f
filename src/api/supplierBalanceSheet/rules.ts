export interface GetSupplierBalanceSheetListData {
  id: number
  createTime: string
  updateTime: string
  creatorId: number
  createUserName: string
  updaterId: number
  updateUserName: string
  saleSystemId: number
  saleSystemName: string
  supplierId: number
  supplierCode: string
  supplierName: string
  bizUnitTypeName: string
  orderFollowerId: number
  orderFollowerName: string
  period: number
  shouldPayPrice: number
  payPrice: number
  discountMoney: number
  chargebackMoney: number
  unpayPrice: number
  writeOffPrice: number
  balancePrice: number
  lastAdvancePrice: number
  advancePrice: number
  advanceUsedPrice: number
  balanceAdvancePrice: number
  endPeriod: number
  settleType: number
  settleTypeName: string
  customCycle: number
  creditLimit: number
  payStatus: number
  payStatusName: string
}

export interface GetPayStatus {
  id: number
  code: string
  name: string
  default: boolean
}
