import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function dyeing_fabriclist() {
  return useRequest({
    url: '/admin/v1/stock/dyeing_fabric/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function dyeing_fabriclist_export({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/stock/dyeing_fabric/list',
    method: 'get',
    nameFile,
  })
}

// 未染坯布库存列表
export function getGfmWarehouseSummaryWaitDyeList() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryWaitDyeList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 未染坯布库存列表--导出
export function getGfmWarehouseSummaryWaitDyeListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryWaitDyeList',
    method: 'get',
    nameFile,
  })
}

// 在染坯布库存列表
export function getGfmWarehouseSummaryDyeingList() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryDyeingList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 在染坯布库存列表--导出
export function getGfmWarehouseSummaryDyeingListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryDyeingList',
    method: 'get',
    nameFile,
  })
}

// 坯布总库存列表
export function GetGfmWarehouseSummaryList() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 坯布总库存列表--导出
export function GetGfmWarehouseSummaryListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryList',
    method: 'get',
    nameFile,
  })
}

// 未染坯布台账
export function getGfmStockRecordList() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockRecord/getGfmStockRecordList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 未染坯布台账--导出
export function getGfmStockRecordListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmStockRecord/getGfmStockRecordList',
    method: 'get',
    nameFile,
  })
}

// 查看修改记录
export function getGfmStockRecordListByWarehouseSumID() {
  return useRequest({
    url: '/admin/v1/costPriceRecord/list',
    method: 'get',
  })
}

// 修改修改记录
export function updateGfmWarehouseSummaryWarehouseRemark() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/updateGfmWarehouseSummaryWarehouseRemark',
    method: 'put',
  })
}

// 新增并审核在染坯布库存盘点单
export function AddPassDyingGfmStockCheckOrder() {
  return useRequest<Api.AddPassDyingGfmStockCheckOrder.Request, Api.AddPassDyingGfmStockCheckOrder.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/addPassDyingGfmStockCheckOrder',
    method: 'post',
  })
}

// 在染坯布库存
export function GetGfmWarehouseSummaryDyeingListEnum() {
  return useRequest<Api.GetGfmWarehouseSummaryDyeingListEnum.Request, Api.GetGfmWarehouseSummaryDyeingListEnum.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryDyeingListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 单据类型
export function GetGfmCheckOrderTypeEnum() {
  return useRequest<Api.GetGfmCheckOrderTypeEnum.Request, Api.GetGfmCheckOrderTypeEnum.Response>({
    url: '/admin/v1/grey_fabric_manage/enum/getGfmCheckOrderTypeEnum',
    method: 'get',
  })
}

// 单据类型
export function AddRmCostPrice() {
  return useRequest({
    url: '/admin/v1/stock/rmCostPrice',
    method: 'post',
  })
}
// 单据类型
export function AddGfmCostPrice() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmCostPrice',
    method: 'post',
  })
}
// 单据类型
export function EditGfmCostPrice() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmCostPrice',
    method: 'put',
  })
}
