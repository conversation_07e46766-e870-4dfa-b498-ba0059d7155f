import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export const getSaleProductPlanOrderList = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取合并列表
export const getSaleProductPlanOrderAndDetailList = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrderAndDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getSaleProductPlanOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/sale/saleProductPlanOrder/exportMergeList',
    method: 'get',
    nameFile,
  })
}

// 新增数据
export const addSaleProductPlanOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/addSaleProductPlanOrder',
    method: 'post',
  })
}

// 更新数据
export const updateSaleProductPlanOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/updateSaleProductPlanOrder',
    method: 'put',
  })
}

// 详情
export const getSaleProductPlanOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrder',
    method: 'get',
  })
}

// 审核
export const updateSaleProductPlanOrderAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/updateSaleProductPlanOrderAuditStatusPass',
    method: 'put',
  })
}

// 消审
export const updateSaleProductPlanOrderAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/updateSaleProductPlanOrderAuditStatusWait',
    method: 'put',
  })
}

// 作废
export const updateSaleProductPlanOrderAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/updateSaleProductPlanOrderAuditStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateSaleProductPlanOrderAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/updateSaleProductPlanOrderAuditStatusReject',
    method: 'put',
  })
}

// 从资料中添加(分页)
export const getFinishProductDropdownListPage = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getFinishProductDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export const getFinishProductColorDropdownList = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColorDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 下推
export const SaleProductPlanOrderProductPushDown = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/saleProductPlanOrderProductPush',
    method: 'put',
  })
}
// 下推修改进度状态
export const UpdatePushStatus = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/UpdatePushStatus',
    method: 'put',
  })
}
