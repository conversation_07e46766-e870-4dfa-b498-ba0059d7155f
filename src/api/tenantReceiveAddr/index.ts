import { useRequest } from '@/use/useRequest'

// 获取账套用户收货地址表列表
export const GetTenantReceiveAddrList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/tenantReceiveAddr/list_enum',
    method: 'get',
    pagination: true,
    pageSize: 81,
  })
}

/**
 * 添加账套用户收货地址表
 */
export const AddTenantReceiveAddr = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/tenantReceiveAddr',
    method: 'post',
  })
}

/**
 * 更新账套用户收货地址表
 */
export const UpdateTenantReceiveAddr = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/tenantReceiveAddr',
    method: 'put',
  })
}

/**
 * 获取账套用户收货地址表
 */
export const GeTenantReceiveAddr = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/tenantReceiveAddr',
    method: 'get',
  })
}
