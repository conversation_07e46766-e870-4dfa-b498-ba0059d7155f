import { useRequest } from '@/use/useRequest'

// 获取库存列表
export function getStockProductDetailDropdownList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDetailDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 获取库存列表
// export const getStockProductDyelotNumberDetailList = () => {
//   return useRequest({
//     url: '/admin/v1/product/stockProduct/getStockProductDyelotNumberDetailList',
//     method: 'get',
//     pagination: true,
//     pageSize: 50,
//   })
// }

// 获取疵点列表
export function getInfoBasicDefectlistEnum() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect/listEnum',
    method: 'get',
  })
}

// 获取质检信息
export function getFpmQualityCheckList() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck/list',
    method: 'get',
    pagination: true,
  })
}

// 获取质检疵点表格
export function getFpmQualityCheckDefectList() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheckDefect/pid',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 增加质检信息
export function addFpmQualityCheck() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck',
    method: 'post',
  })
}

// 更新质检信息
export function updateFpmQualityCheck() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck',
    method: 'put',
  })
}

// 更新质检疵点信息
export function updateFpmQualityCheckDefect() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheckDefect',
    method: 'put',
  })
}

// 删除质检疵点信息
export function deleteFpmQualityCheckDefect() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheckDefect',
    method: 'delete',
  })
}

// 扫码获取库存信息
export function getDetailByCond() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getDetailByCond',
    method: 'get',
  })
}

// 根据库存获取质检信息
export function GetFpmQualityCheckByStockId() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck/getFpmQualityCheckByStockId',
    method: 'get',
  })
}

// 添加或更新质检信息
export function AddOrUpdateFpmQualityCheck() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck/addOrUpdateFpmQualityCheck',
    method: 'post',
  })
}
