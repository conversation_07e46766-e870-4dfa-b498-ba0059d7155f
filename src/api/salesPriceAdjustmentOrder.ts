import { useRequest } from '@/use/useRequest'

// 获取列表
export const GetSalePriceAdjustOrderList = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/getSalePriceAdjustOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取需要调整
export const GetIdDataList = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceColorKind/getIdDataList',
    method: 'post',
  })
}

// 新增销售调价单
export const AddSalePriceAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/addSalePriceAdjustOrder',
    method: 'post',
  })
}

// 详情
export const GetSalePriceAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/getSalePriceAdjustOrder',
    method: 'get',
  })
}

// 编辑
export const UpdateSalePriceAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/updateSalePriceAdjustOrder',
    method: 'put',
  })
}

// 作废
export const UpdateSalePriceAdjustOrderAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/updateSalePriceAdjustOrderAuditStatusCancel',
    method: 'put',
  })
}

// 审核
export const UpdateSalePriceAdjustOrderAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/updateSalePriceAdjustOrderAuditStatusPass',
    method: 'put',
  })
}

// 驳回
export const UpdateSalePriceAdjustOrderAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/updateSalePriceAdjustOrderAuditStatusReject',
    method: 'put',
  })
}

// 消审
export const UpdateSalePriceAdjustOrderAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceAdjustOrder/updateSalePriceAdjustOrderAuditStatusWait',
    method: 'put',
  })
}
