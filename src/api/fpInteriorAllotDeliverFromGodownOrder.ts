import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmInternalAllocateOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/getFpmInternalAllocateOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取物流公司枚举
export const GetInfoSaleLogisticsCompanyEnumList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/getInfoSaleLogisticsCompanyEnumList',
    method: 'get',
  })
}

// 添加
export const addFpmInternalAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/addFpmInternalAllocateOutOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmInternalAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/getFpmInternalAllocateOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmInternalAllocateOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/updateFpmInternalAllocateOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmInternalAllocateOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/updateFpmInternalAllocateOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmInternalAllocateOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/updateFpmInternalAllocateOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmInternalAllocateOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/updateFpmInternalAllocateOutOrderStatusWait',
    method: 'put',
  })
}

// 更新成品采购退货出仓单
export const updateFpmInternalAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateOutOrder/updateFpmInternalAllocateOutOrder',
    method: 'put',
  })
}
