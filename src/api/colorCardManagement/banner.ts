/*
 * @Description:电子色卡服务-轮播图
 */
import { useRequest } from '@/use/useRequest'

// 获取轮播图列表
export function GetCarouselBannerList() {
  return useRequest({
    url: '/admin/v1/carouselBanner/list',
    method: 'get',
  })
}
// 获取轮播图
export function GetCarouselBanner() {
  return useRequest({
    url: '/admin/v1/carouselBanner',
    method: 'get',
  })
}

// 获取轮播图跳转类型
export function GetCarouselBannerJumpType() {
  return useRequest({
    url: '/admin/v1/carouselBanner/jumpType',
    method: 'get',
  })
}

// 更新轮播图
export function UpdateCarouselBanner() {
  return useRequest<Api.ColorCard.BannerItem, any>({
    url: '/admin/v1/carouselBanner',
    method: 'put',
  })
}

// 新建轮播图
export function AddCarouselBanner() {
  return useRequest({
    url: '/admin/v1/carouselBanner',
    method: 'post',
  })
}
// 删除轮播图
export function DeleteCarouselBanner() {
  return useRequest({
    url: '/admin/v1/carouselBanner',
    method: 'delete',
  })
}

// 批量更新轮播图状态
export function UpdatecarouselBannerStatus() {
  return useRequest({
    url: '/admin/v1/carouselBanner/status',
    method: 'put',
  })
}

// 获取轮播图状态-枚举
export function GetCarouselBannerStatus() {
  return useRequest({
    url: '/admin/v1/carouselBanner/getCarouselBannerStatus',
    method: 'get',
  })
}
