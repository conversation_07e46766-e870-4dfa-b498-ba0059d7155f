/*
 * @Description:电子色卡服务-商家信息
 */
import { useRequest } from '@/use/useRequest'

// 获取商户基本信息
export function GetMerchantInfo() {
  return useRequest<any, Api.ColorCard.MerchantInfo>({
    url: '/admin/v1/merchantInfo',
    method: 'get',
  })
}
// 添加或更新商户基本信息
export function UpdateMerchantInfo() {
  return useRequest({
    url: '/admin/v1/merchantInfo',
    method: 'post',
  })
}
// 获取到期时间
export function GetServiceExpire() {
  return useRequest({
    url: '/admin/v1/merchantInfo/getServiceExpire',
    method: 'get',
  })
}
// 获取企业类型标签
export function GetMerchantInfoType() {
  return useRequest({
    url: '/admin/v1/merchantInfo/type',
    method: 'get',
  })
}

// 生成加密的账套id
export function GetGenerateAccountSetId() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/generateAccountSetId',
    method: 'get',
  })
}
