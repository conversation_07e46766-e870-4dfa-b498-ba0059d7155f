declare namespace Api.ColorCard {
  /**
   * carouselBanner.AddCarouselBannersParam
   */
  export interface BannerItem {
  /**
   * 创建时间
   */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 图片
     */
    img_url?: string
    /**
     * 跳转类型
     */
    jump_type?: number
    /**
     * 链接
     */
    link?: string
    /**
     * 预览图
     */
    prev_view_url?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 排序
     */
    sort?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 目标ID
     */
    target_id?: number
    /**
     * 标题
     */
    title?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 视频
     */
    video_url?: string
    [property: string]: any
  }

  /**
   * color_card.GetMerchantBaseInfoData
   */
  export interface MerchantInfo {
  /**
   * 业务类型
   */
    business_type?: string | number[]
    /**
     * 响应码
     */
    code?: number
    data?: any
    /**
     * 商户ID
     */
    id?: number
    /**
     * Logo地址
     */
    logo_url?: string
    /**
     * 主营产品
     */
    main_products?: string
    /**
     * 商户地址
     */
    merchant_addr?: string
    /**
     * 商户名称
     */
    merchant_name?: string
    /**
     * 响应信息
     */
    msg?: string
    /**
     * 联系电话
     */
    phone_contacts?: string
    /**
     * 服务过期时间
     */
    service_expire?: string
    /**
     * 版本号
     */
    version?: string
    [property: string]: any
  }

}
