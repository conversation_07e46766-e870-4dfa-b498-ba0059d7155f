/*
 * @Description:电子色卡服务
 */
import { useRequest } from '@/use/useRequest'

// 获取电子色卡管理列表
export function GetElectronicColorCardList() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/getElectronicColorCardList',
    method: 'get',
    pagination: true,
  })
}
// 获取充值记录列表
export function GetEleColorCardRechargeHistoryList() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/getEleColorCardRechargeHistoryList',
    method: 'get',
    pagination: true,
  })
}

// 禁用电子色卡管理
export function UpdateElectronicColorCardStatusDisable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/updateElectronicColorCardStatusDisable',
    method: 'put',
  })
}

// 启用电子色卡管理
export function UpdateElectronicColorCardStatusEnable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/updateElectronicColorCardStatusEnable',
    method: 'put',
  })
}

// 禁用电子色卡搜索图片管理
export function UpdateElectronicColorCardSearchImageStatusDisable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/disable_search_image',
    method: 'put',
  })
}

// 启用电子色卡管理
export function UpdateElectronicColorCardSearchImageStatusEnable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/enable_search_image',
    method: 'put',
  })
}
// 充值
export function Recharge() {
  return useRequest({
    url: '/admin/v1/tenantManagement/electronicColorCard/rechargeEleColorCard',
    method: 'post',
  })
}
