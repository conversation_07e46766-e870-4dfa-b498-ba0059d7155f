import { useRequest } from '@/use/useRequest'

// 获取列表
export function getPmcGreyPlanOrderList() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/getPmcGreyPlanOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 审核
export function updatePmcGreyPlanOrderAuditStatusPass() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/updatePmcGreyPlanOrderAuditStatusPass',
    method: 'put',
  })
}

// 消审
export function updatePmcGreyPlanOrderAuditStatusWait() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/updatePmcGreyPlanOrderAuditStatusWait',
    method: 'put',
  })
}

// 详情
export function getPmcGreyPlanOrder() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/getPmcGreyPlanOrder',
    method: 'get',
  })
}

// 作废
export function updatePmcGreyPlanOrderAuditStatusCancel() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/updatePmcGreyPlanOrderAuditStatusCancel',
    method: 'put',
  })
}

// 驳回
export function updatePmcGreyPlanOrderAuditStatusReject() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/updatePmcGreyPlanOrderAuditStatusReject',
    method: 'put',
  })
}

// 计划分配

export function saveGfDetailDetails() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/saveGfDetailDetails',
    method: 'put',
  })
}

// 获取合并列表
export function getPmcGreyPlanOrderAndDetailList() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/getPmcGreyPlanOrderAndDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export function getProductStockDetail() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/getProductStockDetail',
    method: 'get',
  })
}
// 单据下推
export function PushPmcGreyPlanOrderSummary() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/pushPmcGreyPlanOrderSummary',
    method: 'get',
  })
}

//
export function yarn_by_detail_id() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/yarn_by_detail_id',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

//
export function SplitOrder() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/split',
    method: 'put',
  })
}
//
export function CancelSplitOrder() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/split_cancel',
    method: 'put',
  })
}
//
export function GetGreyPlanOrderPushRecord() {
  return useRequest({
    url: '/admin/v1/sale/pmcGreyPlanOrder/pushRecord',
    method: 'get',
  })
}
