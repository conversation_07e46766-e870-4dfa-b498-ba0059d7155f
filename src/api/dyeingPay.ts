import { useRequest } from '@/use/useRequest'

// 获取列表
export const dnflist = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const dnfdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/detail',
    method: 'get',
  })
}

// 详情
export const rawMatlDnfDetail = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_dnf/detail',
    method: 'get',
  })
}

// 审核
export const dnfpass = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/pass',
    method: 'put',
  })
}

// 消审
export const dnfcancel = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/cancel',
    method: 'put',
  })
}

// 驳回
export const dnfreject = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/reject',
    method: 'put',
  })
}

// 作废
export const dnfvoid = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf/void',
    method: 'put',
  })
}

//  更新
export const dnfput = () => {
  return useRequest({
    url: '/admin/v1/payable/dnf',
    method: 'put',
  })
}

// 获取用坯信息
export const notice_orderdetail = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/detail',
    method: 'get',
  })
}
