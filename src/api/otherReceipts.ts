import { useRequest } from '@/use/useRequest'

// 获取列表
export const getActuallyPayOrderListForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/getActuallyCollectOrderListForOther',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增
export const addActuallyCollectOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/addActuallyCollectOrderForOther',
    method: 'post',
  })
}

// 编辑
export const updateActuallyCollectOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/updateActuallyCollectOrderForOther',
    method: 'put',
  })
}

// 详情
export const getActuallyCollectOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/getActuallyCollectOrderForOther',
    method: 'get',
  })
}

// 审核
export const updateActuallyPayOrderStatusPassForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/updateActuallyCollectOrderStatusPassForOther',
    method: 'put',
  })
}

// 消审
export const updateActuallyPayOrderStatusWaitForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/updateActuallyCollectOrderStatusWaitForOther',
    method: 'put',
  })
}

// 作废
export const updateActuallyPayOrderStatusCancelForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/updateActuallyCollectOrderStatusCancelForOther',
    method: 'put',
  })
}

// 驳回
export const updateActuallyPayOrderStatusRejectForOther = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/otherCollectOrder/updateActuallyCollectOrderStatusRejectForOther',
    method: 'put',
  })
}
