import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFabricDyeProcessInfoList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/getFabricDyeProcessInfoList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建数据
export const addFabricDyeProcessInfo = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/addFabricDyeProcessInfo',
    method: 'post',
  })
}

// 更新数据
export const updateFabricDyeProcessInfo = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/updateFabricDyeProcessInfo',
    method: 'put',
  })
}

// 详情
export const getFabricDyeProcessInfo = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/getFabricDyeProcessInfo',
    method: 'get',
  })
}

// 删除
export const deleteFabricDyeProcessInfo = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/deleteFabricDyeProcessInfo',
    method: 'delete',
  })
}

// 更新状态
export const updateFabricDyeProcessInfoStatus = () => {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/updateFabricDyeProcessInfoStatus',
    method: 'put',
  })
}
