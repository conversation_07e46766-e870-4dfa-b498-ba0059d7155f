import { useRequest } from '@/use/useRequest'

// 获取列表
export function getFpmProcessInOrderList() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/getFpmProcessInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export function addFpmProcessInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/addFpmProcessInOrder',
    method: 'post',
  })
}

// 获取详情
export function getFpmProcessInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/getFpmProcessInOrder',
    method: 'get',
  })
}

// 作废
export function updateFpmProcessInOrderStatusCancel() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateFpmProcessInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export function updateFpmProcessInOrderStatusPass() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateFpmProcessInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export function updateFpmProcessInOrderStatusReject() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateFpmProcessInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export function updateFpmProcessInOrderStatusWait() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateFpmProcessInOrderStatusWait',
    method: 'put',
  })
}

// 更新
export function updateFpmProcessInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateFpmProcessInOrder',
    method: 'put',
  })
}

// 获取染整枚举列表
export function getSituationList() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/list_enum',
    method: 'get',
    pagination: true,
  })
}
// 获取染整进度详情
export function getDyeingAndFinishing() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/detail',
    method: 'get',
  })
}
// 更新用坯毛重成本
export function UpdateBuoyantWeightPrice() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/updateBuoyantWeightPrice',
    method: 'put',
  })
}
