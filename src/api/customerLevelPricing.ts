import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function GetCustomerSalePriceAdjustList() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/getCustomerSalePriceAdjustList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function GetCustomerSalePriceAdjustListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/getCustomerSalePriceAdjustList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 更新客户销售调价状态
export function UpdateCustomerSalePriceAdjustStatus() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/updateCustomerSalePriceAdjustStatus',
    method: 'put',
  })
}

// 更新客户销售调价
export function UpdateCustomerSalePriceAdjust() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/updateCustomerSalePriceAdjust',
    method: 'put',
  })
}

// 详情
export function GetCustomerSalePriceAdjust() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/getCustomerSalePriceAdjust',
    method: 'get',
  })
}

// 添加
export function AddCustomerSalePriceAdjust() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/addCustomerSalePriceAdjust',
    method: 'post',
  })
}

// 删除
export function DeleteCustomerSalePriceAdjust() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/deleteCustomerSalePriceAdjust',
    method: 'delete',
  })
}

// 下拉
export function GetCustomerSalePriceAdjustDropdownList() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/getCustomerSalePriceAdjustDropdownList',
    method: 'get',
  })
}

// 作废
export function UpdateCustomerSalePriceAdjustAuditStatusCancel() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/updateCustomerSalePriceAdjustAuditStatusCancel',
    method: 'put',
  })
}

// 审核
export function UpdateCustomerSalePriceAdjustAuditStatusPass() {
  return useRequest({
    url: '/admin/v1/sale_price/customerSalePriceAdjust/updateCustomerSalePriceAdjustAuditStatusPass',
    method: 'put',
  })
}
