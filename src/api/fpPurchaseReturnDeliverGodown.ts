import { useRequest } from '@/use/useRequest'

// 获取列表
export function getFpmPrtOutOrderList() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/getFpmPrtOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 获取库存列表
export function getStockProductDyelotNumberDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDyelotNumberDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取细码列表
export function getStockProductWeightDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductWeightDetailList',
    method: 'get',
    pagination: true,
  })
}
// 获取细码列表
export function GetProcessOutItemFcEnumList() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/getProcessOutItemFcEnumList',
    method: 'get',
  })
}
// 获取来源类型
export function GetWarehouseGoodOutEnumTypeReverseIntMap() {
  return useRequest({
    url: '/admin/v1/product/enum/getWarehouseGoodOutEnumTypeReverseIntMap',
    method: 'get',
  })
}

// 添加成品采购退货出仓单
export function addFpmPrtOutOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/addFpmPrtOutOrder',
    method: 'post',
  })
}

// 获取成品采购退货出仓单
export function getFpmPrtOutOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/getFpmPrtOutOrder',
    method: 'get',
  })
}

// 作废
export function updateFpmPrtOutOrderStatusCancel() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/updateFpmPrtOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export function updateFpmPrtOutOrderStatusPass() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/updateFpmPrtOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export function updateFpmPrtOutOrderStatusReject() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/updateFpmPrtOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export function updateFpmPrtOutOrderStatusWait() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/updateFpmPrtOutOrderStatusWait',
    method: 'put',
  })
}

// 更新成品采购退货出仓单
export function updateFpmPrtOutOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrtOutOrder/updateFpmPrtOutOrder',
    method: 'put',
  })
}

// 获取采购退货单列表
export function getPurchaseProductReturnItemDropList() {
  return useRequest({
    url: '/admin/v1/purchase/purchaseProductReturnOrder/getPurchaseProductReturnItemDropList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
