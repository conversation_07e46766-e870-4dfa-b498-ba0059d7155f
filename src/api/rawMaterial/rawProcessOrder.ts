import { useRequest } from '@/use/useRequest'

/**
 * 获取原料扣款出货单详情
 */
export const GetDeductionOutDetail = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/deduction_out`,
    method: 'get',
  })
}

/**
 * 获取原料扣款出货单列表
 */
export const GetDeductionOutList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out/list',
    method: 'get',
  })
}

/**
 * 新增原料扣款出货单
 * @constructor
 */
export const AddDeductionOut = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out',
    method: 'post',
  })
}

/**
 * 修改原料扣款出货单
 */
export const UpdateDeductionOut = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out',
    method: 'put',
  })
}

/**
 * 作废原料扣款出货单
 */
export const UpdateDeductionOutStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out/cancel',
    method: 'put',
  })
}

/**
 * 审核原料扣款出货单
 */
export const UpdateDeductionOutStatusPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out/pass',
    method: 'put',
  })
}

/**
 * 驳回原料扣款出货单
 */
export const UpdateDeductionOutStatusReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out/reject',
    method: 'put',
  })
}

/**
 * 消审原料扣款出货单
 */
export const UpdateDeductionOutStatusUnWait = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/deduction_out/wait',
    method: 'put',
  })
}
