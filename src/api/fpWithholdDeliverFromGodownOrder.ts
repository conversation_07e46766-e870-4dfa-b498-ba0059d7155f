import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmDeductionOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/getFpmDeductionOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmDeductionOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/addFpmDeductionOutOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmDeductionOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/getFpmDeductionOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmDeductionOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/updateFpmDeductionOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmDeductionOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/updateFpmDeductionOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmDeductionOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/updateFpmDeductionOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmDeductionOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/updateFpmDeductionOutOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmDeductionOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmDeductionOutOrder/updateFpmDeductionOutOrder',
    method: 'put',
  })
}
