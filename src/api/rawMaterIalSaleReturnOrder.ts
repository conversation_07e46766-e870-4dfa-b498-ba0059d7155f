import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export const SaleReturnOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 导出数据
export const SaleReturnOrderListDownLoad = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_material/sale_return_order/list',
    method: 'get',
    nameFile,
  })
}

// 获取详情
export const SaleReturnOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/detail',
    method: 'get',
  })
}

// 添加
export const SaleReturnOrderAdd = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order',
    method: 'post',
  })
}
// 修改
export const SaleReturnOrderEdit = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order',
    method: 'put',
  })
}

// 消审
export const SaleReturnOrderCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/cancel',
    method: 'put',
  })
}

// 审核
export const SaleReturnOrderPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/pass',
    method: 'put',
  })
}

// 驳回
export const SaleReturnOrderReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/reject',
    method: 'put',
  })
}

// 作废
export const SaleReturnOrderVoid = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_return_order/void',
    method: 'put',
  })
}

// 获取原料采购单项列表
export const SaleOrderItemList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/item_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
