import { useRequest } from '@/use/useRequest'

// 获取列表
export const grey_fabric_purlist = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const grey_fabric_purdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/detail',
    method: 'get',
  })
}

// 审核
export const grey_fabric_purpass = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/pass',
    method: 'put',
  })
}

// 消审
export const grey_fabric_purcancel = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/cancel',
    method: 'put',
  })
}

// 驳回
export const grey_fabric_purreject = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/reject',
    method: 'put',
  })
}

// 作废
export const grey_fabric_purvoid = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur/void',
    method: 'put',
  })
}

//  更新
export const grey_fabric_purput = () => {
  return useRequest({
    url: '/admin/v1/payable/grey_fabric_pur',
    method: 'put',
  })
}
