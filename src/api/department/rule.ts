export interface SystemGetDepartmentData {
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 深度 */
  deep?: number
  /** 部门员工数 */
  employee_num?: number
  /** 记录ID */
  id?: number
  /** Code           string                `json:"code"`             // 部门编号 */
  name?: string
  /** 父部门ID */
  parent_id?: number
  /** 备注 */
  remark?: string
  /** 营销体系名称 */
  sale_system_id?: number
  /** 营销体系名称 */
  sale_system_name?: string
  /** 状态 */
  status?: any
  /** 状态名称 */
  status_name?: string
  /** 子部门 */
  sub_department?: SystemGetDepartmentData[]
  /** 部门类型 */
  type?: any
  /** 部门类型名称 */
  type_name?: string
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
}
