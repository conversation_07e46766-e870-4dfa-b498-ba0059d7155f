import { useRequest } from '@/use/useRequest'

// 获取order_prefix表
export function GetOrderPrefix() {
  return useRequest<Api.GetOrderPrefix.Request, Api.GetOrderPrefix.Response>({
    url: '/admin/v1/system/orderPrefix',
    method: 'get',
  })
}
// 更新order_prefix表
export function UpdateOrderPrefix() {
  return useRequest<Api.UpdateOrderPrefix.Request, Api.UpdateOrderPrefix.Response>({
    url: '/admin/v1/system/orderPrefix',
    method: 'put',
  })
}
// 添加order_prefix表
export function AddOrderPrefix() {
  return useRequest<Api.AddOrderPrefix.Request, Api.AddOrderPrefix.Response>({
    url: '/admin/v1/system/orderPrefix',
    method: 'post',
  })
}
