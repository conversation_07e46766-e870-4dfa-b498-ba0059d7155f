import { useRequest } from '@/use/useRequest'

// 获取列表
export const getAdvanceCollectOrderList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/getAdvanceCollectOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 作废
export const updateAdvanceCollectOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/updateAdvanceCollectOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateAdvanceCollectOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/updateAdvanceCollectOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateAdvanceCollectOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/updateAdvanceCollectOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateAdvanceCollectOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/updateAdvanceCollectOrderStatusWait',
    method: 'put',
  })
}

// 获取详情
export const getAdvanceCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/getAdvanceCollectOrder',
    method: 'get',
  })
}

// 编辑
export const updateAdvanceCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/updateAdvanceCollectOrder',
    method: 'put',
  })
}

// 新建
export const addAdvanceCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/addAdvanceCollectOrder',
    method: 'post',
  })
}

// 结算方式枚举（新）
export const GetInfoSaleSettlementMethodEnumList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleSettlementMethod/getInfoSaleSettlementMethodEnumList',
    method: 'get',
  })
}

// 预收列表枚举
export const getAdvanceCollectOrderListEnum = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/getAdvanceCollectOrderListEnum',
    method: 'get',
  })
}
