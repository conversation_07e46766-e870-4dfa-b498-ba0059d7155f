import { useRequest } from '@/use/useRequest'

// 获取列表
export const getInfoBasicDefectList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增
export const addInfoBasicDefect = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect',
    method: 'post',
  })
}

// 编辑
export const updateInfoBasicDefect = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect',
    method: 'put',
  })
}

// 更新状态
export const updateInfoBasicDefectStatus = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect/updateStatus',
    method: 'put',
  })
}
// 删除
export const deleteInfoBasicDefect = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBasicDefect',
    method: 'delete',
  })
}
