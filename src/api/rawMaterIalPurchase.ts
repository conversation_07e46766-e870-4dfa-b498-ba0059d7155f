import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function PurchaseReceiveOrderList() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function PurchaseReceiveOrderListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/raw_material/purchase_receive_order/list',
    method: 'get',
    nameFile,
  })
}

// 获取详情
export function PurchaseReceiveOrderDetail() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/detail',
    method: 'get',
  })
}

// 添加原料采购收货单
export function PurchaseReceiveOrder() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order',
    method: 'post',
  })
}
// 编辑原料采购收货单
export function PurchaseReceiveOrderEdit() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order',
    method: 'put',
  })
}

// 审核
export function PurchaseReceiveOrderPass() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/pass',
    method: 'put',
  })
}

// 驳回
export function PurchaseReceiveOrderReject() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/reject',
    method: 'put',
  })
}

// 作废
export function PurchaseReceiveOrderVoid() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/void',
    method: 'put',
  })
}

// 消审
export function PurchaseReceiveOrderCancel() {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/cancel',
    method: 'put',
  })
}

// 获取仓管员
// export const GetWarehouseManagerDropdownList = () => {
//   return useRequest({
//     url: '/admin/v1/user/getUserDropdownList',
//     method: 'get',
//     query: { duty: 5 },
//   })
// }
