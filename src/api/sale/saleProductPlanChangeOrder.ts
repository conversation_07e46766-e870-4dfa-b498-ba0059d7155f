import { useRequest } from '@/use/useRequest'

// 获取成品销售计划变更单详情
export function GetSaleProductPlanChangeOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/getSaleProductPlanChangeOrder',
    method: 'get',
  })
}

// 获取成品销售计划变更单列表
export function GetSaleProductPlanChangeOrderList() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/getSaleProductPlanChangeOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加成品销售计划变更单
export function AddSaleProductPlanChangeOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/addSaleProductPlanChangeOrder',
    method: 'post',
  })
}

// 更新成品销售计划变更单
export function UpdateSaleProductPlanChangeOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrder',
    method: 'put',
  })
}

// 删除成品销售计划变更单
export function DeleteSaleProductPlanChangeOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/deleteSaleProductPlanChangeOrder',
    method: 'delete',
  })
}

// 更新成品销售计划变更单状态-作废
export function UpdateSaleProductPlanChangeOrderAuditStatusCancel() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrderAuditStatusCancel',
    method: 'put',
  })
}

// 更新成品销售计划变更单状态-审核
export function UpdateSaleProductPlanChangeOrderAuditStatusPass() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrderAuditStatusPass',
    method: 'put',
  })
}

// 更新成品销售计划变更单状态-驳回
export function UpdateSaleProductPlanChangeOrderAuditStatusReject() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrderAuditStatusReject',
    method: 'put',
  })
}

// 更新成品销售计划变更单状态-消审
export function UpdateSaleProductPlanChangeOrderAuditStatusWait() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrderAuditStatusWait',
    method: 'put',
  })
}

// 更新成品销售计划变更单业务状态
export function UpdateSaleProductPlanChangeOrderBusinessClose() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanChangeOrder/updateSaleProductPlanChangeOrderBusinessClose',
    method: 'put',
  })
}
