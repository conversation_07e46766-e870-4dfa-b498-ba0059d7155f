import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmOtherReceiveOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/getGfmOtherReceiveOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmOtherReceiveOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/getGfmOtherReceiveOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmOtherReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/getGfmOtherReceiveOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmOtherReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/addGfmOtherReceiveOrder',
    method: 'post',
  })
}

// 编辑
export const updateGfmOtherReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/updateGfmOtherReceiveOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmOtherReceiveOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/updateGfmOtherReceiveOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmOtherReceiveOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/updateGfmOtherReceiveOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmOtherReceiveOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/updateGfmOtherReceiveOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmOtherReceiveOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherReceiveOrder/updateGfmOtherReceiveOrderStatusReject',
    method: 'put',
  })
}
