import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const finishinglist = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const finishinglistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/dnf_quote/finishing/list',
    method: 'get',
    nameFile,
  })
}

// 新增
export const finishingpost = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing',
    method: 'post',
  })
}

// 编辑
export const finishingput = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing',
    method: 'put',
  })
}

// 详情
export const finishingdetail = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/detail',
    method: 'get',
  })
}

// 审核
export const finishingpass = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/pass',
    method: 'put',
  })
}

// 消审
export const finishingcancel = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/cancel',
    method: 'put',
  })
}

// 作废
export const finishingvoid = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/void',
    method: 'put',
  })
}

// 驳回
export const finishingreject = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/reject',
    method: 'put',
  })
}
