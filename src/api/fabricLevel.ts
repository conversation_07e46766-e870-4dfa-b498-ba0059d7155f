import { useRequest } from '@/use/useRequest'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/getInfoBaseGreyFabricLevelList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取下拉列表
export const GetInfoBaseGreyFabricLevelListUseByOther = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/getInfoBaseGreyFabricLevelListUseByOther',
    method: 'get',
  })
}

// 新建类型
export const addTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/addInfoBaseGreyFabricLevel',
    method: 'post',
  })
}

// 更新类型
export const updateTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/updateInfoBaseGreyFabricLevel',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/deleteInfoBaseGreyFabricLevel',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/updateInfoBaseGreyFabricLevelStatus',
    method: 'put',
  })
}
