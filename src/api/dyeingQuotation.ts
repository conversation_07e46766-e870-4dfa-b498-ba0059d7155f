import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const dyeinglist = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const dyeinglistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/dnf_quote/dyeing/list',
    method: 'get',
    nameFile,
  })
}

// 新增
export const dyeingpost = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing',
    method: 'post',
  })
}

// 编辑
export const dyeingput = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing',
    method: 'put',
  })
}

// 详情
export const dyeingdetail = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/detail',
    method: 'get',
  })
}

// 审核
export const dyeingpass = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/pass',
    method: 'put',
  })
}

// 消审
export const dyeingcancel = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/cancel',
    method: 'put',
  })
}

// 作废
export const dyeingvoid = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/void',
    method: 'put',
  })
}

// 驳回
export const dyeingreject = () => {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/reject',
    method: 'put',
  })
}

// 选择染厂色号
export const getDyeingColorDetailsByProductListEnum = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getDyeingColorDetailsByProductListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
