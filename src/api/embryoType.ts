import { useRequest } from '@/use/useRequest'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/getTypeGreyFabricOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export const addTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/addTypeGreyFabricOrder',
    method: 'post',
  })
}

// 更新类型
export const updateTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/updateTypeGreyFabricOrder',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/deleteTypeGreyFabricOrder',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/updateTypeGreyFabricOrderStatus',
    method: 'put',
  })
}
