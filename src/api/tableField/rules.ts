export interface SystemGetListHabitsManagementData {
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 是否是默认模板 */
  default?: boolean
  /** 习惯 */
  habits?: string
  /** 模板 */
  habits_temp?: string
  /** 模板id */
  habits_temp_id?: number
  /** 记录ID */
  id?: number
  /** 关键词 */
  key?: string
  /** 类型 1保存信息 2模板 */
  type?: any
  /** 类型名称 */
  type_name?: string
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
  /** 用户id */
  user_id?: number
}

export interface SystemGetListHabitsManagementDataSave {
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 是否是默认模板 */
  default?: boolean
  /** 习惯 */
  habits?: string
  /** 模板 */
  habits_temp?: string
  /** 模板id */
  habits_temp_id?: number
  /** 记录ID */
  id?: number
  /** 关键词 */
  key?: string
  /** 类型 1保存信息 2模板 */
  type?: any
  /** 类型名称 */
  type_name?: string
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
  /** 用户id */
  user_id?: number
}

export interface SystemUpdateListHabitsParam {
  /** 是否是默认模板 */
  default?: boolean
  /** 习惯 */
  habits?: string
  /** 模板 */
  habits_temp?: string
  /** 模板id */
  habits_temp_id?: number
  id?: number
  /** 关键词 */
  key?: string
  /** 用户id */
  user_id?: number
}

export interface SystemUpdateListHabitsData {
  id?: number
}
