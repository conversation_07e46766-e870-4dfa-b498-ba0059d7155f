import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const Business_unitsale_grouplist = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 删除列表数据
export const Business_unitsale_groupdelete = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group',
    method: 'delete',
  })
}

// 启用数据
export const Business_unitsale_groupdenable = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group/enable',
    method: 'put',
  })
}

// 禁用数据
export const Business_unitsale_groupddisable = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group/disable',
    method: 'put',
  })
}

// 新建销售群体
export const Business_unitsale_groupdpost = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group',
    method: 'post',
  })
}

// 修改销售群体
export const Business_unitsale_groupdput = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group',
    method: 'put',
  })
}
