import { useRequest } from '@/use/useRequest'

// 获取列表
export function raw_material_list() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export function raw_material_colordetail() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/detail',
    method: 'get',
  })
}

// 新建数据

export function raw_material_add() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color',
    method: 'post',
  })
}

//  删除数据
export function raw_material_delete() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color',
    method: 'delete',
  })
}

// 启用数据

export function raw_material_enable() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/enable',
    method: 'put',
  })
}

// 禁用数据

export function raw_material_disable() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/disable',
    method: 'put',
  })
}

// 更新数据

export function raw_material_update() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color',
    method: 'put',
  })
}

// 枚举列表
export function raw_material_colorlist_enum() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 原料染厂颜色列表
export function raw_material_colorlist_factory() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_factory',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 原料染厂颜色列表(枚举)
export function raw_material_colorlist_factory_enum() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_factory_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export function list_wait_dye_yarn_enum() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_wait_dye_yarn_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export function raw_materialdetail() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/detail',
    method: 'get',
  })
}
