import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function dyeing_and_finishinglist() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export function dyeing_and_finishinglistExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/dyeing_and_finishing/notice_order/list',
    method: 'get',
    nameFile,
  })
}

// 详情
export function dyeing_and_finishingdetail() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/detail',
    method: 'get',
  })
}

// 审核
export function dyeing_and_finishingpass() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/pass',
    method: 'put',
  })
}

// 驳回
export function dyeing_and_finishingreject() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/reject',
    method: 'put',
  })
}

// 作废
export function dyeing_and_finishingvoid() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/void',
    method: 'put',
  })
}

// 消审
export function dyeing_and_finishingCancel() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/cancel',
    method: 'put',
  })
}

// 新建
export function dyeing_and_finishingpost() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order',
    method: 'post',
  })
}

// 编辑
export function dyeing_and_finishingput() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order',
    method: 'put',
  })
}

// 选择色号
export function getDyeingColorDetailsByProduct() {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getDyeingColorDetailsByProduct',
    method: 'get',
  })
}

// 从库存中添加坯布
export function getGfmWarehouseSummaryListEnum() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryListEnum',
    method: 'get',
  })
}

// 获取工艺列表
export function getInfoDyeingFinishingProcessDataEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/getInfoDyeingFinishingProcessDataEnumList',
    method: 'get',
  })
}

// 获取布种染整工艺资料列表（枚举）
export function getDyeingFinishingProcessDataEnumList() {
  return useRequest({
    url: '/admin/v1/basic_data/fabricDyeProcessInfo/for/dnf',
    method: 'get',
  })
}

// 从成品加工发料出仓单成品列表
export function getProcessOutItemEnumList() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/getProcessOutItemEnumList',
    method: 'get',
  })
}

// 销售计划单枚举列表
export function getSaleProductPlanOrderGfDropdownList() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrderProductDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 快捷添加染厂颜色
export function addQuickProductDyeColor() {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/addQuickProductDyeColor',
    method: 'post',
  })
}

// 后台快捷添加染厂颜色
export function addQuickProductDyeColorByAdmin() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/addQuickDyeColor',
    method: 'post',
  })
}

/**
 * 染整进度添加配布
 */
export function AddDyeingScheduleDistribute() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/updateNoticeGF',
    method: 'put',
  })
}

/**
 * 获取染整进度用坯信息
 */
export function GetDyeingScheduleGFInfo() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/item/use_gf',
    method: 'get',
  })
}

/**
 * 获取上一单染整通知单信息
 */
export function GetLastOrder() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/notice_order/last_order',
    method: 'get',
  })
}
