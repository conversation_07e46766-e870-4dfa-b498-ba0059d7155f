import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function getListRawMaterial() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function getListRawMaterialExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/should_collect_order/rawMaterial/getList',
    method: 'get',
    nameFile,
  })
}

// 获取详情
export function getRawMaterial() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/get',
    method: 'get',
  })
}

// // 添加
// export const SaleOrderAdd = () => {
//   return useRequest({
//     url: '/admin/v1/raw_material/sale_order',
//     method: 'post',
//   })
// }

// 修改
export function updateRawMaterial() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/update',
    method: 'put',
  })
}

// 作废
export function updateAuditStatusCancel() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/updateAuditStatusCancel',
    method: 'put',
  })
}

// 审核
export function updateAuditStatusPass() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/updateAuditStatusPass',
    method: 'put',
  })
}

// 驳回
export function updateAuditStatusReject() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/updateAuditStatusReject',
    method: 'put',
  })
}

// 消审
export function updateAuditStatusWait() {
  return useRequest({
    url: '/admin/v1/should_collect_order/rawMaterial/updateAuditStatusWait',
    method: 'put',
  })
}
