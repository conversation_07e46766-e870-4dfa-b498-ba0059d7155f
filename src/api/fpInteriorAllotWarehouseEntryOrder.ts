import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getFpmInternalAllocateInOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/getFpmInternalAllocateInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmInternalAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/addFpmInternalAllocateInOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmInternalAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/getFpmInternalAllocateInOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmInternalAllocateInOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/updateFpmInternalAllocateInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmInternalAllocateInOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/updateFpmInternalAllocateInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmInternalAllocateInOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/updateFpmInternalAllocateInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmInternalAllocateInOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/updateFpmInternalAllocateInOrderStatusWait',
    method: 'put',
  })
}

// 编辑
export const updateFpmInternalAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmInternalAllocateInOrder/updateFpmInternalAllocateInOrder',
    method: 'put',
  })
}
