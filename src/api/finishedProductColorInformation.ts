import { useRequest } from '@/use/useRequest'

// 获取列表
export const GetFinishProductColorList = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColorList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取下拉列表
export const GetFinishProductColorDropdownList = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColorDropdownList',
    method: 'get',
    pagination: true,
  })
}

// 详情
export const GetFinishProductColor = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColor',
    method: 'get',
  })
}

// 新增
export const AddFinishProductColor = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/addFinishProductColor',
    method: 'post',
  })
}

// 更新
export const UpdateFinishProductColor = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/updateFinishProductColor',
    method: 'put',
  })
}

// 更新状态
export const UpdateFinishProductColorStatus = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/updateFinishProductColorStatus',
    method: 'put',
  })
}

// 删除
export const DeleteFinishProductColor = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/deleteFinishProductColor',
    method: 'delete',
  })
}

// 根据成品颜色id获取复合布信息
export const GetProductCompositeDetails = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getProductCompositeDetails',
    method: 'get',
  })
}

// 根据成品颜色id获取染厂颜色信息
export const GetProductDyeingColorDetails = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getProductDyeingColorDetails',
    method: 'get',
  })
}

// 成品色号类型枚举
export const GetTypeFinishedProductColorEnumList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFinishedProductColor/getTypeFinishedProductColorEnumList',
    method: 'get',
  })
}

// 成品色号类型枚举（根据成品id获取）
export const GetProductColorKindByProductId = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getProductColorKindByProductId',
    method: 'get',
  })
}

// 成品等级枚举
export const GetInfoBaseFinishedProductLevelEnumList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/getInfoBaseFinishedProductLevelEnumList',
    method: 'get',
  })
}

// 获取布种类型平铺选择
export const GetKindAndProductList = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getKindAndProductList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
