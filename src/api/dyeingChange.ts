import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const change_orderlist = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const change_orderExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/dyeing_and_finishing/change_order/list',
    method: 'get',
    nameFile,
  })
}

// 新建
export const change_orderpost = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order',
    method: 'post',
  })
}

// 编辑
export const change_orderput = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order',
    method: 'put',
  })
}

// 详情
export const change_orderdetail = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/detail',
    method: 'get',
  })
}

// 审核
export const change_orderpass = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/pass',
    method: 'put',
  })
}

// 消审
export const change_ordercancel = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/cancel',
    method: 'put',
  })
}

// 驳回
export const change_orderreject = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/reject',
    method: 'put',
  })
}

// 作废
export const change_ordervoid = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/change_order/void',
    method: 'put',
  })
}
