import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 织厂纱库存
export function getRawMaterialListYarn() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_yarn',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 织厂纱库存--导出
export function getRawMaterialListYarnExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/stock/raw_material/list_yarn',
    method: 'get',
    nameFile,
  })
}

// 染纱厂库存
export function getRawMaterialListWaitDyeYarn() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_wait_dye_yarn',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 染纱厂库存--导出
export function getRawMaterialListWaitDyeYarnExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/stock/raw_material/list_wait_dye_yarn',
    method: 'get',
    nameFile,
  })
}

// 在染纱库存
export function getRawMaterialListDyingYarn() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_dying_yarn',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 在染纱库存--导出
export function getRawMaterialListDyingYarnExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/stock/raw_material/list_dying_yarn',
    method: 'get',
    nameFile,
  })
}

// 原料颜色下拉枚举
export function GetRawMaterialColor() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_enum',
    method: 'get',
  })
}

// 编辑织厂备注
export function GetRawMaterialEditFactoryRemark() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/edit_factory_remark',
    method: 'put',
  })
}

// 原料库存编辑织厂记录
export function GetRawMaterialEditRecordList() {
  return useRequest({
    url: '/admin/v1/stock/stockRawMaterialFactoryRemarkRecord/list_by_stock_id',
    method: 'get',
  })
}

// 单据类型枚举
export function GetRawMaterialEditOrderTypeEnum() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/order_type_enum',
    method: 'get',
  })
}

// 进出仓明细
export function getRawMaterialRecordList() {
  return useRequest({
    url: 'admin/v1/stock/raw_material/record_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 原料库存列表(染纱厂库存枚举) 在染
export function GetRawMaterialListDyingYarnEnum() {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_dying_yarn_enum',
    method: 'get',
  })
}

//
export function EditRawMaterialCostPrice() {
  return useRequest({
    url: '/admin/v1/stock/rmCostPrice',
    method: 'put',
  })
}
