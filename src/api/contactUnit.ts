import { useRequest } from '@/use/useRequest'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/getTypeIntercourseUnitsList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export const addTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/addTypeIntercourseUnits',
    method: 'post',
  })
}

// 更新类型
export const updateTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/updateTypeIntercourseUnits',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/deleteTypeIntercourseUnits',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/updateTypeIntercourseUnitsStatus',
    method: 'put',
  })
}

// 获取类型
export const GetEnumBizUnitType = () => {
  return useRequest({
    url: '/admin/v1/basic_data/enum/bizUnitType',
    method: 'get',
  })
}
