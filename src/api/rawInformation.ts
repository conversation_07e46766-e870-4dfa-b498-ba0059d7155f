import { useRequest } from '@/use/useRequest'

// 获取列表
export const raw_material_list = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表(枚举)
export const raw_material_dropdownList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/dropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表枚举
export const raw_material_list_dropdown = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/dropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建数据

export const raw_material_add = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material',
    method: 'post',
  })
}

//  删除数据
export const raw_material_delete = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material',
    method: 'delete',
  })
}

// 启用数据

export const raw_material_enable = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/enable',
    method: 'put',
  })
}

// 禁用数据

export const raw_material_disable = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/disable',
    method: 'put',
  })
}

// 更新数据

export const raw_material_update = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material',
    method: 'put',
  })
}

// 获取坯布资料里的原料数据

export const getRawMaterialInfoListEnum = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getRawMaterialInfoListEnum',
    method: 'get',
  })
}
