import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export const AdjustOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const AdjustOrderListDownLoad = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_material/adjust_order/list',
    method: 'get',
    nameFile,
  })
}

// 获取详情
export const AdjustOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/detail',
    method: 'get',
  })
}

// 添加
export const AdjustOrderAdd = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order',
    method: 'post',
  })
}
// 修改
export const AdjustOrderEdit = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order',
    method: 'put',
  })
}

// 消审
export const AdjustOrderCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/cancel',
    method: 'put',
  })
}

// 审核
export const AdjustOrderPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/pass',
    method: 'put',
  })
}

// 驳回
export const AdjustOrderReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/reject',
    method: 'put',
  })
}

// 作废
export const AdjustOrderVoid = () => {
  return useRequest({
    url: '/admin/v1/raw_material/adjust_order/void',
    method: 'put',
  })
}
