import { useRequest } from '@/use/useRequest'

// 获取拜访标签列表
export function GetVisitTagList() {
  return useRequest({
    url: '/admin/v1/visit_tag/list',
    method: 'get',
    pagination: true,
  })
}
// 添加拜访标签
export function AddVisitTag() {
  return useRequest({
    url: '/admin/v1/visit_tag/add',
    method: 'post',
  })
}
// 获取拜访模式主标签
export function EnumVisitingMode() {
  return useRequest({
    url: '/admin/v1/visit_tag/visiting_mode',
    method: 'get',
  })
}
// 更新拜访标签
export function UpdateVisitTag() {
  return useRequest({
    url: '/admin/v1/visit_tag/update',
    method: 'put',
  })
}
