import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmProduceReceiveOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmProduceReceiveOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmProduceReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrder',
    method: 'get',
  })
}

// 更新数据
export const updateGfmProduceReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrder',
    method: 'put',
  })
}

// 新建数据
export const addGfmProduceReceiveOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/addGfmProduceReceiveOrder',
    method: 'post',
  })
}

// 审核
export const updateGfmProduceReceiveOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmProduceReceiveOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmProduceReceiveOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmProduceReceiveOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusReject',
    method: 'put',
  })
}

// 获取通知单列表
export const getProductionNotifyOrderList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
