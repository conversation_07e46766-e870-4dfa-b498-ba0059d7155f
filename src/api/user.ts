import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取用户
export const AdminUserlist = () => {
  return useRequest({
    url: '/admin/v1/user/getUserList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取用户不分页
export const AdminUserlistAll = () => {
  return useRequest({
    url: '/admin/v1/user/getUserList',
    method: 'get',
  })
}

// 新建用户
export const AdminaddUser = () => {
  return useRequest({
    url: '/admin/v1/user/addUser',
    method: 'post',
  })
}

// 更新用户
export const AdmineditUser = () => {
  return useRequest({
    url: '/admin/v1/user/updateUser',
    method: 'put',
  })
}

// 更改密码
export const AdminUpadatePassword = () => {
  return useRequest({
    url: '/admin/v1/user/updatePassword',
    method: 'put',
  })
}

// 更新状态
export const AdminupdateUserStatus = () => {
  return useRequest({
    url: '/admin/v1/user/updateUserStatus',
    method: 'put',
  })
}

// 导出用户
export const AdminUserlistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/user/getUserList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 查看用户
export const AdmingetUser = () => {
  return useRequest({
    url: '/admin/v1/user/getUser',
    method: 'get',
  })
}

// 数据权限
export const RoleAccessDataScope = () => {
  return useRequest({
    url: '/admin/v1/enum/roleAccessDataScope',
    method: 'get',
  })
}
