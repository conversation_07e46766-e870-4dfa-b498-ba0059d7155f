import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function getCustomerReconciliationList() {
  return useRequest({
    url: '/admin/v1/should_collect_order/report_forms/getCustomerReconciliationList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表-不分页
export function getCustomerReconciliationListNoPage() {
  return useRequest({
    url: '/admin/v1/should_collect_order/report_forms/getCustomerReconciliationList',
    method: 'get',
    pagination: false,
  })
}

// 获取列表--导出
export function getCustomerReconciliationListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/should_collect_order/report_forms/getCustomerReconciliationList',
    method: 'get',
    nameFile,
  })
}
