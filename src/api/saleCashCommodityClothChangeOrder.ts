import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmChangeArrangeOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/getFpmChangeArrangeOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmChangeArrangeOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/addFpmChangeArrangeOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmChangeArrangeOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/getFpmChangeArrangeOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmChangeArrangeOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/updateFpmChangeArrangeOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmChangeArrangeOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/updateFpmChangeArrangeOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmChangeArrangeOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/updateFpmChangeArrangeOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmChangeArrangeOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/updateFpmChangeArrangeOrderStatusWait',
    method: 'put',
  })
}

// 编辑
export const updateFpmChangeArrangeOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmChangeArrangeOrder/updateFpmChangeArrangeOrder',
    method: 'put',
  })
}

// 获取配布信息
export const getFpmArrangeOrderEnum = () => {
  return useRequest({
    url: `/admin/v1/product/fpmArrangeOrder/getFpmArrangeOrderEnum`,
    method: 'get',
  })
}

// 获取变更信息的出货类型
export const GetWarehouseGoodOutChangeTypeEnum = () => {
  return useRequest({
    url: `/admin/v1/product/enum/getWarehouseGoodOutChangeTypeEnum`,
    method: 'get',
  })
}
