import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getCustomerOweMoneyList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/report_forms/getCustomerOweMoneyList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
export const ExportGetCustomerOweMoneyList = ({ nameFile }: any) => {
  return useDownLoad<any>({
    url: '/admin/v1/should_collect_order/report_forms/getCustomerOweMoneyList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 获取欠款状态
export const OweStatus = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/enum/oweStatus',
    method: 'get',
  })
}
