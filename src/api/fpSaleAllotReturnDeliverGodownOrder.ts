import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmSaleAllocateOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/getFpmSaleAllocateOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmSaleAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/addFpmSaleAllocateOutOrder',
    method: 'post',
  })
}

// 获取
export const getFpmSaleAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/getFpmSaleAllocateOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmSaleAllocateOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/updateFpmSaleAllocateOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmSaleAllocateOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/updateFpmSaleAllocateOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmSaleAllocateOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/updateFpmSaleAllocateOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmSaleAllocateOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/updateFpmSaleAllocateOutOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmSaleAllocateOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateOutOrder/updateFpmSaleAllocateOutOrder',
    method: 'put',
  })
}
