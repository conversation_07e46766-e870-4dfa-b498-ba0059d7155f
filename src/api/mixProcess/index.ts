import type { AddRequest, ListResponse } from './rules'
import { useRequest } from '@/use/useRequest'

// 获取月结结转节点记录表列表
export function GetSettlePointMonthRecord() {
  return useRequest<any, ListResponse>({
    url: '/admin/v1/mixProcess/settlePointRecord/month/list',
    method: 'get',
  })
}
// 添加月结结转节点记录表
export function AddSettlePointMonthRecord() {
  return useRequest<AddRequest, any >({
    url: '/admin/v1/mixProcess/settlePointRecord/month',
    method: 'post',
  })
}

// 删除月结结转节点记录表
export function DeleteSettlePointMonthRecord() {
  return useRequest({
    url: '/admin/v1/mixProcess/settlePointRecord/month',
    method: 'delete',
  })
}
// 获取未审核数据
export function getAbnormalList() {
  return useRequest({
    url: '/admin/v1/mixProcess/settlePointRecord/month/abnormal/list',
    method: 'get',
  })
}
