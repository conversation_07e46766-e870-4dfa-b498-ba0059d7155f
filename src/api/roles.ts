import { useRequest } from '@/use/useRequest'

// 获取角色列表
export const GetRoleListApi = () => {
  return useRequest({
    url: '/admin/v1/role/getRoleList',
    method: 'get',
    pageSize: 50,
    pagination: true,
  })
}

// 根据id获取角色列表
export const GetRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/getRole',
    method: 'get',
  })
}

// 删除角色
export const DeleteRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/deleteRole',
    method: 'delete',
  })
}

// 添加角色表
export const AddRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/addRole',
    method: 'post',
  })
}

// 添加用户角色关系
export const BindUserRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/bindUserRole',
    method: 'post',
  })
}

// 添加用户到角色
export const BindRoleUserApi = () => {
  return useRequest({
    url: '/admin/v1/role/bindRoleUser',
    method: 'post',
  })
}

// 删除用户角色关系
export const UnbindUserRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/unbindUserRole',
    method: 'post',
  })
}

// 更新角色表
export const UpdateRoleApi = () => {
  return useRequest({
    url: '/admin/v1/role/updateRole',
    method: 'put',
  })
}

// 更新角色表状态
export const UpdateRoleStatusApi = () => {
  return useRequest({
    url: '/admin/v1/role/updateRoleStatus',
    method: 'put',
  })
}

// 角色绑定权限
export const RoleAccessAuthApi = () => {
  return useRequest({
    url: '/admin/v1/roleAccess/auth',
    method: 'post',
  })
}

// 根据角色id获取权限
export const GetIdRoleAccessAuthApi = () => {
  return useRequest({
    url: '/admin/v1/roleAccess/auth',
    method: 'get',
  })
}

// 获取角色下用户列表
export const GetRoleUserListApi = () => {
  return useRequest({
    url: '/admin/v1/role/getRoleUserList',
    method: 'get',
    pagination: true,
  })
}

// 获取角色枚举
export const GetRoleDropdownList = () => {
  return useRequest({
    url: '/admin/v1/role/getRoleDropdownList',
    method: 'get',
  })
}
