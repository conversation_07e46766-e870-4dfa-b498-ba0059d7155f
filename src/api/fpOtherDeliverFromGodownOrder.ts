import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmOtherOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/getFpmOtherOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmOtherOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/addFpmOtherOutOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmOtherOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/getFpmOtherOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmOtherOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/updateFpmOtherOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmOtherOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/updateFpmOtherOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmOtherOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/updateFpmOtherOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmOtherOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/updateFpmOtherOutOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmOtherOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOtherOutOrder/updateFpmOtherOutOrder',
    method: 'put',
  })
}
