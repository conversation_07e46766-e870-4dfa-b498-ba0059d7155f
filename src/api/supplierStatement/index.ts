import { useRequest } from '@/use/useRequest'
import type { ResponseList } from '@/api/commonTs'
import type { GetSupplierReconciliationListData } from '@/api/supplierStatement/rules'
import { useDownLoad } from '@/use/useDownLoad'

// 获取供方对账单
export function GetSupplierReconciliationList() {
  return useRequest<any, ResponseList<GetSupplierReconciliationListData>>({
    url: '/admin/v1/payable/report_forms/getSupplierReconciliationList',
    method: 'get',
    pagination: true,
  })
}

// 获取供方对账单-不分页
export function GetSupplierReconciliationListNoPage() {
  return useRequest<any, ResponseList<GetSupplierReconciliationListData>>({
    url: '/admin/v1/payable/report_forms/getSupplierReconciliationList',
    method: 'get',
  })
}

// 供方对账单--导出
export function GetSupplierReconciliationListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/payable/report_forms/getSupplierReconciliationList',
    method: 'get',
    nameFile,
  })
}
