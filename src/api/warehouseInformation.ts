import { useRequest } from '@/use/useRequest'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getPhysicalWarehouseList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取下拉（无权限）
export const GetDropdownListWithoutDS = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getDropdownListWithoutDS',
    method: 'get',
  })
}

// 新建仓库资料
export const addPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/addPhysicalWarehouse',
    method: 'post',
  })
}

/**
 *  选择地址（下拉列表）
 * @returns
 */
export const selectAddressReposistories = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getDistrictArea',
    method: 'get',
  })
}

// 修改状态

export const updatePhysicalWarehouseStatus = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/updatePhysicalWarehouseStatus',
    method: 'put',
  })
}

// 删除数据
export const deletePhysicalWarehouse = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/deletePhysicalWarehouse',
    method: 'delete',
  })
}

// 编辑仓库资料

export const updatePhysicalWarehouse = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/updatePhysicalWarehouse',
    method: 'put',
  })
}

// 仓库资料详情
export const getPhysicalWarehouse = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getPhysicalWarehouse',
    method: 'get',
  })
}

// 新增仓位数据
export const savePhysicalWarehouseBin = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouseBin/savePhysicalWarehouseBin',
    method: 'post',
  })
}

// 删除仓位数据
export const deletePhysicalWarehouseBin = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouseBin/deletePhysicalWarehouseBin',
    method: 'delete',
  })
}

// 仓位资料列表
// export const getPhysicalWarehouseBinList = () => {
//   return useRequest({
//     url: '/admin/v1/warehouse/physicalWarehouseBin/getPhysicalWarehouseBinList',
//     method: 'get',
//     pagination: true,
//     pageSize: 50,
//   })
// }
