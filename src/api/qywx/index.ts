import { useRequest } from '@/use/useRequest'

/**
 * 企微应用配置列表
 */
export function GetQywxConfigList() {
  return useRequest<Api.GetQywxConfigList.Request, Api.GetQywxConfigList.Response>({
    url: `/admin/v1/qywx`,
    method: 'get',
    pagination: true,
  })
}
/**
 * 获取代开发应用列表
 */
export function GetTobeDevelopedApp() {
  return useRequest<Api.GetTobeDevelopedApp.Request, Api.GetTobeDevelopedApp.Response>({
    url: `/admin/v1/qywx/tobe_developed_app`,
    method: 'get',
    pagination: true,
  })
}
/**
 * 代开发应用绑定账套
 */
export function BindTenant() {
  return useRequest<Api.BindTenant.Request, Api.BindTenant.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/bind_tenant`,
    method: 'post',
  })
}
/**
 * 绑定阿布机器人
 */
export function BindRobot() {
  return useRequest<Api.BindRobot.Request, Api.BindRobot.Response>({
    url: `/admin/v1/qywx/tobe_developed_app`,
    method: 'put',
  })
}
/**
 * 创建群机器人
 */
export function BindQywxRobot() {
  return useRequest<Api.BindQywxRobot.Request, Api.BindQywxRobot.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_robot`,
    method: 'post',
  })
}
/**
 * 删除群机器人
 */
export function DeleteQywxRobot() {
  return useRequest<Api.DeleteQywxRobot.Request, Api.DeleteQywxRobot.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_robot`,
    method: 'delete',
  })
}
/**
 * 企微配置页面获取未绑定代开发应用的租户列表
 */
export function GetQywxSearch() {
  return useRequest<Api.GetQywxSearch.Request, Api.GetQywxSearch.Response>({
    url: `/admin/v1/tenantManagement/qywx_search`,
    method: 'get',
    pagination: true,
  })
}
/**
 * 企业微信群机器人列表
 */
export function GetQywxGroupRobotList() {
  return useRequest<Api.GetQywxGroupRobotList.Request, Api.GetQywxGroupRobotList.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_robot`,
    method: 'get',
    pagination: true,
  })
}

/**
 * 获取企业微信用户列表
 */
export function GetQywxUsers() {
  return useRequest<Api.GetQywxUsers.GetQywxUsersParams, Api.GetQywxUsers.GetQYWXUsersResponse>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_users`,
    method: 'get',
    pagination: true,
  })
}

/**
 * 获取企业微信客户列表
 */
export function GetQywxCustomers() {
  return useRequest<Api.GetQywxCustomers.Request, Api.GetQywxCustomers.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_customers`,
    method: 'get',
    pagination: true,
  })
}

/**
 * 企微群聊列表
 */
export function GetQywxGroupChatList() {
  return useRequest<Api.GetQywxGroupChatList.Request, Api.GetQywxGroupChatList.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/qywx_group_chat_list`,
    method: 'get',
    pagination: true,
  })
}

/**
 * 账套取消绑定待开发应用
 */
export function CancelBindTenant() {
  return useRequest<Api.CancelBindTenant.Request, Api.CancelBindTenant.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/cancel_bind_tenant`,
    method: 'put',
  })
}

/**
 * 添加外部群
 */
export function AddQywxGroupChat() {
  return useRequest<Api.AddQywxGroupChat.Request, Api.AddQywxGroupChat.Response>({
    url: `/admin/v1/qywx/tobe_developed_app/add_qywx_group_chat`,
    method: 'post',
  })
}
