import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function price_list() {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/price_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export function price_listExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/dnf_quote/dyeing/price_list',
    method: 'get',
    nameFile,
  })
}

// 详情
export function dyeingdetail() {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/detail',
    method: 'get',
  })
}

// 获取items
export function dyeingdetail_items() {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/detail_items',
    method: 'get',
  })
}

// 获取items(改)
export function priceBy_items() {
  return useRequest({
    url: '/admin/v1/dnf_quote/dyeing/price_by_items',
    method: 'get',
  })
}

// 供应商枚举
export function BusinessUnitSupplierEnumlist() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/enum_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取后整价目的返回数据
export function price_by_items() {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/price_by_items',
    method: 'get',
  })
}

// 原料颜色价目表
export function raw_matldyeingprice_list() {
  return useRequest({
    url: '/admin/v1/raw_matl_quote/dyeing/price_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export function raw_matl_quotedyeingprice_listExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/raw_matl_quote/dyeing/price_list',
    method: 'get',
    nameFile,
  })
}
