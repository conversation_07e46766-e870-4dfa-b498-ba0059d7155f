import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function situation_list() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function situation_list_export({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/dyeing_and_finishing/situation/list',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

export function GetFpmProcessInOrderItemListUseDyeEnum() {
  return useRequest({
    url: '/admin/v1/product/fpmProcessInOrder/getFpmProcessInOrderItemListUseDyeEnum',
    method: 'get',
  })
}

// 完成染整进度
export function situation_finish() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/finish',
    method: 'put',
  })
}

// 取消完成染整进度
export function situation_cancel_finish() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/cancel_finish',
    method: 'put',
  })
}

// 更新完成染整进度
export function situation_update() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/update',
    method: 'put',
  })
}

// 获取拆缸数据
export function item_detail() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/item_detail',
    method: 'get',
  })
}

// 拆缸提交
export function split_dyelot() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/split_dyelot',
    method: 'put',
  })
}

// 取消拆缸
export function split_dyelot_cancel() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/split_dyelot_cancel',
    method: 'put',
  })
}

// 作废
export function SituationCancel() {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/cancel',
    method: 'put',
  })
}
