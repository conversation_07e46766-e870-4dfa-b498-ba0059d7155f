import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmOtherDeliveryOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/getGfmOtherDeliveryOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmOtherDeliveryOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/getGfmOtherDeliveryOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmOtherDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/getGfmOtherDeliveryOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmOtherDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/addGfmOtherDeliveryOrder',
    method: 'post',
  })
}

// 更新数据
export const updateGfmOtherDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/updateGfmOtherDeliveryOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmOtherDeliveryOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/updateGfmOtherDeliveryOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmOtherDeliveryOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/updateGfmOtherDeliveryOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmOtherDeliveryOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/updateGfmOtherDeliveryOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmOtherDeliveryOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmOtherDeliveryOrder/updateGfmOtherDeliveryOrderStatusReject',
    method: 'put',
  })
}
