import { useRequest } from '@/use/useRequest'

// 获取列表
export function getFpmOtherInOrderList() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/getFpmOtherInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export function addFpmOtherInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/addFpmOtherInOrder',
    method: 'post',
  })
}

// 获取详情
export function getFpmOtherInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/getFpmOtherInOrder',
    method: 'get',
  })
}

// 作废
export function updateFpmOtherInOrderStatusCancel() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export function updateFpmOtherInOrderStatusPass() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export function updateFpmOtherInOrderStatusReject() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export function updateFpmOtherInOrderStatusWait() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusWait',
    method: 'put',
  })
}

// 更新
export function updateFpmOtherInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrder',
    method: 'put',
  })
}
