import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmStockCheckOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/getGfmStockCheckOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmStockCheckOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/getGfmStockCheckOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmStockCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/getGfmStockCheckOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmStockCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/addGfmStockCheckOrder',
    method: 'post',
  })
}

// 更新数据
export const updateGfmStockCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/updateGfmStockCheckOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmStockCheckOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/updateGfmStockCheckOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmStockCheckOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/updateGfmStockCheckOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmStockCheckOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/updateGfmStockCheckOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmStockCheckOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockCheckOrder/updateGfmStockCheckOrderStatusReject',
    method: 'put',
  })
}
