import { useRequest } from '@/use/useRequest'

// 获取列表
export function getPhysicalWarehouseList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/getTypeSettleAccountsList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export function addTypeIntercourseUnits() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/addTypeSettleAccounts',
    method: 'post',
  })
}

// 更新类型
export function updateTypeIntercourseUnits() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/updateTypeSettleAccounts',
    method: 'put',
  })
}

// 删除类型
export function deleteTypeIntercourseUnits() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/deleteTypeSettleAccounts',
    method: 'delete',
  })
}

// 修改状态
export function updateTypeIntercourseUnitsStatus() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/updateTypeSettleAccountsStatus',
    method: 'put',
  })
}

// 结算方式枚举列表
export function getSettleTypeEnum() {
  return useRequest({
    url: '/admin/v1/basic_data/enum/getSettleTypeEnum',
    method: 'get',
  })
}

// 单位枚举列表

export function getInfoBaseMeasurementUnitList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseMeasurementUnit/getInfoBaseMeasurementUnitEnumList',
    method: 'get',
  })
}

// 控件展示类型枚举

export function getShowTypeEnum() {
  return useRequest({
    url: '/admin/v1/basic_data/enum/getShowTypeEnum',
    method: 'get',
  })
}

// 原料类型枚举
export function getTypeWarehouseList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeRawMaterial/getTypeRawMaterialList',
    method: 'get',
  })
}

// 布种类型枚举列表

export function getTypeFabricList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/getTypeFabricEnumList',
    method: 'get',
  })
}

// 坯布订单类型枚举列表
export function getTypeGreyFabricOrderList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeGreyFabricOrder/getTypeGreyFabricOrderEnumList',
    method: 'get',
  })
}

// 织机机型枚举

export function getInfoProductLoomModelList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoProductLoomModel/getInfoProductLoomModelEnumList',
    method: 'get',
  })
}

// 胚布颜色枚举

export function getInfoProductGrayFabricColorList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoProductGrayFabricColor/getInfoProductGrayFabricColorEnumList',
    method: 'get',
  })
}

// 织造规格枚举

export function getInfoProductWeaveSpecificationList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoProductWeaveSpecification/getInfoProductWeaveSpecificationEnumList',
    method: 'get',
  })
}

// 原料资料类型
export function rawmateriallist() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/list',
    method: 'get',
  })
}

// 原料资料类型枚举
export function rawmaterialMenu() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/dropdownList',
    method: 'get',
  })
}

// 往来单位枚举
export function business_unitlist(config = {}) {
  return useRequest({
    url: '/admin/v1/business_unit/list',
    method: 'get',
    ...config,
  })
}

// 供应商列表
export function business_unitsupplierlist() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/list',
    method: 'get',
  })
}

// 供应商枚举
export function BusinessUnitSupplierEnumlist() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/enum_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 供应商枚举
export function BusinessUnitSupplierEnumAll() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/enum_list',
    method: 'get',
    pagination: false,
  })
}

// 坯布等级枚举
export function getInfoBaseGreyFabricLevelList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseGreyFabricLevel/getInfoBaseGreyFabricLevelList',
    method: 'get',
  })
}

// 单据状态枚举
export function getAuditStatusEnums() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/enum/getAuditStatusEnum',
    method: 'get',
  })
}

// 坯布仓库来源类型枚举
export function getSourceWarehouseTypeEnum() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/enum/getSourceWarehouseTypeEnum',
    method: 'get',
  })
}

// 获取坯布采购里面信息列表
export function GetPurchaseGreyFabricItemList() {
  return useRequest({
    url: '/admin/v1/purchase/purchaseGreyFarbric/getItemAddrEnumList',
    method: 'get',
  })
}

// 获取通知单列表
export function getProductionNotifyOrderList() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderList',
    method: 'get',
  })
}

// 坯布名称和编号枚举
export function GetGreyFabricInfoListUseByOthers() {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoListUseByOthers',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 计量单位
export function getInfoBaseMeasurementUnitEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseMeasurementUnit/getInfoBaseMeasurementUnitEnumList',
    method: 'get',
  })
}

// 色号枚举下拉列表
export function getFinishProductColorDropdownList(config) {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColorDropdownList',
    method: 'get',
    ...config,
  })
}

// 获取染整工艺编号枚举列表
export function getInfoDyeingFinishingProcessDataEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/getInfoDyeingFinishingProcessDataEnumList',
    method: 'get',
  })
}

// 含税项目枚举列表
export function GetInfoSaleTaxableItemEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleTaxableItem/getInfoSaleTaxableItemEnumList',
    method: 'get',
  })
}

// 出货类型下拉枚举
export function GetSendProductTypeReverseIntMap() {
  return useRequest({
    url: '/admin/v1/sale/enum/getSendProductTypeReverseIntMap',
    method: 'get',
  })
}

// 获取邮费项目枚举列表
export function GetPostageItemsReverseIntMap() {
  return useRequest({
    url: '/admin/v1/sale/enum/getPostageItemsReverseIntMap',
    method: 'get',
  })
}

// 获取默认结算类型枚举列表
export function GetSettleTypeReverseIntMap() {
  return useRequest({
    url: '/admin/v1/sale/enum/getSettleTypeReverseIntMap',
    method: 'get',
  })
}

// 根据客户id获取工厂物流
export function Logistics_enum_list() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/logistics_enum_list',
    method: 'get',
  })
}

// 色号下拉枚举
export function GetDyeingColorDetailsByProduct() {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getDyeingColorDetailsByProduct',
    method: 'get',
  })
}

// 订单类别下拉枚举列表
export function getInfoSaleOrderCategoryEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleOrderCategory/getInfoSaleOrderCategoryEnumList',
    method: 'get',
  })
}

// 染整进度枚举列表
export function GetInfoDyeingFinishingProgressEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProgress/getInfoDyeingFinishingProgressEnumList',
    method: 'get',
  })
}

// 染整库存单据类型枚举
export function GetGfmWarehouseTypeEnum() {
  return useRequest<void, Api.GetGfmWarehouseTypeEnum.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseTypeEnum',
    method: 'get',
  })
}

// 染费数量按枚举
export function Getdnfchargingmethodenum() {
  return useRequest({
    url: '/admin/v1/business_unit/dnf_charging_method/enum',
    method: 'get',
  })
}

// 收款账户下拉枚举 结算类型
export function GetTypeSettleAccountsEnumList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeSettleAccounts/getTypeSettleAccountsEnumList',
    method: 'get',
  })
}

// 计划类型
export function PushType() {
  return useRequest({
    url: '/admin/v1/sale/enum/pushType',
    method: 'get',
  })
}

// 染厂色号
export function GetDyeingColorDetailsByProductListEnum() {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getDyeingColorDetailsByProductListEnum',
    method: 'get',
  })
}

// 销售类型枚举
export function GetPlanTypeReverseIntMap() {
  return useRequest({
    url: '/admin/v1/sale/enum/getPlanTypeReverseIntMap',
    method: 'get',
  })
}

// 原料染厂颜色列表
export function GetListFactory() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_factory',
    method: 'get',
  })
}

// 染纱厂色号枚举
export function GetlistFactoryEnum() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/list_factory_enum',
    method: 'get',
  })
}

// 资金费用类型枚举
export function GetTypeCapitalExpensesEnumList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeCapitalExpenses/getTypeCapitalExpensesEnumList',
    method: 'get',
  })
}
