import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

//  从库存中添加
export const getGfmWarehouseSumList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取录入细码的数据
export const getGfmWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseList',
    method: 'get',
  })
}

// 获取列表页数据
export const getGfmPurchaseReturnList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/getGfmPurchaseReturnList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmPurchaseReturnListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/getGfmPurchaseReturnList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmPurchaseReturn = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/getGfmPurchaseReturn',
    method: 'get',
  })
}

// 新增数据
export const addGfmPurchaseReturn = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/addGfmPurchaseReturn',
    method: 'post',
  })
}

// 更新数据
export const updateGfmPurchaseReturn = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/updateGfmPurchaseReturn',
    method: 'put',
  })
}

// 消审
export const updateGfmPurchaseReturnStatus = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/updateGfmPurchaseReturnStatusWait',
    method: 'put',
  })
}

// 审核
export const UpdateGfmPurchaseReturnStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/updateGfmPurchaseReturnStatusPass',
    method: 'put',
  })
}

// 驳回
export const UpdateGfmPurchaseReturnStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/updateGfmPurchaseReturnStatusReject',
    method: 'put',
  })
}

// 作废
export const UpdateGfmPurchaseReturnStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReturn/updateGfmPurchaseReturnStatusCancel',
    method: 'put',
  })
}

// 选择采购订单枚举列表
export const getPurchaseGreyFabricItemList = () => {
  return useRequest({
    url: '/admin/v1/purchase/purchaseGreyFarbric/getItemAddrEnumList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 用纱信息列表
export const list_enum = () => {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_enum',
    method: 'get',
  })
}

// 用坯信息汇总数据
export const getGfmProduceReceiveOrderUseYarnItemListByOrderID = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrderUseYarnItemListByOrderID',
    method: 'get',
  })
}

//  从库存获取
export const GetGfmWarehouseSummaryListEnum = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseSummaryListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
