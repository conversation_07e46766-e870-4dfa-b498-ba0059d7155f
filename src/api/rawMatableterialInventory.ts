import { useRequest } from '@/use/useRequest'

// 获取原料等级下拉
export const GetInfoBaseRawMaterialLevelEnumList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseRawMaterialLevel/getInfoBaseRawMaterialLevelEnumList',
    method: 'get',
  })
}

// 获取原料库存列表
export const GetGawMaterialList = () => {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取原料库存列表枚举
export const GetGawMaterialListMenu = () => {
  return useRequest({
    url: '/admin/v1/stock/raw_material/list_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取原料库存记录列表
export const GetRawMaterialRecordList = () => {
  return useRequest({
    url: '/admin/v1/stock/raw_material/record_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
