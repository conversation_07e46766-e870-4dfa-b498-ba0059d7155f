import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmProcessOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/getFpmProcessOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmProcessOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/addFpmProcessOutOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmProcessOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/getFpmProcessOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmProcessOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/updateFpmProcessOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmProcessOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/updateFpmProcessOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmProcessOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/updateFpmProcessOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmProcessOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/updateFpmProcessOutOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmProcessOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/updateFpmProcessOutOrder',
    method: 'put',
  })
}

// 加工出仓类型
export const GetProcessOutTypeTypeEnum = () => {
  return useRequest({
    url: '/admin/v1/product/enum/getProcessOutTypeTypeEnum',
    method: 'get',
  })
}
