import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function GetSaleLevelList() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/getSaleLevelList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function GetSaleLevelListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/sale_price/saleLevel/getSaleLevelList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 删除销售等级
export function DeleteSaleLevel() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/deleteSaleLevel',
    method: 'delete',
  })
}

// 更新销售等级
export function UpdateSaleLevel() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/updateSaleLevel',
    method: 'put',
  })
}

// 更新销售等级状态
export function UpdateSaleLevelStatus() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/updateSaleLevelStatus',
    method: 'put',
  })
}

// 新增销售等级
export function AddSaleLevel() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/addSaleLevel',
    method: 'post',
  })
}

// 获取销售等级
export function GetSaleLevel() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/getSaleLevel',
    method: 'get',
  })
}

// 获取销售等级
export function GetSaleLevelDropdownList() {
  return useRequest({
    url: '/admin/v1/sale_price/saleLevel/getSaleLevelDropdownList',
    method: 'get',
  })
}
