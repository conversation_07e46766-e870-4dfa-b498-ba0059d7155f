import { useRequest } from '@/use/useRequest'

// 获取列表
export const product_purlist = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const product_purdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/detail',
    method: 'get',
  })
}

// 审核
export const product_purpass = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/pass',
    method: 'put',
  })
}

// 消审
export const product_purcancel = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/cancel',
    method: 'put',
  })
}

// 驳回
export const product_purreject = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/reject',
    method: 'put',
  })
}

// 作废
export const product_purvoid = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur/void',
    method: 'put',
  })
}

//  更新
export const product_purput = () => {
  return useRequest({
    url: '/admin/v1/payable/product_pur',
    method: 'put',
  })
}
