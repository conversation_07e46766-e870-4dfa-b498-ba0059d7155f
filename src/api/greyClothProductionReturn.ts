import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmProduceReturnOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/getGfmProduceReturnOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmProduceReturnOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/getGfmProduceReturnOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmProduceReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/getGfmProduceReturnOrder',
    method: 'get',
  })
}

// 新建数据
export const addGfmProduceReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/addGfmProduceReturnOrder',
    method: 'post',
  })
}

// 更新数据
export const updateGfmProduceReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/updateGfmProduceReturnOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmProduceReturnOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/updateGfmProduceReturnOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmProduceReturnOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/updateGfmProduceReturnOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmProduceReturnOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/updateGfmProduceReturnOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmProduceReturnOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/updateGfmProduceReturnOrderStatusReject',
    method: 'put',
  })
}

// 获取通知单列表
export const getProductionNotifyOrderList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
