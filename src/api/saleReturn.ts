import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export const productReturnlist = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const productReturnlistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/should_collect_order/productReturn/getList',
    method: 'get',
    nameFile,
  })
}

// 审核
export const updateAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/updateAuditStatusPass',
    method: 'put',
  })
}

// 消审
export const updateAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/updateAuditStatusWait',
    method: 'put',
  })
}

// 驳回
export const updateAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/updateAuditStatusReject',
    method: 'put',
  })
}

// 作废
export const updateAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/updateAuditStatusCancel',
    method: 'put',
  })
}

// 更新
export const updateput = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/update',
    method: 'put',
  })
}

// 详情
export const updateget = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/get',
    method: 'get',
  })
}

// 获取细码接口
export const getWeightItemList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productReturn/getWeightItemList',
    method: 'get',
  })
}
