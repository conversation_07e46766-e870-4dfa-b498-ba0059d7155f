import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 原料调拨单列表
export const AllocateOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 原料调拨单详情
export const AllocateOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/detail',
    method: 'get',
  })
}

// 原料调拨单添加
export const AllocateOrderAdd = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order',
    method: 'post',
  })
}

// 原料调拨单修改
export const AllocateOrderEdit = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order',
    method: 'put',
  })
}

// 原料调拨审核
export const AllocateOrderPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/pass',
    method: 'put',
  })
}

// 原料调拨驳回
export const AllocateOrderReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/reject',
    method: 'put',
  })
}
// 原料调拨作废
export const AllocateOrderVoid = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/void',
    method: 'put',
  })
}
// 原料调拨作废
export const AllocateOrderCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/allocate_order/cancel',
    method: 'put',
  })
}

// 导出数据
export const AllocateOrderListDownLoad = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_material/allocate_order/list',
    method: 'get',
    nameFile,
  })
}
