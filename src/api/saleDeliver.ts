import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export const productSalelist = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const productSalelistExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/should_collect_order/productSale/getList',
    method: 'get',
    nameFile,
  })
}

// 审核
export const updateAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/updateAuditStatusPass',
    method: 'put',
  })
}

// 消审
export const updateAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/updateAuditStatusWait',
    method: 'put',
  })
}

// 驳回
export const updateAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/updateAuditStatusReject',
    method: 'put',
  })
}

// 作废
export const updateAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/updateAuditStatusCancel',
    method: 'put',
  })
}

// 更新
export const updateput = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/update',
    method: 'put',
  })
}

// 详情
export const updateget = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/get',
    method: 'get',
  })
}

// 获取细码接口
export const getWeightItemList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getWeightItemList',
    method: 'get',
  })
}

// 合并送货单
export const mergeItems = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/mergeItems',
    method: 'put',
  })
}

// 撤销合并
export const breakUpItems = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/breakUpItems',
    method: 'put',
  })
}

// 合并单列表数据
export const getProductSaleShouldCollectOrderDropdownList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取已合并列表
export const getMergeOrderList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getMergeOrderList',
    method: 'get',
  })
}
