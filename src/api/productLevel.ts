import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/getInfoBaseFinishedProductLevelList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export const addTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/addInfoBaseFinishedProductLevel',
    method: 'post',
  })
}

// 更新类型
export const updateTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/updateInfoBaseFinishedProductLevel',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/deleteInfoBaseFinishedProductLevel',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/updateInfoBaseFinishedProductLevelStatus',
    method: 'put',
  })
}
