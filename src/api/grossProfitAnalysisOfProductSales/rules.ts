export interface GetGrossProfitGroupMaterialListData {
  material_id: number
  material_code: string
  material_name: string
  material_code_and_name: string
  measurement_unit_id: number
  measurement_unit_code: string
  measurement_unit_name: string
  roll: number
  weight: number
  out_total_cost: number
  sale_total_price: number
  sale_avg_price: number
  unit_gross_profit: number
  gross_profit: number
}

export interface GetGrossProfitGroupColorListData {
  color_code: string
  color_code_and_name: string
  color_id: number
  color_name: string
  material_id: number
  material_code: string
  material_name: string
  material_code_and_name: string
  measurement_unit_id: number
  measurement_unit_code: string
  measurement_unit_name: string
  roll: number
  weight: number
  out_total_cost: number
  sale_total_price: number
  sale_avg_price: number
  unit_gross_profit: number
  gross_profit: number
}

export interface GetGrossProfitGroupCustomerListData {
  customer_id: number
  customer_name: string
  gross_profit: number
  out_total_cost: number
  sale_total_price: number
}

export interface GetGrossProfitGroupSellerListData {
  gross_profit: number
  out_total_cost: number
  sale_total_price: number
  seller_id: number
  seller_name: string
}
