import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmSaleReturnInOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/getFpmSaleReturnInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmSaleReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/addFpmSaleReturnInOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmSaleReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/getFpmSaleReturnInOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmSaleReturnInOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/updateFpmSaleReturnInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmSaleReturnInOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/updateFpmSaleReturnInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmSaleReturnInOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/updateFpmSaleReturnInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmSaleReturnInOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/updateFpmSaleReturnInOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmSaleReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleReturnInOrder/updateFpmSaleReturnInOrder',
    method: 'put',
  })
}

// 获取销售送货单列表
export const getProductDropdownList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getProductDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
