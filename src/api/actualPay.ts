import { useRequest } from '@/use/useRequest'

// 获取列表
export const actuallyPayOrderList = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/getActuallyPayOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const actuallyPayOrderdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/getActuallyPayOrder',
    method: 'get',
  })
}

// 审核
export const actuallyPayOrderpass = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/updateActuallyPayOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const actuallyPayOrdercancel = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/updateActuallyPayOrderStatusWait',
    method: 'put',
  })
}

// 驳回
export const actuallyPayOrderreject = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/updateActuallyPayOrderStatusReject',
    method: 'put',
  })
}

// 作废
export const actuallyPayOrdervoid = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/updateActuallyPayOrderStatusCancel',
    method: 'put',
  })
}

//  更新
export const updateactuallyPayOrder = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/updateActuallyPayOrder',
    method: 'put',
  })
}

//  新增
export const actuallyPayOrderpost = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/addActuallyPayOrder',
    method: 'post',
  })
}

// 核销明细枚举
export const list_enum = () => {
  return useRequest({
    url: '/admin/v1/payable/enum/list_enum',
    method: 'get',
  })
}

// 使用预付款枚举接口
export const getAdvancePayOrderListEnum = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/getAdvancePayOrderListEnum',
    method: 'get',
  })
}

// 别的单据获取核销明细的接口
export const getPayOrderWriteOffList = () => {
  return useRequest({
    url: '/admin/v1/payable/actuallyPayOrder/getPayOrderWriteOffList',
    method: 'get',
  })
}

// 实付自动核销
export const GetAutoWriteOffList = () => {
  return useRequest({
    url: '/admin/v1/payable/enum/getAutoWriteOffList',
    method: 'get',
  })
}
