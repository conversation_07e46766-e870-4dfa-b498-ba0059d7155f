import { type ResponseList } from '@/api/commonTs/index'
import { useRequest } from '@/use/useRequest'
import { type Type_basic_dataGetTypeFabricTreeData } from './rule'

// 获取列表
export const getPhysicalWarehouseList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/getTypeFabricList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export const addTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/addTypeFabric',
    method: 'post',
  })
}

// 更新类型
export const updateTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/updateTypeFabric',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/deleteTypeFabric',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/updateTypeFabricStatus',
    method: 'put',
  })
}

// 获取树状列表
export const getPhysicalWarehouseListTree = () => {
  return useRequest<any, ResponseList<Type_basic_dataGetTypeFabricTreeData>>({
    url: '/admin/v1/basic_data/typeFabric/getTreeList',
    method: 'get',
  })
}

// 获取树状枚举
export const getPhysicalWarehouseListTreeEnum = () => {
  return useRequest<any, ResponseList<Type_basic_dataGetTypeFabricTreeData>>({
    url: '/admin/v1/basic_data/typeFabric/getTreeEnumList',
    method: 'get',
  })
}
