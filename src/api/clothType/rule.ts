export interface Type_basic_dataGetTypeFabricTreeData {
  /** 编号 */
  code?: string
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 深度 */
  deep?: number
  /** 删除的备注 */
  delete_remark?: string
  /** 记录ID */
  id?: number
  /** 名称 */
  name?: string
  /** 父部门ID */
  parent_id?: number
  /** 类型成品数 */
  product_num?: number
  /** 备注 */
  remark?: string
  /** 状态 */
  status?: any
  /** 状态 */
  status_name?: string
  /** 子类型 */
  sub_type_fabric?: Type_basic_dataGetTypeFabricTreeData[]
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
}
