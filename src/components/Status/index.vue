<template>
  <div :class="['base', `base-${props.type}`, props.circle ? 'base--circle' : null, props.plain ? `base-${props.type}--plain` : null]">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
export type Status = 'success' | 'primary' | 'danger' | 'info'

interface StatusProps {
  type: Status
  circle: boolean
  plain: boolean
}

const props = withDefaults(defineProps<StatusProps>(), {
  type: 'success',
  plain: false,
  circle: true,
})
</script>

<style scoped lang="scss">
.base {
  width: 60px;
  border-radius: 10px;
  text-align: center;

  &-success {
    background-color: #d1f2e2;
    color: #3fbd81;

    &--plain {
      border: 1px solid #3fbd81;
    }
  }

  &-primary {
    background-color: #e6f2ff;
    color: #4d94ff;

    &--plain {
      border: 1px solid #4d94ff;
    }
  }

  &-danger {
    background-color: #f3ecf2;
    color: #f47584;

    &--plain {
      border: 1px solid #f47584;
    }
  }

  &-info {
    background-color: #f4f4f5;
    color: #999ca1;

    &--plain {
      border: 1px solid #999ca1;
    }
  }

  &--circle {
    border-radius: 50px;
  }
}
</style>
