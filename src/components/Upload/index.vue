<template>
  <div class="uploader-content" @click="handleClick">
    <input ref="inputRef" class="uploader-target" :name="name" :multiple="multiple" :accept="props.accept" type="file" @change="handleChange" v-if="props.uploadMode != 'click'" @click.stop />

    <div
      class="uploader-paste"
      v-if="props.uploadMode == 'paste'"
      :class="['dragger', dragover ? '' : '']"
      @mouseover.stop="clipboardover = true"
      @mouseleave.stop="clipboardover = false"
      @drop.prevent="onDrop"
      @dragover.prevent="onDragover"
      @dragleave.prevent="dragover = false"
      @paste="pasteFun"
    >
      <template v-if="$slots.default == null">
        <div class="dragicon-box">
          <span>+</span>
        </div>
      </template>
      <slot />
    </div>
  </div>
</template>

<script setup>
import { shallowRef } from 'vue';
const inputRef = shallowRef(null)
interface UploadFilePropsType {
    limit: boolean,
    multiple: boolean,
    accept: string,
}
const props = withDefaults(defineProps<UploadFilePropsType>(), {

})
// 上传文件
const uploadFiles = files => {
  if (files.length === 0) return
  const { limit, multiple, accept } = props
  // 是否多文件限制，主要用于拖拽和粘贴上传中
  if (!multiple) {
    files = Array.from(files).slice(0, 1)
  }
  // 文件数量
  if (limit && files.length > limit) {
    /* 具体大家需要的逻辑可自行定义 */
    return
  }
  // 文件类型限制
  if (accept) {
    files = filesFiltered(Array.from(files), accept)
  }
  // 在文件符合条件后执行上传方法
}
// 文件过滤
const filesFiltered = (files, accept) => {
  return files.filter(file => {
    const { type, name } = file
    const extension = name.includes('.') ? `.${name.split('.').pop()}` : ''
    const baseType = type.replace(/\/.*$/, '')
    return accept
      .split(',')
      .map(type => type.trim())
      .filter(type => type)
      .some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return extension === acceptedType
        }
        if (/\/*$/.test(acceptedType)) {
          return baseType === acceptedType.replace(/\/*$/, '')
        }
        if (/^[^/]+\/[^/]+$/.test(acceptedType)) {
          type === acceptedType
        }
        return false
      })
  })
}
</script>
