<template>
  <!-- 防止在flex的情况下‘暂无数据’这四个字竖向排列 -->
  <div>
    <el-tree
      v-loading="loading"
      :data="state.treeData"
      ref="authTree"
      :default-checked-keys="state.selectTreeIds"
      :node-key="props.nodeKey"
      :props="defaultProps"
      show-checkbox
      @check="handleCheckChange"
    >
      <template #default="{ node }">
        <slot :node="node">
          <span>{{ node.label }}</span>
        </slot>
      </template>
    </el-tree>
  </div>
</template>
<script lang="ts" setup>
import { GetMenuResourceListApi } from '@/api/menu'
import { reactive, ref, watch } from 'vue'

export interface Props {
  selectTreeIds: number[] // 默认选中的id
  selectType: boolean // 是否要半选中的状态
  nodeKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectTreeIds: () => [],
  selectType: true,
  nodeKey: 'key_id',
})

const state = reactive({
  treeData: [],
  selectTreeIds: [] as any,
  role_access: {
    menu_id: [] as number[],
    resource_id: [] as number[],
    resource_router_names: [] as string[],
    button_codes: [] as string[],
    key_id: [] as string[],
  },
})
const emits = defineEmits(['change'])

// 获取后台权限
const { fetchData: fetchDataMenu, data: dataMenu, loading } = GetMenuResourceListApi()
const getMenu = async () => {
  await fetchDataMenu({ show_children: true })
  state.treeData = formatedTreeData(dataMenu.value.list)
}

// 格式化pc端菜单与权限
// const formatedTreeData = (val: any) => {
//   val?.map((item: any) => {
//     if ((!item.sub_menu || item.sub_menu.length <= 0) && item.resource_list?.length > 0) {
//       item.sub_menu = item.resource_list
//     }
//     if (item.resource_list?.length <= 0 && item.resource_id && state.selectTreeIds.includes(item.key_id)) {
//       state.selectTreeIds.push(item.key_id)
//     }
//     if (item.sub_menu && item.sub_menu.length > 0) {
//       formatedTreeData(item.sub_menu)
//     }
//   })
//

//   return val
// }

// 格式化pc端菜单与权限
const formatedTreeData = (val: any) => {
  val?.map((item: any) => {
    if ((!item.sub_menu || item.sub_menu.length <= 0) && item.resource_list?.length > 0) {
      item.sub_menu = item.resource_list
    }
    if (item.resource_list?.length > 0 && item.resource_id) {
      const index = state.selectTreeIds.indexOf(item.key_id)
      if (index !== -1) {
        state.selectTreeIds?.splice(index, 1)
      }
    }
    if (item.sub_menu && item.sub_menu.length > 0) {
      formatedTreeData(item.sub_menu)
    }
  })

  return val
}

watch(
  () => props.selectTreeIds,
  () => {
    state.selectTreeIds = props.selectTreeIds
    getMenu()
  },
  { immediate: true }
)

const authTree = ref()
const handleCheckChange = () => {
  const nodes = authTree.value.getCheckedNodes(false, props.selectType)
  state.role_access.menu_id = []
  state.role_access.resource_id = []
  state.role_access.resource_router_names = []
  state.role_access.button_codes = []
  state.role_access.key_id = []
  nodes?.map((item: any) => {
    if (!item.button_code) {
      state.role_access.menu_id.push(item.id)
    }
    if (item.resource_id) {
      state.role_access.resource_id.push(item.resource_id)
      state.role_access.key_id.push(item.key_id)
    }
    if (item.resource_router_name) {
      state.role_access.resource_router_names.push(item.resource_router_name)
    }
    if (item.button_code) {
      state.role_access.button_codes.push(item.button_code)
      state.role_access.resource_id.push(item.id)
      state.role_access.key_id.push(item.key_id)
    }
  })
  emits('change', state.role_access)
}

const defaultProps = {
  children: 'sub_menu',
  value: 'id',
  label: 'name',
}
</script>
