<script setup lang="ts" name="AITextarea">
import { ref } from 'vue'
import {
  EventStreamContentType,
  fetchEventSource,
} from '@fortaine/fetch-event-source'
import { CloseBold, RefreshLeft } from '@element-plus/icons-vue'
import FlashButton from '@/components/AITextarea/components/FlashButton.vue'
import Loader from '@/components/AITextarea/components/Loader.vue'
import Textarea from '@/components/AITextarea/components/Textarea.vue'
import Output from '@/components/AITextarea/components/Output.vue'
import { baseURL } from '@/common/uploadExel'
import { getFilterData } from '@/common/util'

const props = withDefaults(defineProps<{
  loading?: boolean
  placeholder?: string
  text?: string
  type: 'output' | 'input' // output 输出框 textarea 输入框
  outputParams?: Record<string, any>
}>(), {
  placeholder: '输入或黏贴下单内容',
  loading: false,
  type: 'input',
  text: 'AI识别下单',
})
const emit = defineEmits(['submit'])
const value = defineModel<string>('modelValue', { required: false })
// const showFullScreen = ref(false)
// function handFullScreen() {
//   showFullScreen.value = !showFullScreen.value
// }
function handleSubmit() {
  if (!value.value)
    return
  emit('submit', value.value)
}
const isSSELoading = ref(false)

function handleClickButton() {
  isSSELoading.value = true
  const params = new URLSearchParams(getFilterData(props.outputParams))
  connectSSE(`${baseURL}/admin/v1/ai/analysis/sale?${params.toString()}`)
}

function prettyObject(msg: any) {
  const obj = msg
  if (typeof msg !== 'string')
    msg = JSON.stringify(msg, null, '  ')

  if (msg === '{}')
    return obj.toString()

  if (msg.startsWith('```json'))
    return msg

  return ['```json', msg, '```'].join('\n')
}
const showPopover = ref(false)
const OutputRef = ref()
const controller = new AbortController()
const REQUEST_TIMEOUT_MS = 60000
// 处理 SSE 连接
function connectSSE(url: string) {
  // make a fetch request
  const requestTimeoutId = setTimeout(
    () => controller.abort(),
    REQUEST_TIMEOUT_MS,
  )
  const token = localStorage.getItem('token')
  const chatPayload = {
    method: 'GET',
    signal: controller.signal,
    headers: {
      'Content-Type': 'application/json',
      'Platform': '1',
      'Authorization': `${token}`,
    },
  }

  let responseText = ''
  let remainText = ''
  let finished = false
  let responseRes: Response
  // animate response to make it looks smooth
  function animateResponseText() {
    if (finished || controller.signal.aborted) {
      responseText += remainText

      if (responseText?.length === 0)
        OutputRef.value.onError?.(new Error('empty response from server'))

      return
    }

    if (remainText.length > 0) {
      const fetchCount = Math.max(1, Math.round(remainText.length / 60))
      const fetchText = remainText.slice(0, fetchCount)
      responseText += fetchText
      remainText = remainText.slice(fetchCount)
      OutputRef.value.onUpdate?.(responseText, fetchText)
    }

    requestAnimationFrame(animateResponseText)
  }
  animateResponseText()
  const finish = () => {
    if (!finished) {
      finished = true
      OutputRef.value.onFinish(responseText + remainText, responseRes)
    }
  }

  controller.signal.onabort = finish
  fetchEventSource(url, {
    fetch: fetch as any,
    ...chatPayload,
    async onopen(res) {
      isSSELoading.value = false
      showPopover.value = true
      clearTimeout(requestTimeoutId)
      const contentType = res.headers.get('content-type')

      responseRes = res
      if (contentType?.startsWith('text/plain')) {
        responseText = await res.clone().text()
        return finish()
      }
      if (
        !res.ok
        || !res.headers
          .get('content-type')
          ?.startsWith(EventStreamContentType)
          || res.status !== 200
      ) {
        const responseTexts = [responseText]
        let extraInfo = await res.clone().text()
        try {
          const resJson = await res.clone().json()
          extraInfo = prettyObject(resJson)
        }
        catch { }

        if (res.status === 401)
          responseTexts.push('请先登录')

        if (extraInfo)
          responseTexts.push(extraInfo)

        responseText = responseTexts.join('\n\n')
        return finish()
      }
    },
    onmessage(msg) {
      if (msg.data === '[DONE]' || finished)
        return finish()

      const text = msg.data
      try {
        const json = JSON.parse(text)
        const choices = json.choices as Array<{
          delta: { content: string }
        }>
        const delta = choices[0]?.delta?.content
        if (delta)
          remainText += delta
      }
      catch (e) {
        console.error('[Request] parse error', text, msg)
      }
    },
    onclose() {
      finish()
    },
    onerror(e) {
      OutputRef.value.onError?.(e)
      throw e
    },
    openWhenHidden: true,
  })
}
</script>

<template>
  <el-dropdown v-if="type === 'input'" placement="top-start" trigger="click">
    <FlashButton
      class="button"
      :disabled="loading"
      :loading="loading"
    >
      <template v-if="!loading">
        <el-icon size="20px">
          <Promotion />
        </el-icon>
        {{ text }}
      </template>
      <template v-else>
        <Loader :size="16" color="#fff" class="mr-2" />
        思考中...
      </template>
    </FlashButton>
    <template #dropdown>
      <div class="AITextarea-card">
        <Textarea
          v-model="value"
          :placeholder="placeholder"
          @submit="handleSubmit"
        />
      </div>
    </template>
  </el-dropdown>
  <template v-else>
    <el-popover
      popper-class="custom-popover"
      :visible="showPopover"
      :width="500"
      :teleported="false"
      trigger="manual"
      placement="bottom-start"
    >
      <div class="popover-content">
        <Output ref="OutputRef" />
        <div class="popover-header">
          <div class="header-actions">
            <el-button
              class="action-btn"
              type="primary"
              :icon="RefreshLeft"
              link
              @click="handleClickButton"
            >
              重新分析
            </el-button>
            <el-button
              class="action-btn"
              type="primary"
              :icon="CloseBold"
              link
              @click="showPopover = false"
            >
              关闭
            </el-button>
          </div>
        </div>
      </div>
      <template #reference>
        <FlashButton
          class="button"
          :disabled="isSSELoading"
          :loading="isSSELoading"
          @click="handleClickButton"
        >
          <template v-if="!isSSELoading">
            <el-icon size="20px">
              <Promotion />
            </el-icon>
            {{ text }}
          </template>
          <template v-else>
            <Loader :size="16" color="#fff" class="mr-2" />
            思考中...
          </template>
        </FlashButton>
      </template>
    </el-popover>
  </template>
</template>

<style>
.custom-popover {
  background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB) !important;

}
.custom-popover .el-popper__arrow::before{
  background-image: linear-gradient(144deg,#a43ffa, #a43ffa 50%,#a43ffa) !important;
  border: none !important;
  background-color: transparent!important;
}
</style>

<style scoped lang="scss">
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: "阿里妈妈数黑体 Bold";font-weight: 700;src: url("//at.alicdn.com/wf/webfont/0wfGzkRxGTr2/MzrM8WGQ8fVy.woff2") format("woff2"),
url("//at.alicdn.com/wf/webfont/0wfGzkRxGTr2/8kj449JpI58L.woff") format("woff");
  font-display: swap;
}

.popover-content {
  position: relative;
  padding-bottom: 32px;
}

.popover-header {
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  height: 32px;
  padding: 4px 8px;
  padding-bottom: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.close-btn {
  font-size: 14px;
}
.header-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.action-btn {
  font-size: 14px;
  padding: 0;
  color: white;
}
.button{
  border-radius: 4px;
  padding-left: 10px;
  padding-right: 10px;
  //width: 120px;
  height: 30px;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  font-family: "阿里妈妈数黑体 Bold";
}

:deep(.el-textarea__inner){
  background: #fff;
  box-shadow: none;
  outline: none;
  height: 100%;
  padding: 0;
  &:focus{
    box-shadow: none;
  }
  &:hover{
    box-shadow: none;
  }
}
.textarea-full-screen :deep(.el-textarea__inner){
  height: 80vh;
}
.AITextarea-card {
  width: 240px;
  height: 254px;
  border-radius: 4px;
  padding: 5px ;
  box-shadow: rgba(151, 65, 252, 0.2) 0 15px 30px -5px;
  background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB);
}

.el-popper :deep(.el-popper__arrow){
  &:before{
  background: #8c41fa !important;
  }
}
</style>
