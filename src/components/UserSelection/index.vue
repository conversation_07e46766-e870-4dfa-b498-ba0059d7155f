<template>
  <vxe-modal
    v-model="relationOpenStatus"
    type="confirm"
    title="关联员工"
    width="800"
    :mask="true"
    :lock-view="true"
    :before-hide-method="beforeHideMethod"
    esc-closable
    draggable
    resize
    show-footer
    @close="closeEven"
  >
    <template #default>
      <div class="relation_main">
        <div class="relation_header flex">
          <div class="relation_header_left flex-1 flex items-center">
            <el-input v-model="searchUser" placeholder="请输入关键字" :suffix-icon="Search" clearable />
          </div>
          <div class="relation_header_right flex-1 flex items-center justify-between">
            <div>已选: {{ selectList.length }} 个员工</div>
            <div class="clear" @click="clearAll">清空</div>
          </div>
        </div>
        <div class="relation_list flex">
          <div class="relation_list_left">
            <div class="relation_list_left_header flex items-center justify-between">
              <h2>按组织框架查看</h2>
              <el-icon><ArrowRightBold /></el-icon>
            </div>
            <div ref="scrollRef" class="relation_list_left_con">
              <ul>
                <li v-for="(item, index) in userList" :key="index" ref="itemRef">
                  <div class="relation_list_left_con_tag">{{ item.tag }}</div>
                  <div v-for="citem in item.children" :key="citem?.[props.config.id]" class="relation_list_left_con_name flex items-center hover:bg-slate-50">
                    <el-checkbox v-model="citem.check" :disabled="citem.existIdStatus" size="large" @change="changeCheckbox(citem, $event)">
                      <template #default>
                        <div class="flex items-center hover:bg-slate-50">
                          <div class="ml-3 mr-3">
                            <el-avatar :icon="UserFilled" />
                          </div>
                          <div class="user_name flex flex-col">
                            <span :class="citem.existIdStatus && 'select_color'">{{ citem?.[props.config.name] }}</span>
                            <span>{{ citem?.[props.config.department_name] }}</span>
                          </div>
                        </div>
                      </template>
                    </el-checkbox>
                  </div>
                </li>
              </ul>
            </div>
            <div ref="letterListRef" class="letter_list">
              <ul>
                <li v-for="item in retterList" :key="item" :style="{ width: `${letterSize}px`, height: `${letterSize}px` }" @click="letterClick(item)">
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
          <div class="relation_list_right">
            <div v-for="citem in selectList" :key="citem.id" class="relation_list_right_con_name flex items-center">
              <div class="ml-3 mr-3">
                <el-avatar :icon="UserFilled" />
              </div>
              <div class="user_name flex flex-col flex-1">
                <span>{{ citem?.[props.config.name] }}</span>
                <span>{{ citem?.[props.config.department_name] }}</span>
              </div>
              <div class="delete_user" @click="deltUser(citem)">删除</div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end mt-3">
        <el-button @click="closeEven">取消</el-button>
        <el-button type="primary" @click="submitBind">确定</el-button>
      </div>
    </template>
  </vxe-modal>
</template>
<script lang="ts"></script>
<script setup lang="ts">
import BScroll from '@better-scroll/core'
import MouseWheel from '@better-scroll/mouse-wheel'
import ObserveDOM from '@better-scroll/observe-dom'
import { Search, UserFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { pinyin } from 'pinyin-pro'
import { computed, nextTick, ref, watch } from 'vue'
import { VxeModalPropTypes } from 'vxe-pc-ui'

interface PropsType {
  modelValue: boolean
  type: 'checkbox' | 'radio'
  existId: number[]
  list: any[]
  config: {
    name: string
    id: string
    department_name: string
  }
}
const props = withDefaults(defineProps<PropsType>(), {
  modelValue: false,
  type: 'checkbox',
  existId: () => [],
  list: () => [],
  config: () => ({
    name: 'real_name',
    id: 'id',
    department_name: 'department_name',
  }),
})
const emits = defineEmits(['update:modelValue', 'successBind'])

const list = ref<any[]>([])

// 监听是否显示
const relationOpenStatus = ref(false)

watch(
  () => props.modelValue,
  value => {
    relationOpenStatus.value = value
    list.value = props.list || []
    console.log('modelValue list', list.value)
    value && letterListAddEvent()
  }
)

// 搜索员工
const searchUser = ref('')

// 整理用户数据格式
const letterSize = ref(20)
const retterList = ref('ABCDEFGHIJKLMNOPQRSTUVWXYZ#')
const userList = ref<any[]>([])
watch(
  () => [list.value, searchUser.value, props.existId, relationOpenStatus],
  ([value, oldValue]) => {
    if (!relationOpenStatus.value) return false
    const lists: Record<string, any> = {}
    ;(value as any[])?.map((item: any) => {
      if (!item?.[props.config.name]?.includes(searchUser.value)) return false

      const initials = pinyin(item?.[props.config.name], { pattern: 'first', type: 'array', v: true })
      const initials_up = retterList.value.includes(initials[0].toUpperCase()) ? initials[0].toUpperCase() : '#'
      if (!lists[initials_up]) {
        lists[initials_up] = {
          tag: initials_up,
          children: [{ ...item, existIdStatus: props.existId.includes(item?.[props.config.id]) }],
        }
      } else {
        lists[initials_up].children.push({
          ...item,
          existIdStatus: props.existId.includes(item?.[props.config.id]),
        })
      }
    })
    userList.value = Object.values(lists).sort((a, b) => {
      return b.tag > a.tag ? -1 : 1
    })
  }
)

watch(
  () => userList.value,
  () => {
    nextTick(() => {
      if (!scroll.value) {
        getScrollRef()
      } else {
        scroll.value.refresh()
      }
    })
  }
)

const closeEven = () => {
  clearAll()
  userList.value = []
  list.value = []
  emits('update:modelValue', false)
}

const beforeHideMethod: VxeModalPropTypes.BeforeHideMethod = () => {
  closeEven()
  return Promise.resolve()
}

// 寻找对应的节点
const letterClick = (value: any) => {
  const index = userList.value.findIndex(item => item.tag === value)
  if (index !== -1) scroll.value.scrollToElement(itemRef.value[index])
}

// 鼠标事件
const letterListRef = ref<any>()
const letterListAddEvent = async () => {
  await nextTick()
  if (letterListRef.value) {
    letterListRef.value.onselectstart = function () {
      return false
    }
    letterListRef.value.onmousedown = function () {
      letterListRef.value.onmousemove = function (e: any) {
        const index = e.layerY > 0 ? Math.ceil(e.layerY / letterSize.value) : 1
        letterClick(retterList.value[index - 1])
      }
    }
    letterListRef.value.onmouseup = function () {
      letterListRef.value.onmousemove = null
    }
  }
}

// 滚动相关
const scroll = ref<any>(null)
const scrollRef = ref<any>(null)
const itemRef = ref<any>(null)
BScroll.use(MouseWheel)
BScroll.use(ObserveDOM)
const getScrollRef = () => {
  scroll.value = new BScroll(scrollRef.value, {
    scrollY: true,
    probetype: 3,
    observeDOM: true,
    mouseWheel: {
      speed: 20,
      invert: false,
      easeTime: 300,
    },
  })
}

// 获取选中的数据
const selectListObj = ref<Record<string, any>>({})

// const handleClickItem = (item: any) => {
//   if (item.existIdStatus) return
//   console.log('handleClickItem')
//   item.check = !item.check
//   if (props.type === 'radio') {
//     handleRadio(item)
//   }
//   handleCheck(item)
// }
// 处理单选逻辑
const handleRadio = (value: any) => {
  userList.value = userList.value.map(item => {
    item.children = item.children.map((citem: any) => {
      console.log('item', citem)
      if (value.id !== citem.id) {
        citem.check = false
      }
      return citem
    })
    return item
  })
  selectListObj.value = {}
}
const handleCheck = (value: any) => {
  if (value.check) {
    selectListObj.value[value.id] = value
  } else {
    delete selectListObj.value[value.id]
  }
}
const changeCheckbox = (value: any, $event: any) => {
  $event.stopPropagation?.()
  console.log('value', value)
  if (props.type === 'radio') {
    handleRadio(value)
  }
  handleCheck(value)
}
const selectList = computed<any[]>(() => Object.values(selectListObj.value) || [])
// 删除选中的
const deltUser = (citem: any) => {
  citem.check = false
  delete selectListObj.value[citem.id]
}
// 清空选中的
const clearAll = () => {
  Object.values(selectListObj.value).map((item: any) => (item.check = false))
  selectListObj.value = {}
}

// 绑定员工
const submitBind = async () => {
  if (!selectList.value.length) return ElMessage.error('请选择要绑定的员工！')
  const targetList = selectList.value.map((item: any) => ({ id: item.id, name: item.real_name }))
  emits('successBind', targetList)
  closeEven()
}
</script>

<style lang="scss" scoped>
.relation_main {
  //   width: 800px;
  .relation_header {
    border: 1px solid #ccc;
  }
  .relation_header_left {
    padding: 10px;
    border-right: 1px solid #ccc;
  }
  .relation_header_right {
    padding: 10px;
    .clear {
      color: #ccc;
      cursor: pointer;
    }
  }
  .relation_list {
    border: 1px solid #ccc;
    border-top: 0;
    .relation_list_left {
      border-right: 1px solid #ccc;
      width: calc(50% + 1px);
      padding: 5px;
      box-sizing: border-box;
      position: relative;
      .relation_list_left_header {
        padding: 10px 0;
        border-bottom: 1px solid #ccc;
      }
      .relation_list_left_con {
        height: 600px;
        overflow-y: hidden;
        width: calc(100% - 20px);
        .relation_list_left_con_tag {
          background-color: #f0f0f0;
          padding: 2px 5px;
          font-weight: 700;
        }
        .relation_list_left_con_name {
          height: 60px;
          border-bottom: 1px solid #ccc;
          cursor: pointer;
          .user_name {
            font-size: 15px;
            .select_color {
              color: #409eff;
            }
            span {
              &:nth-child(2) {
                font-size: 13px;
                color: #ccc;
              }
            }
          }
          .el-checkbox.el-checkbox--large {
            width: 100%;
            display: inline-block;
          }
        }
      }
      .letter_list {
        width: 20px;
        height: 540px;
        position: absolute;
        margin: auto;
        right: 0;
        top: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        li {
          text-align: center;
          cursor: pointer;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          &:hover {
            background-color: #ccc;
          }
        }
      }
    }
    .relation_list_right {
      width: 50%;
      overflow-y: auto;
      height: 650px;
      .relation_list_right_con_name {
        height: 60px;
        border-bottom: 1px solid #ccc;
        .user_name {
          font-size: 15px;
          span {
            &:nth-child(2) {
              font-size: 13px;
              color: #ccc;
            }
          }
        }
        .delete_user {
          color: #ccc;
          cursor: pointer;
          padding: 10px;
        }
      }
    }
  }
}
</style>
