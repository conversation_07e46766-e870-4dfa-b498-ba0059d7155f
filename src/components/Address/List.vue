<script setup lang="ts">
import type { AddressFormData } from './types'
// import { Edit, LocationFilled } from '@element-plus/icons-vue'

// 定义 props
const props = defineProps<{
  list: AddressFormData[]
  types: string
}>()

// 定义事件
const emit = defineEmits<{
  cardClick: [item: AddressFormData] // 卡片点击事件
  edit: [item: AddressFormData] // 编辑按钮点击事件
}>()

// 处理卡片点击
function handleCardClick(item: AddressFormData) {
  emit('cardClick', item)
}

// 处理编辑按钮点击
function handleEdit(e: Event, item: AddressFormData) {
  // 阻止事件冒泡，防止触发卡片点击事件
  e.stopPropagation()
  e.preventDefault()
  emit('edit', item)
}
</script>

<template>
  <div class="address-list">
    <div class="address-grid">
      <div
        v-for="item in props.list"
        :key="item.id"
        class="address-card"
        :class="{ 'is-default': item.is_default }"
        @click="handleCardClick(item)"
      >
        <div class="card-header">
          <div class="order-number">
            <span class="label">{{ item.contact_name }}</span>
            <span class="number">{{ item.phone }}</span>
            <span v-if="item.is_default" class="default-tag">默认</span>
          </div>
          <button
            v-if="props.types === 'CanEdit'"
            type="button"
            class="edit-btn"
            @click="(e) => handleEdit(e, item)"
          >
            <el-icon color="#1890ff" size="18">
              <Edit />
            </el-icon>
          </button>
        </div>

        <div class="card-content">
          <div class="info-row">
            <div class="address">
              <el-icon color="#1890ff" size="18">
                <LocationFilled />
              </el-icon>
              {{ item.location && item.location.length > 0 ? item.location.join(" ") : "" }} {{ item.address }}
            </div>
          </div>

          <div class="info-row company-info">
            <div class="factory">
              <span class="label">加工厂：</span>
              <span class="value">{{ item.name }}</span>
            </div>
            <div class="logistics">
              <span class="label">物流公司：</span>
              <span class="value">{{ item.logistics_company }} ({{ item.logistics_area }})</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.address-list {
  width: 100%;
  padding: 16px;
}

.address-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
  // max-width: 1200px;
  // margin: 0 auto;
}

.address-card {
  cursor: pointer;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border: 1px solid #1890ff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // 修改点击效果，只在非编辑按钮区域生效
  &:active:not(.edit-btn) {
    transform: scale(0.98);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 0px;
  border-bottom: 1px solid #f5f5f5;

  .order-number {
    display: flex;
    align-items: center;
    gap: 8px;

    .label {
      color: #666;
      font-size: 14px;
    }

    .number {
      color: #333;
      font-weight: 500;
      font-size: 15px;
    }
    .default-tag {
      background: #e6f7ff;
      color: #1890ff;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-left: 8px;
    }
  }

  .copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f5f5f5;
    }

    .copy-icon {
      font-size: 16px;
    }
  }
}

.card-content {
  .info-row {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .address {
      display: flex;
      align-items: baseline;
      gap: 8px;
      color: #333;
      line-height: 1.5;
      font-size: 14px;

      .location-icon {
        font-size: 16px;
        margin-top: 2px;
      }
    }
  }

  .company-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .factory,
    .logistics {
      display: flex;
      align-items: center;
      font-size: 14px;

      .label {
        color: #666;
        min-width: 70px;
        padding: 0;
      }

      .value {
        color: #333;
      }
    }
  }
}

// 修改编辑按钮样式
.edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #f5f5f5;
  }

  // 防止按钮点击时触发卡片动画
  &:active {
    transform: scale(0.95);
  }
}
</style>
