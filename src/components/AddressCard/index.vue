<script setup lang="ts">
import type { PropType } from 'vue'
import { ref } from 'vue'
import DeliveryCard from './DeliveryCard.vue'
import Container from './Container.vue'
import type { AddressTypes } from '@/api/addressCard'
import type { ShipmentInfosType } from '@/api/files/basicData/saleslssueType'

const props = defineProps({
  saleShipmentTypeCode: { // 当前记录的发货类型code
    type: String as PropType<string | undefined>,
    default: undefined,
  },
  saleSystemIds: { // 营销体系 id
    type: Number,
    default: undefined,
  },
  customerIds: { // 客户id
    type: Number,
    default: undefined,
  },
  customerName: { // 客户名称
    type: String,
    default: '',
  },
  type: { // 类型 Add 新增，Edit 编辑 Detail 查看模式
    type: String as PropType<'Add' | 'Edit' | 'Detail'>,
    default: 'Add',
  },
  addressData: { // 地址信息
    type: Object as PropType<AddressTypes | null>,
    default: null,
  },
  deliveryData: { // 发货类型信息
    type: Object as PropType<ShipmentInfosType>,
    default: null,
  },
  isShowDelivery: { // 是否显示发货类型
    type: Boolean,
    default: true,
  },
  isShowAddress: { // 是否显示收货地址
    type: Boolean,
    default: true,
  },
})
// 定义事件
const emit = defineEmits<{
  'shipment-changes': [shipment: ShipmentInfosType]
  'address-changes': [address: AddressTypes]
}>()

const deliveryCardRef = ref()
const containerRef = ref()

// 获取表单数据
async function getFormData() {
  const [deliveryData, addressData] = await Promise.all([
    deliveryCardRef.value?.submitForm() || null,
    containerRef.value?.handleGetAddressInfos() || null,
  ])

  return {
    deliveryData,
    addressData,
  }
}
// 处理地址变更
function handleAddressChange(address: AddressTypes) {
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('address-changes', address)
}

// 处理发货类型变更
function handleShipmentChange(shipment: ShipmentInfosType) {
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('shipment-changes', shipment)
}
defineExpose({
  getFormData,
})
</script>

<template>
  <div class="w-full">
    <div class="flex flex-wrap gap-4 bg-white rounded-lg border border-gray-200  p-4">
      <!-- 左侧：发货类型相关 -->
      <div v-if="props.isShowDelivery" class=" shrink-0">
        <DeliveryCard
          ref="deliveryCardRef"
          :type="props.type"
          :sale-system-ids="props.saleSystemIds"
          :sale-shipment-type-code="props.saleShipmentTypeCode"
          @shipment-changes="handleShipmentChange"
        />
      </div>

      <!-- 右侧：收货地址相关 -->
      <div v-if="props.isShowAddress" class="min-w-[500px] flex-1 rounded-md pl-2">
        <Container
          ref="containerRef"
          :type="props.type"
          :customer-ids="props.customerIds"
          :address-data="props.addressData"
          @address-change="handleAddressChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-select) {
  @apply w-full;
}

:deep(.el-button--primary) {
  @apply bg-blue-500 border-blue-500 hover:bg-blue-600 hover:border-blue-600;
}

:deep(.el-button--primary.is-link) {
  @apply bg-transparent text-blue-500 hover:text-blue-600;
}
</style>
