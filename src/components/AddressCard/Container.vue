<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useAddressAPI } from './composables/useAddressAPI'
import { useAddressState } from './composables/useAddressState'
import AddressContent from './components/AddressContent.vue'
import AddressHeader from './components/AddressHeader.vue'
import AddressListDialog from './components/AddressListDialog.vue'
import { AddressForm } from '@/components/Address'
import type { AddressTypes } from '@/api/addressCard'

const props = withDefaults(defineProps<{
  customerIds?: number
  type?: 'Add' | 'Edit' | 'Detail'
  addressData?: AddressTypes | null
}>(), {
  customerIds: undefined,
  type: 'Add',
  addressData: null,
})

// 定义事件
const emit = defineEmits<{
  'address-change': [address: AddressTypes]
}>()

// 使用组合式API管理状态和API
const { addressList, currentAddress, editData } = useAddressState()
const { fetchAddressList, addAddress, updateAddress } = useAddressAPI()

// 弹窗状态
const showMode = ref(false) // 选择地址弹窗
const formVisible = ref(false) // 新增地址弹窗
const isFirstInit = ref(true) // 是否为首次初始化

/**
 * 重置组件状态
 */
function resetState() {
  addressList.value = []
  currentAddress.value = useAddressState().currentAddress.value
  showMode.value = false
  formVisible.value = false
}

/**
 * 获取地址列表并处理初始化逻辑
 */
async function loadAddressList() {
  if (!props.customerIds) { // 没有客户ID，重置状态
    resetState()
    return
  }

  try {
    const { list, defaultAddress } = await fetchAddressList(props.customerIds)
    addressList.value = list

    // 编辑或详情模式下，优先使用父组件传入的地址
    if ((props.type === 'Edit' || props.type === 'Detail') && props.addressData && isFirstInit.value) {
      currentAddress.value = { ...props.addressData }
      // eslint-disable-next-line vue/custom-event-name-casing
      emit('address-change', currentAddress.value)
      isFirstInit.value = false
      return
    }

    // 新增模式或非首次加载时的处理
    if (props.type === 'Add' || !isFirstInit.value) {
      if (defaultAddress)
        currentAddress.value = defaultAddress

      else if (list.length > 0)
        currentAddress.value = list[0]

      else
        resetState()

      if (currentAddress.value?.id)
        // eslint-disable-next-line vue/custom-event-name-casing
        emit('address-change', currentAddress.value)
    }
  }
  catch (error) {
    console.error('加载地址列表失败:', error)
    resetState()
  }
}

// 监听客户ID变化
watch(() => props.customerIds, async (newVal, oldVal) => {
  if (newVal !== oldVal) {
    if (newVal)
      await loadAddressList()

    else
      resetState()
  }
}, { immediate: true })

// 计算属性：是否显示交互按钮
const showOperations = computed(() => props.type !== 'Detail')

// 计算属性：是否显示空状态提示
const showEmptyTip = computed(() => {
  if (props.type === 'Detail')
    return !props.addressData
  return !props.customerIds
})

// 计算属性：空状态提示文本
const emptyTipText = computed(() => {
  if (props.type === 'Detail')
    return '没有选择收货地址'
  if (!props.customerIds)
    return '选择客户后可以添加收货地址'
  return '暂无地址，请添加'
})

// 监听弹窗状态
watch(formVisible, (newVal) => {
  if (newVal)
    showMode.value = false
})

watch(showMode, (newVal) => {
  if (newVal)
    formVisible.value = false
})

function handleSelectAddress(item: AddressTypes) {
  currentAddress.value = item
  showMode.value = false
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('address-change', currentAddress.value)
}

function handleEditAddress(item: AddressTypes) {
  showMode.value = false // 先关闭列表弹窗
  nextTick(() => {
    editData.value = JSON.parse(JSON.stringify(item))
    formVisible.value = true
  })
}

// 新增地址按钮点击处理
function handleAddAddress() {
  showMode.value = false
  nextTick(() => {
    editData.value = {}
    formVisible.value = true
  })
}
const shouldSelectNewData = ref(true) // 是否默认开启新增或者编辑后自动选择新地址
// 处理新增或则编辑表单提交成功
async function handleFormSuccess({ mode, data }: { mode: 'add' | 'edit', data: AddressTypes }) {
  //
  //
  // return
  const submitData = {
    ...data,
    biz_uint_id: props.customerIds,
    location: Array.isArray(data.location) ? data.location.join(',') : data.location,
  }
  try {
    const { success, data: { id } } = await (mode === 'add' ? addAddress(submitData) : updateAddress(submitData))
    if (success) {
      ElMessage.success(`${mode === 'add' ? '新增' : '更新'}成功`)
      formVisible.value = false
      shouldSelectNewData.value = true
      const targetId = id

      await loadAddressList()

      if (shouldSelectNewData.value) {
        const targetAddress = addressList.value.find(item => item.id === targetId)
        if (targetAddress) {
          currentAddress.value = targetAddress
          // eslint-disable-next-line vue/custom-event-name-casing
          emit('address-change', currentAddress.value)
        }
        shouldSelectNewData.value = false
      }
    }
  }
  catch (error) {
    console.error(`${mode === 'add' ? '新增' : '更新'}失败:`, error)
    ElMessage.error(`${mode === 'add' ? '新增' : '更新'}失败`)
    shouldSelectNewData.value = false
  }
}

function handleGetAddressInfos() { // 获取地址信息
  return JSON.parse(JSON.stringify(currentAddress.value))
}

defineExpose({
  handleGetAddressInfos,
})
</script>

<template>
  <div class="address-container">
    <AddressHeader
      :show-add="showOperations && Boolean(props.customerIds)"
      @add="handleAddAddress"
    />

    <template v-if="!showEmptyTip">
      <AddressContent
        :address-list="addressList"
        :current-address="currentAddress"
        :show-operations="showOperations"
        @create="handleAddAddress"
        @change="showMode = true"
      />
    </template>
    <el-text v-else>
      {{ emptyTipText }}
    </el-text>

    <!-- 弹窗组件 -->
    <AddressListDialog
      v-if="showMode"
      v-model="showMode"
      :list="addressList"
      :type="props.type"
      :current-address="currentAddress"
      @select="handleSelectAddress"
      @edit="handleEditAddress"
      @create="handleAddAddress"
    />

    <AddressForm
      v-if="formVisible"
      v-model:visible="formVisible"
      :edit-data="editData"
      @submit="handleFormSuccess"
    />
  </div>
</template>
