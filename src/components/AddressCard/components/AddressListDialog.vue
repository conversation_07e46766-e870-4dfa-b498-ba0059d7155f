<script setup lang="ts">
import { computed } from 'vue'
import type { AddressTypes } from '@/api/addressCard'
import { List } from '@/components/Address'

const props = defineProps<{
  modelValue: boolean
  list: AddressTypes[]
  type: string
  currentAddress?: AddressTypes
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'select': [item: AddressTypes]
  'edit': [item: AddressTypes]
  'create': []
}>()

// 计算属性：标记当前选中的地址
const addressListWithSelected = computed(() => {
  return props.list.map(addr => ({
    ...addr,
    isSelected: addr.id === props.currentAddress?.id,
  }))
})
</script>

<template>
  <el-dialog
    :model-value="modelValue"
    title="选择收货地址"
    width="800px"
    :close-on-click-modal="false"
    class="address-dialog"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <List
      :list="addressListWithSelected"
      :types="type === 'Detail' ? 'NoEdit' : 'CanEdit'"
      :current-id="currentAddress?.id"
      @card-click="emit('select', $event)"
      @edit="emit('edit', $event)"
    />
    <div class="dialog-footer">
      <el-button type="primary" @click="emit('create')">
        新增地址
      </el-button>
    </div>
  </el-dialog>
</template>

<style scoped>
.address-dialog {
  :deep(.el-dialog) {
    background-color: #fff;
  }
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
