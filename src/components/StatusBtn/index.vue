<script lang="ts" setup>
export interface Props {
  status: 1 | 2 | 3
}
const props = withDefaults(defineProps<Props>(), {
  status: 1,
})
const emits = defineEmits(['change'])
const changeStatus = () => [emits('change')]
</script>

<template>
  <el-button link text :type="props.status === 1 ? 'danger' : 'primary'" @click="changeStatus()">
    {{ props.status === 1 ? '禁用' : '启用' }}
  </el-button>
</template>

<style></style>
