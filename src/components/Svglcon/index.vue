<template>
  <svg
    aria-hidden="true" class="svg-icon" :width="props.width || props.size"
    :height="props.height || props.size"
  >
    <use :xlink:href="symbolId" :fill="props.color" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '#333',
  },
  size: {
    type: String,
    default: '1em',
  },
  width: {
    type: String,
  },
  height: {
    type: String,
  },
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>
