<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from 'vue'

// 接收外部传入的 props
const props = defineProps({
  title: { type: String, default: '默认标题' },
  width: { type: [String, Number], default: '950' },
  height: { type: [String, Number], default: '550' },
  mask: { type: Boolean, default: false }, // 是否开启遮罩
  lockView: { type: Boolean, default: false }, // 是否锁定视图不允许点击外部
  escClosable: { type: Boolean, default: true }, // 是否允许ESC关闭
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const modelValue = defineModel<boolean>('modelValue') // 是否展示弹窗

// 点击取消按钮
function handleCancel() {
  emit('update:modelValue', false) // 关闭弹窗
  emit('cancel') // 触发取消事件
}

// 点击确认按钮
function handleConfirm() {
  emit('update:modelValue', false) // 关闭弹窗
  emit('confirm') // 触发确认事件
}

// 监听 modelValue 的变化
watch(
  () => modelValue,
  (newValue) => {
    if (!newValue) {
      // 弹窗关闭时触发 close 事件
      emit('close')
    }
  },
)
</script>

<template>
  <Teleport to="body">
    <vxe-modal
      v-model="modelValue"
      destroy-on-close
      show-zoom
      resize
      show-footer
      :title="props.title"
      :width="props.width"
      :height="props.height"
      :mask="props.mask"
      :lock-view="props.lockView"
      :esc-closable="props.escClosable"
    >
      <!-- 主体内容 -->
      <div>
        <slot name="content">
          <!-- 默认内容 -->
          <p>
            这是弹窗的默认内容，您可以通过插槽覆盖。<br>
            插槽：<br>
            {{ `<template #content>内容</template>` }}<br>
            api基础用法：<br>
            title: 标题，默认《默认标题》,<br>
            width: 弹窗宽度，默认《950》,<br>
            height: 弹窗高度，默认《550》,<br>
            mask: 是否开启遮罩,默认《false》<br>
            lockView: 是否锁定视图不允许点击外部,默认《false》<br>
            escClosable: 是否允许ESC关闭,默认《true》<br>
            方法用法：<br>
            cancel: 点击取消按钮,<br>
            confirm: 点击确认按钮,<br>
            close: 弹窗关闭的时候执行的回调
          </p>
        </slot>
      </div>
      <template #footer>
        <slot name="footer">
          <!-- 默认按钮 -->
          <el-button @click="handleCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确认
          </el-button>
        </slot>
      </template>
    </vxe-modal>
  </Teleport>
</template>

<style lang="scss" scoped>
:deep(.vxe-input--suffix) {
  width: auto;
}

:deep(.vxe-input.is--suffix .vxe-input--inner) {
  padding-right: 46px;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0 !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  cursor: pointer;
}

.vxe_icon {
  margin-right: 6px;
  font-size: 14px;
  cursor: pointer;
}

:deep(.el-form-item--default) {
  margin-bottom: 0;
}
</style>
