<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'
import AddDialog from '@/pages/commonData/rawInformation/add.vue'
import { raw_material_add } from '@/api/rawInformation'

defineOptions({
  name: 'SelectRawMaterialDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'code',
  disabled: false,
  showAddLink: true,
  api: 'rawmaterialMenu',
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  // (e: 'update:grey_fabric_id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const modelValue = defineModel<number>('modelValue', { required: true })
interface Props {
  query?: any
  defaultValue?: {
    id: number
    name?: string
    code?: string
  }
  labelName?: string
  field?: 'code' | 'name'
  api?: keyof typeof selectApi | ''
  showAddLink?: boolean
  disabled?: boolean
  isPushDefaultData?: boolean
}
const fieldName = {
  name: '原料名称',
  code: '原料编号',
}

const productRef = ref()

const componentRemoteSearch = reactive({
  name: '',
  code: '',
})
function handleInputCustomerName(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  emits('changeValue', val)
  modelValue.value = val.id
  // emits('update:grey_fabric_id', val.id)
}
const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})

defineExpose({
  productRef,
})
const AddDialogRef = ref()
function handleSelectCustomer() {
  AddDialogRef.value.state.modalName = '新建原料资料'
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.form.ingredient_item = [{ fibre_id: '', fibre_code: '', count: 0 }]
}

const {
  fetchData: postFetch,
  success: postSuccess,
  msg: postMsg,
} = raw_material_add()
async function handleSure(form: any) {
  const query = {
    code: form.code,
    // count: Number(form.count),
    count: form.count,
    craft: form.nuclear,
    ingredient: form.composition,
    name: form.name,
    remark: form.remark,
    texture_url: form.texture_url,
    type_id: form.type,
    unit_id: form.unit,
    id: form.id,
    color_id: form.color_id,
    craft_id: form.craft_id,
    value_id: form.value_id,
    bleach_id: form.bleach_id,
    ingredient_item: form.ingredient_item.filter(item => item.fibre_id),
    supplier_id: form.product_supplier_id?.length
      ? form.product_supplier_id.join(',')
      : '',
    yarn: form.yarn,
  }
  await postFetch(getFilterData(query))
  if (postSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    productRef.value.getTableData()
  }
  else {
    ElMessage.error(postMsg.value)
  }
}
</script>

<template>
  <SelectDialog
    ref="productRef"
    v-model="modelValue"
    :label-field="field"
    :api="api"
    :label-name="labelName"
    :disabled="disabled"
    :table-config="tableConfig"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :is-push-default-data="isPushDefaultData"
    :column-list="[
      {
        field: 'code',
        colGroupHeader: true,
        title: '原料编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'code',
            title: '原料编号',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'name',
        colGroupHeader: true,
        title: '原料名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'name',
            title: '原料名称',
            minWidth: 100,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: defaultValue ? {
          id: defaultValue?.id,
          name: defaultValue?.name,
          code: defaultValue?.code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInputCustomerName"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSure" />
</template>

<style scoped></style>
