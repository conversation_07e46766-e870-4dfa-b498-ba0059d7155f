<template>
  <el-tag :type="statusObj[props.status]">{{ status[props.status] }}</el-tag>
</template>

<script lang="ts" setup>
export interface Props {
  status: 1 | 2 | 3
  name: string
}
const props = withDefaults(defineProps<Props>(), {
  status: 1,
  name: '',
})

const statusObj: any = {
  1: 'success',
  2: 'danger',
  3: 'warning',
}

const status = {
  1: '启用',
  2: '禁用',
  3: '未处理',
}
</script>

<style></style>
