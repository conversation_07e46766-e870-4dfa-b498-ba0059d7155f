<script lang="ts" setup>
import { h, inject, useAttrs } from 'vue'
import { uniqueId } from 'lodash-es'
import SvgIcon from '@/components/Svglcon/index.vue'

interface Props {
  size: string
  text?: string
  options: Options[]
}
interface Options {
  label: string
  qywxName: string
}
const props = withDefaults(defineProps<Props>(), {
  text: '',
  size: '20px',
  options: () => [],
})
const id = uniqueId()
const refName = `svgIcon_${id}`

const updateQywxIconRef = inject<() => void>('updateQywxIconRef')
const updateOptions = inject<() => void>('updateOptions')
const updateIconRefs = inject<() => void>('updateIconRefs')
function setIconRef(el) {
  if (el)
    updateIconRefs(refName, el)
}

function onMoveOverIcon() {
  updateOptions(props.options)
  updateQywxIconRef(refName)
}
const attrs = useAttrs()
function SvgIconCom() {
  return h(SvgIcon, {
    ref: setIconRef,
    class: 'cursor-pointer',
    name: '企业微信',
    size: props.size,
    ...attrs,
    onMouseover: onMoveOverIcon,
  })
}
</script>

<template>
  <SvgIconCom />
</template>

<style scoped></style>
