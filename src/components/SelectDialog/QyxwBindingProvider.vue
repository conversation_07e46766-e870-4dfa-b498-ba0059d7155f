<script lang="ts" setup>
import { provide, ref } from 'vue'
import BindQyGroup from '@/pages/contactUnitMange/components/BindQyGroup.vue'
import BindWechatFriend from '@/pages/contactUnitMange/components/BindWechatFriend.vue'
import { openAutoWxwork } from '@/plugins/autoWxwork'

const bindQyGroupRef = ref()
const bindWechatRef = ref()
const iconRefs = new Map()
const iconRef = ref()
provide('iconRefs', iconRefs)
function updateIconRefs(name, ref) {
  iconRefs.set(name, ref)
}
provide('updateIconRefs', updateIconRefs)
function showBindQyGroup() {
  bindQyGroupRef.value.showModal = true
}

function showBindWechat() {
  bindWechatRef.value.showModal = true
}
function updateQywxIconRef(refName: string) {
  iconRef.value = iconRefs.get(refName)
}

const NOT_BIND_QX_CUSTOMER_TEXT = '未绑定企微客户'
const NOT_BIND_QX_GROUP_TEXT = '未绑定企微群'
const options = ref([])
const isNotBind = ref(false)
function updateOptions(params: any[]) {
  if (!params.length) {
    isNotBind.value = true
    options.value = [
      {
        label: NOT_BIND_QX_CUSTOMER_TEXT,
      },
      {
        label: NOT_BIND_QX_GROUP_TEXT,
      },
    ]
    return
  }
  isNotBind.value = true
  options.value = params
}
// 提供方法给子组件使用
provide('showBindQyGroup', showBindQyGroup)
provide('showBindWechat', showBindWechat)
const popoverRef = ref()
provide('qywxPopoverRef', popoverRef)
provide('updateQywxIconRef', updateQywxIconRef)
provide('updateOptions', updateOptions)
function handleClickOption(option: any[]) {
  // 未绑定企微客户
  if (option.label === NOT_BIND_QX_CUSTOMER_TEXT) {
    showBindWechat()
    return
  }

  // 未绑定企微群
  if (option.label === NOT_BIND_QX_GROUP_TEXT) {
    showBindQyGroup()
    return
  }

  openAutoWxwork({
    text: '自动发送文本测试',
    group: '测试群',
  })
  popoverRef.value.hide()
}
function handleQyGroupBound() {
  // 处理绑定逻辑
}

function handleWechatSure() {
  // 处理绑定逻辑
}
</script>

<template>
  <slot />
  <el-popover
    ref="popoverRef"
    :virtual-ref="iconRef"
    :aria-hidden="false"
    :focus-on-show="true"
    :show-after="200"
    trigger="hover"
    virtual-triggering
    placement="bottom-start"
    :width="200"
  >
    <el-link v-for="(option, index) in options" :key="index" :disabled="options.some(item => item.label === NOT_BIND_QX_CUSTOMER_TEXT) && option.label === NOT_BIND_QX_GROUP_TEXT" :class="`p-1 flex justify-start ${index !== options.length - 1 ? 'border-b' : ''} border-solid border-gray-300`" :type="isNotBind ? 'danger' : 'default'" :underline="false" @click="handleClickOption(option)">
      <svg-icon name="企业微信" size="20px" />
      <span class="text-ellipsis">{{ option.label }}</span>
      <template v-if="!isNotBind">
        <span v-if="!option.qywxName" class="text-xs text-[#7cbc18]">@微信</span>
        <span v-else class="text-xs text-[#b67903]">@{{ options.qywxName }}</span>
      </template>
    </el-link>
  </el-popover>
  <teleport to="body">
    <BindQyGroup ref="bindQyGroupRef" @handle-sure="handleQyGroupBound" />
    <BindWechatFriend ref="bindWechatRef" @handle-sure="handleWechatSure" />
  </teleport>
</template>
