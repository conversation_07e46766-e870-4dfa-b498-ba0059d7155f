<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { VxeTableEvents, VxeTablePropTypes } from 'vxe-table'
import Table from '@/components/Table.vue'
import selectApi from '@/api/selectInit'
import { debounce, getFilterData } from '@/common/util'
import { useComponentsRequestStore } from '@/stores/requestCaching'
import { generateReqKey } from '@/util/commonFuns'
import { EmployeeType } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { Business_unitcustomerAdd, business_unitcustomersimple } from '@/api/customerMange'
import type { TableColumn, TableColumnType } from '@/components/Table/type'

interface Props {
  api: keyof typeof selectApi
  query?: any
  height?: number
  modalName: string
  multiple: boolean
  ids?: any
  valueField?: string | number
  labelField?: string | number
  dialogType?: string
  needRefresh?: boolean
  tableConfig?: TableColumnType
  queryConfig?: any
}

const props = withDefaults(defineProps<Props>(), {
  api: '',
  query: {},
  height: 600,
  modalName: '',
  multiple: false,
  ids: [],
  valueField: '',
  dialogType: '',
  needRefresh: false,
  queryConfig: {},
  tableConfig: () => ({}),
})
const emits = defineEmits(['handleSure'])
const componentsRequestStore = useComponentsRequestStore()
const state = reactive<any>({
  showModal: false,
  modalName: '请选择数据',
  multipleSelection: [],
  apiString: '',
  tableData: [],
  columnList: [],
  filterData: {
    code: '',
    name: '',
    phone: '',
    seller_id: '',
  },
  editConfig: null,
  validConfig: null,
})

const { fetchData, data, loading, size, success, page, total, msg, handleSizeChange, query, handleCurrentChange } = selectApi[props.api]?.(props.queryConfig)

const tableRef = ref()
const options = ref<any>([])

watch(
  () => state.showModal,
  async () => {
    if (state.showModal) {
      query.value = props.query
      if (props.needRefresh) {
        await fetchData(getFilterData({ ...props.query, ...state.filterData }))
        options.value = data.value.list
      }
      else {
        getData()
      }

      state.multipleSelection = []
      if (props.multiple) {
        const arr = props.ids === '' ? [] : props.ids
        options.value = options?.value?.map((item: any) => {
          item.selected = arr?.some((citem: any) => citem === item[props.valueField]) ?? false
          return item
        })

        options.value.forEach((item: any) => {
          if (item.selected)
            state.multipleSelection.push(item)
        })
      }
    }
  },
)
const handleRadioChange: VxeTableEvents.RadioChange = ({ newValue }) => {
  state.multipleSelection = [newValue]
}
const tableConfig = computed<TableColumnType>(() => {
  const config = {
    showSlotNums: false,
    showSort: false,
    showPagition: true,
    loading: loading.value,
    page: page.value,
    size: size.value,
    total: total.value,
    handleSizeChange,
    handleCurrentChange,
    height: '100%',
    showSpanHeader: true, // 显示表头分组
    // radioChangeEvent: val => handleSelectionChange(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    radioChangeEvent: handleRadioChange,
    handUpdateRow: handSave,
    // cellClick: handClickCells,
    editConfig: state.editConfig,
    validConfig: state.validConfig,
  }
  if (props.multiple)
    config.showCheckBox = true
  else
    config.showRadio = true

  if (props.dialogType === 'customer') {
    config.showOperate = true
    config.operateWidth = '90'
  }

  return Object.assign(config, props.tableConfig)
})

watch(loading, (loadingStatus) => {
  tableConfig.value.loading = loadingStatus
})

// sizeChange 和 currentChange 触发fetchData之后都应该走这里 更新表格数据
watch(data, (newData) => {
  options.value = newData?.list || newData?.sub_department
})

const key = ref()
function setStoreKey() {
  const config = { data: {}, url: props.api, method: 'get', params: props.query }
  key.value = generateReqKey(config)
}

async function getData() {
  setStoreKey()

  if (!componentsRequestStore.count[key.value]) {
    componentsRequestStore.addCount(key.value)
    await fetchData(getFilterData({ ...props.query, ...state.filterData }))
    if (!success.value)
      return ElMessage.error(msg.value)

    const template = data.value?.list || data.value?.sub_department
    componentsRequestStore.setRequestData(key.value, {
      data: template,
      total: total.value,
      page: page.value,
      size: size.value,
    })
  }
  else {
    const { data, page: cachePage, size: cacheSize, total: cacheTotal } = componentsRequestStore.list[key.value]
    options.value = data
    page.value = cachePage
    size.value = cacheSize
    total.value = cacheTotal
  }
  options.value = options.value.map((item: any) => {
    return {
      ...item,
    }
  })
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// const handleSelectionChange = (obj: any) => {
//   state.multipleSelection = [obj.row]
// }

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (state.multipleSelection.length !== 1 && !props.multiple)
    return ElMessage.error('请选择一条数据')

  if (!state.multipleSelection.length && props.multiple)
    return ElMessage.error('请至少选择一条数据')

  emits('handleSure', state.multipleSelection)
  handCancel()
}

/**
 * 打开弹窗
 * @param columnList 列表配置
 * @param editable 是否支持单元格编辑
 * @param validConfig 校验配置
 */
function showDialog(columnList: TableColumn[], editable = false, validConfig: VxeTablePropTypes.ValidConfig = {}) {
  state.columnList = columnList.map((column) => {
    if (column.colGroupHeader && Array.isArray(column.childrenList) && column.childrenList.length > 0) {
      const targetField = column.childrenList[0].field
      if (props?.labelField === targetField)
        column.headerInputValue = props.query?.[props.labelField] || ''
      if (typeof column.onInputSearch !== 'function') {
        // 绑定搜索事件
        column.onInputSearch = debounce(({ value }) => {
          state.filterData[targetField] = value
        }, 400)
      }
    }
    return column
  })
  if (editable) {
    state.editConfig = {
      trigger: 'click',
      mode: 'cell',
    }
    state.validConfig = validConfig
  }
  state.showModal = true
}

watch(
  () => state.filterData,
  debounce(async () => {
    await fetchData(getFilterData({ ...props.query, ...state.filterData }))
  }, 400),
  { deep: true },
)

const users = JSON.parse(localStorage.getItem('user') as string)

function handAdd() {
  options.value.unshift({
    code: '',
    name: '',
    seller_id: users.user.user_id,
    phone: '',
    id: '',
  })
}

const { fetchData: postFetch, success: postSuccess, msg: postMsg } = Business_unitcustomerAdd()

const { fetchData: putFetch, success: putSuccess, msg: putMsg } = business_unitcustomersimple()
// 保存客户
async function handSave(row: any) {
  if (row.name === '')
    return ElMessage.error('客户名称不可为空')

  row.id === ''
    ? await postFetch({
      code: row.code,
      name: row.name,
      seller_id: row.seller_id,
      phone: row.phone,
      is_simple_add: true,
    })
    : await putFetch({
      code: row.code,
      name: row.name,
      seller_id: row.seller_id,
      phone: row.phone,
      is_simple_add: true,
      id: row.id,
    })
  if (row.id === '' ? postSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    await fetchData(getFilterData({ ...props.query, ...state.filterData }))
    options.value = data.value?.list
    // 添加到缓存里
    componentsRequestStore.setRequestData(key.value, {
      data: options.value,
      total: total.value,
      page: page.value,
      size: size.value,
    })
  }
  else {
    ElMessage.error(row.id === '' ? postMsg.value : putMsg.value)
  }
}

defineExpose({
  state,
  showDialog,
  getData: async () => {
    await fetchData(getFilterData({ ...props.query, ...state.filterData }))
    const template = data.value?.list || data.value?.sub_department
    options.value = template
    componentsRequestStore.setRequestData(key.value, {
      data: template,
      total: total.value,
      page: page.value,
      size: size.value,
    })
  },
})
</script>

<template>
  <vxe-modal v-model="state.showModal" destroy-on-close resize show-footer :title="props.modalName" width="1000" :height="props.height" :mask="false" :lock-view="false" :esc-closable="true">
    <div v-if="props.dialogType === 'customer'" class="descriptions_row flex flex-row justify-end mb-2" :style="{ '--minLabelWidth': '74px' }">
      <el-button type="primary" plain @click="handAdd">
        新增客户
      </el-button>
    </div>
    <Table v-if="state.showModal" ref="tableRef" :config="tableConfig" :table-list="options" :column-list="state.columnList">
      <template #radioHeader>
        <slot name="radioHeader" />
      </template>
      <template #code="{ row }">
        <vxe-input v-model="row.code" clearable />
      </template>
      <template #name="{ row }">
        <vxe-input v-model="row.name" clearable />
      </template>
      <template #phone="{ row }">
        <vxe-input v-model="row.phone" clearable />
      </template>
      <template #seller_name="{ row }">
        <SelectComponents v-model="row.seller_id" style="width: 300px" api="Adminemployeelist" :query="{ duty: EmployeeType.salesman }" label-field="name" value-field="id" clearable />
      </template>
      <template #operate="{ row }">
        <el-button text type="primary" @click="handSave(row)">
          保存
        </el-button>
      </template>
    </Table>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>
