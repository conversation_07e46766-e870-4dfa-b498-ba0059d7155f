<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { GetTenantReceiveAddrList } from '@/api/tenantReceiveAddr'

const props = defineProps({
  saleSystemId: {
    type: Number || String,
    required: true,
  },
})

const emits = defineEmits(['selectChange'])

const options = ref<any>([])
const value = defineModel()

const { fetchData, data } = GetTenantReceiveAddrList()

function reset() {
  value.value = ''
  options.value = []
}

watchEffect(async () => {
  // 如果传入的这个是空的，说明没选或者是重置了
  if (Number(props.saleSystemId) === 0) {
    reset()
    return
  }
  // 请求数据
  await fetchData({
    sale_system_id: Number(props.saleSystemId),
    addr_type: 2,
  })
  // 赋值
  options.value = data.value.list
})

function handChangeSelect(addrId: any) {
  // 把选中的整个对象也抛出去
  const selectedItem = options.value?.find(
    (item: any) => item?.id === addrId,
  )
  emits('selectChange', selectedItem)
}
</script>

<template>
  <el-select v-model="value" placeholder="请选择回货地址" clearable @change="handChangeSelect">
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.addr"
      :value="item.id"
    />
  </el-select>
</template>
