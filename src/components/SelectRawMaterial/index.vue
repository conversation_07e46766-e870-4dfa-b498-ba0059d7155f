<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onActivated, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { GetGreyFabricInfoListUseByOthers } from '@/api/greyFabricInformation'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { debounce, getFilterData } from '@/common/util'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import { formatHashTag } from '@/common/format'
import { processDataOut } from '@/common/handBinary'

export interface Props {
  id: number
  modelValue: boolean
  defaultSelecteIds?: number[]
  multiple: boolean
  handBinary?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  id: 0,
  modelValue: false,
  multiple: true,
  handBinary: false,
})
const emit = defineEmits(['update:modelValue', 'submit'])

const state = reactive({
  filterData: {
    code: '',
    name: '',
    full_name: '',
    grey_fabric_width: '',
    grey_fabric_gram_weight: '',
    total_needle_size: '',
    gray_fabric_color_id: '',
    single_price_min: null,
    single_price_max: null,
  },
})

const showModal = ref<boolean>(false)
watch(
  () => props.modelValue,
  (show) => {
    showModal.value = show
    if (show) {
      getData()
    }
    else {
      state.filterData = {
        code: '',
        name: '',
        full_name: '',
        grey_fabric_width: '',
        grey_fabric_gram_weight: '',
        total_needle_size: '',
        gray_fabric_color_id: '',
        single_price_min: null,
        single_price_max: null,
      }
    }
  },
)

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const columnList = ref([
  // {
  //   title: '坯布编号',
  //   field: 'code',
  //   minWidth: 100,
  // },
  {
    title: '坯布名称',
    field: 'name',
    minWidth: 100,
    soltName: 'name',
  },
  {
    title: '坯布全称',
    field: 'full_name',
    minWidth: 100,
  },
  {
    title: '布种类型',
    field: 'type_grey_fabric_name',
    minWidth: 100,
  },
  {
    title: '坯布订单类型',
    field: 'type_grey_fabric_order_name',
    minWidth: 100,
  },
  {
    title: '坯布成分',
    field: 'grey_fabric_composition',
    minWidth: 100,
  },
  {
    title: '坯布幅宽',
    field: 'grey_fabric_width',
    minWidth: 100,
  },
  {
    title: '坯布克重',
    field: 'grey_fabric_gram_weight',
    minWidth: 80,
  },
  {
    title: '成品幅宽',
    field: 'finish_product_width',
    minWidth: 80,
  },
  {
    title: '成品克重',
    field: 'finish_product_gram_weight',
    minWidth: 100,
  },
  {
    title: '单位',
    field: 'unit_name',
    minWidth: 80,
  },
  {
    title: '针寸数',
    field: 'needle_size',
    minWidth: 80,
  },
  {
    title: '总针数',
    field: 'total_needle_size',
    minWidth: 80,
  },
  {
    title: '织机机型',
    field: 'loom_model_name',
    minWidth: 80,
  },
  {
    title: '织坯颜色',
    field: 'gray_fabric_color_name',
    minWidth: 100,
  },
  {
    title: '织造规格',
    field: 'weaving_process_name',
    soltName: 'weaving_process_name',
    minWidth: 100,
  },
  {
    title: '织造损耗',
    field: 'weaving_loss',
    minWidth: 100,
    isPrice: !props.handBinary,
  },
  {
    title: '布匹定重',
    field: 'weight_of_fabric',
    minWidth: 100,
    isWeight: !props.handBinary,
  },
  {
    title: '纱长',
    field: 'yarn_length',
    minWidth: 100,
  },
  {
    title: '创建时间',
    field: 'create_time',
    minWidth: 150,
    isDate: true,
    sortable: true,
  },
])

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)
const tablesRef = ref()

const { fetchData: fetchDataList, data: dataList, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetGreyFabricInfoListUseByOthers()
async function getData() {
  await fetchDataList(getFilterData(state.filterData))
  if (props.handBinary)
    dataList.value = processDataOut(dataList.value)
}
onActivated(() => {
  getData()
  tablesRef.value?.tableRef?.sort({ field: 'create_time', order: 'desc' })
})
function onClose() {
  emit('update:modelValue', false)
}

const selectList = ref<any[]>()
function handAllSelect({ records }: any) {
  // 单选
  if (!props.multiple && records.length > 1) {
    records.forEach((item: any, index: number) => {
      item.selected = index === 0
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function handleSelectionChange({ row }: any) {
  const records = tablesRef.value.tableRef.getCheckboxRecords()
  if (!props.multiple && records.length > 1) {
    const index = records.findIndex((item: any) => item.id === row.id)
    records.forEach((item: any, i: number) => {
      item.selected = index === i
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function submit() {
  emit('submit', selectList.value)
}
// 添加点击行时切换选中状态的函数
function handleRowClick({ row }: any) {
  // 获取表格实例
  const tableRef = tablesRef.value.tableRef

  // 判断当前行是否已选中
  const isSelected = tableRef.isCheckedByCheckboxRow(row)

  if (isSelected) {
    // 如果已选中，则取消选中
    tableRef.setCheckboxRow(row, false)
  }
  else {
    // 如果未选中，则选中该行
    // 对于单选模式，需要先清除所有选中
    if (!props.multiple) {
      // 获取所有已选中的行
      const selectedRows = tableRef.getCheckboxRecords()
      // 清除所有选中
      selectedRows.forEach((item: any) => {
        tableRef.setCheckboxRow(item, false)
      })
    }
    // 选中当前行
    tableRef.setCheckboxRow(row, true)
  }

  // 更新选中列表
  selectList.value = tableRef.getCheckboxRecords()
}
const tableConfig = reactive({
  showCheckBox: true,
  handAllSelect,
  handleSelectionChange,
  handleSizeChange,
  handleCurrentChange,
  showPagition: true,
  loading,
  size,
  cellClick: (val: any) => handleRowClick(val),
  height: '100%',
  page,
  total,
  checkRowKeys: [] as number[],
})

watch(
  () => props.defaultSelecteIds,
  (val) => {
    tableConfig.checkRowKeys = val || []
  },
)
const router = useRouter()
function handleAddNew() {
  router.push({
    name: 'GreyFabricInformationAdd',
  })
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="添加坯布商品" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <div class="flex flex-col h-full ">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.code" size="small" placeholder="坯布编号" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.name" size="small" placeholder="坯布名称" clearable />
          </template>
        </DescriptionsFormItem>

        <!-- <DescriptionsFormItem label="坯布全称:">
          <template #content>
            <el-input v-model="state.filterData.full_name" size="small" placeholder="坯布名称" clearable />
          </template>
        </DescriptionsFormItem> -->

        <DescriptionsFormItem label="幅宽:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_width" size="small" placeholder="幅宽" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="克重:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_gram_weight" size="small" placeholder="克重" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="总针数:">
          <template #content>
            <el-input v-model="state.filterData.total_needle_size" size="small" placeholder="总针数" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="织坯颜色:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.gray_fabric_color_id"
              :query="{ name: componentRemoteSearch.name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'name',
                  title: '颜色',
                },
              ]"
              api="getInfoProductGrayFabricColorList"
              @change-input="val => (componentRemoteSearch.name = val)"
            />
          <!-- <SelectComponents size="small" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" v-model="state.filterData.gray_fabric_color_id" clearable /> -->
          </template>
        </DescriptionsFormItem>

        <!-- <DescriptionsFormItem label="坯布单价:" :copies="2">
          <template #content>
            <div class="flex">
              <el-input-number v-model="state.filterData.single_price_min" size="small" controls-position="right" clearable />
              -
              <el-input-number v-model="state.filterData.single_price_max" size="small" controls-position="right" clearable />
            </div>
          </template>
        </DescriptionsFormItem> -->
      </div>

      <FildCard title="" no-shadow class="mt-[5px] flex flex-col h-full flex-auto" :tool-bar="true">
        <template #right-top>
          <el-link :underline="false" type="primary" @click="handleAddNew">
            <el-icon><Plus /></el-icon>
            新建坯布
          </el-link>
        </template>
        <Table v-if="modelValue" ref="tablesRef" :config="tableConfig" :table-list="dataList.list" :column-list="columnList">
          <template #name="{ row }">
            {{ formatHashTag(row?.code, row?.name) }}
          </template>
          <template #weaving_process_name="{ row }">
            {{ row?.weave_spec?.map(v => v.weave_spec_name)?.join(',') }}
          </template>
        </Table>
      </FildCard>
    </div>
    <template #footer>
      <el-button type="primary" size="small" @click="submit">
        提交
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
</style>
