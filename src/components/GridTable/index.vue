<script setup lang="ts">
import {
  computed,
  h,
  inject,
  nextTick,
  reactive,
  ref,
  toRaw,
  watch,
} from 'vue'
import type {
  VxeColumnSlotTypes,
  VxeGridListeners,
  VxeGridProps,
  VxeTableDataRow,
  VxeTableDefines,
  VxeTableInstance,
  VxeTablePropTypes,
} from 'vxe-table'
import { VxeInput } from 'vxe-pc-ui'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import TableFile from '@/components/TableField/index.vue'
import {
  GetListHabitsList,
  UpdateAllListHabits,
  UpdateListHabits,
} from '@/api/tableField'
import type { TableColumn } from '@/components/Table/type'
import type { ItemProps } from '@/components/TableField/item.vue'
import { genId } from '@/use/useTableEnterAutoFocus'
import type { GridPropsType } from '@/components/GridTable/index'
import { debounce } from '@/common/util'

defineOptions({
  name: 'GridTable',
})
const props = withDefaults(defineProps<GridPropsType>(), {
  cellClick: undefined,
  cellDblclick: undefined,
  cellMenu: undefined,
  checkboxAll: undefined,
  checkboxChange: undefined,
  custom: undefined,
  footerCellClick: undefined,
  footerCellDblclick: undefined,
  footerCellMenu: undefined,
  scroll: undefined,
  tableHeaderCellMenu: undefined,
  tableHeaderClick: undefined,
  tableHeaderDbClick: undefined,
  zoom: undefined,
  footerMethod: undefined,
  footerCellClassName: undefined,
  showLog: false,
  showPagition: false,
  height: 'auto',
  config: () => ({}),
  columns() {
    return []
  },
  data(_props: Readonly<GridPropsType> & Record<string, any>): (any[] & Record<string, any>) | undefined {
    return undefined
  },
})

const emit = defineEmits(['changeSelectAllListData'])
const slots = defineSlots()
const tableFieldcolumn = ref<TableColumn[]>([])
const list = ref<any[]>([])

const TableRef = ref<VxeTableInstance>()

const gridOptions = reactive<VxeGridProps<any>>({
  border: true,
  stripe: true,
  showFooter: !!props.config?.footerMethod,
  footerMethod: props.config?.footerMethod,
  footerCellClassName: props.config?.footerCellClassName,
  height: props.height,
  minHeight: '0px',
  tooltipConfig: {},
  exportConfig: {},
  menuConfig: {},
  size: 'mini',
  mergeCells: props.config?.mergeCells,
  spanMethod: props.config.spanMethod,
  keyboardConfig: {
    isArrow: true,
    isDel: true,
    isEnter: true,
    isTab: true,
    isEdit: true,
    isChecked: true,
    editMethod({ row, column }) {
      const $table = TableRef.value
      // 重写默认的覆盖式，改为追加式
      $table.setEditCell(row, column)
    },
  } as VxeTablePropTypes.KeyboardConfig,
  scrollY: {
    enabled: true,
    gt: 100,
    scrollToTopOnChange: false, // 切换分页时滚动条自动置顶
  },
  showOverflow: true,
  headerCellClassName: props.config?.headerCellClassName,
  scrollX: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  toolbarConfig: {
    export: false,
    zoom: false,
    custom: false,
    enabled: false,
  },
  editConfig: props.config.editConfig || {
    trigger: 'click',
    mode: 'row',
    showStatus: true,
  },
  checkboxConfig: props.config.checkboxConfig || {
    labelField: '',
    reserve: true,
    highlight: true,
    range: false,
  },
  validConfig: {
    showMessage: false,
  },
  filterConfig: props.config.filterConfig,
  sortConfig: props.config.sortConfig,
  columns: tableFieldcolumn,
  editRules: props.config.editRules,
  data: list.value,
  checkboxAll: props.config.handAllSelect,
  checkboxChange: props.config.handleSelectionChange,
  radioChange: props.config.radioChangeEvent,
  rowConfig: props.config?.rowConfig,
})

function render(item: VxeTableDefines.ColumnOptions<VxeTableDataRow>) {
  if (!item.slots)
    item.slots = {}

  if (item.editRender) {
    const defaultSlot = item.slots?.default as (
      params: VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>,
    ) => any
    if (defaultSlot) {
      item.slots.default = (args) => {
        const render = defaultSlot?.(args) || null
        return h(
          'div',
          {
            class: 'flex items-center justify-center',
            id: genId(item.field!, args.rowIndex),
          },
          [
            render != null
              ? render
              : h(
                'span',
                { class: 'text-blue-300 font-medium' },
                  `请填入${item.title}`,
              ),
          ],
        )
      }
    }
  }
  // 筛选
  item.slots.filter = (args) => {
    return args.column.filters.map((item, index) => {
      return h(VxeInput, {
        class: 'search-input',
        key: index,
        modelValue: item.data,
        onInput: (val) => {
          item.data = val.value
          return args.$panel.changeOption(val, !!item.data, item)
        },
        onKeyup: (e: KeyboardEvent) => {
          if (e.keyCode === 13)
            args.$panel.confirmFilter()
        },
        placeholder: '点击筛选确认筛选',
      })
    })
  }

  return renderDefaultConfig(item)
}

const handleFilterMethod: any = ({ option, row }: any, field = '') => {
  return row[field]?.toString().includes(option.data)
}
function renderDefaultConfig(
  item: VxeTableDefines.ColumnOptions<VxeTableDataRow>,
) {
  const filter = ref<any>([{ data: '' }])
  return {
    ...item,
    filters: item.filterStatus === false ? null : filter,
    filterMethod: (val: any) => handleFilterMethod(val, item.field),
    showOverflow: true,
    align: 'center',
  }
}

function handleColumns(columnsArg: GridPropsType['columns']) {
  const columns = cloneDeep(columnsArg)
  let hasCheckbox = false
  let hasRadio = false
  let hasSeq = false
  const result = columns.map((item) => {
    if (item.type === 'seq')
      hasSeq = true

    if (item.type === 'checkbox')
      hasCheckbox = true

    if (item.type === 'radio')
      hasRadio = true

    if ('children' in item && item?.children?.length > 0) {
      item.children = item.children?.map((item) => {
        return render(item)
      })
      return item
    }
    else {
      return render(item)
    }
  })
  // 明确指定不显示序号列 才不显示
  if (props.config.showSeq !== false) {
    if (!hasSeq) {
      result.unshift({
        type: 'seq',
        width: 50,
        title: '序号',
        align: 'center',
        fixed: 'left',
        showOverflow: true,
        showHeaderOverflow: true,
        showFooterOverflow: true,
      })
    }
  }
  if (props.config.showCheckBox) {
    if (!hasCheckbox)
      result.unshift({ type: 'checkbox', title: '', width: 50, fixed: 'left' })
  }
  if (props.config.showRadio) {
    if (!hasRadio)
      result.unshift({ type: 'radio', title: '', width: 45, fixed: 'left' })
  }
  return result
}
const multipleSelection = ref<any[]>([])

const gridEvents: VxeGridListeners<any> = {
  headerCellClick(params) {
    const { _column } = params
  },
  headerCellDblclick(params) {
    const { _column } = params
  },
  headerCellMenu(params) {
    const { _column } = params
  },
  cellClick(params) {
    const { _column } = params
  },
  cellDblclick(params) {
    const { _column } = params
  },
  cellMenu(params) {
    const { _row } = params
  },
  footerCellClick(params) {
    const { _column } = params
  },
  footerCellDblclick(params) {
    const { _column } = params
  },
  footerCellMenu(params) {
    const { _column } = params
  },
  checkboxChange(params) {
    props.config.handleSelectionChange?.(params)
    multipleSelection.value = TableRef.value.getCheckboxRecords(true)
  },
  checkboxAll(params) {
    props.config.handAllSelect?.(params)
    multipleSelection.value = TableRef.value.getCheckboxRecords(true)
  },
  radioChange(params) {
    props.config.radioChangeEvent?.(params)
    multipleSelection.value = [params.newValue]
  },
  scroll(_params) {},
  zoom(_params) {},
  custom(_params) {},
}

const tableFieldRef = ref() // 字段调整组件
const showField: any = inject('toolbar')

const { fetchData: fetchDataField, data: dataField } = GetListHabitsList()
const tableFieldcolumnUser = ref()
const saveDefaultColumnList = ref<ItemProps[]>([])
async function getFieldDataFun() {
  const filtered: TableColumn[] = toRaw(props.columns)
  if (!props.config?.fieldApiKey) {
    tableFieldcolumnUser.value = filtered
  }
  else {
    await fetchDataField({ key: props.config.fieldApiKey })

    tableFieldcolumnUser.value = JSON.parse(
      JSON.stringify(
        (dataField.value?.habits
          ? JSON.parse(dataField.value?.habits) || []
          : filtered) || [],
      ),
    )
    getSlotObject(tableFieldcolumnUser.value)
    saveDefaultColumnList.value = JSON.parse(
      JSON.stringify(
        dataField.value?.habits_temp
          ? JSON.parse(dataField.value?.habits_temp) || []
          : [],
      ),
    )
    getSlotObject(saveDefaultColumnList.value)
  }
  tableFieldcolumn.value = handleColumns(tableFieldcolumnUser.value)
  //
  //
  // // gridOptions.value.columns = tableFieldcolumn.value
  // TableRef.value?.loadColumn(tableFieldcolumn.value)
}
const getFieldData = debounce(getFieldDataFun, 500)
// 因为JSON不能序列化slot里面的render 函数，所以需要在原来的column上取回render 函数
function getSlotObject(columnsList: GridPropsType['columns']) {
  // 根据field递归查找columns对应的column
  const findColumn = (columns: any, field: string) => {
    for (const column of columns) {
      if (column.field === field)
        return column

      if (column.children) {
        const found = findColumn(column.children, field)
        if (found)
          return found
      }
    }
  }
  for (const item of columnsList) {
    if (item.children && item.children.length > 0) {
      getSlotObject(item.children)
      continue
    }
    // 根据field递归查找props.columns对应的column
    const column = findColumn(props.columns, item.field)
    if (column)
      item.slots = column.slots

    // for (const originColumn of props.columns) {
    //   if (item.field === originColumn.field) {
    //     item.slots = originColumn.slots
    //     break
    //   }
    // }
  }
}

watch(
  () => [props.config.fieldApiKey, props.columns],
  (_val) => {
    getFieldData()
  },
  { immediate: true, deep: true },
)

const { fetchData: fetchDataUpdate, success: successUpdate }
  = UpdateListHabits()
const { fetchData: fetchDataAllUpdate, success: successAllUpdate }
  = UpdateAllListHabits()

const checkAll = ref(false)

const page = ref(1)
const size = ref(50)

function handleChangeCheckAll(value: boolean) {
  const checkField = gridOptions.checkboxConfig?.checkField

  if (checkField) {
    list.value?.forEach((item: any) => {
      item[checkField] = value
    })
  }
  multipleSelection.value = value ? list.value : []
  TableRef.value?.setCheckboxRow(list.value, value)
  emit('changeSelectAllListData', value)
}

// 分页配置
const elPaginationConfig = computed(() => {
  let paginationConfig = {
    page: page.value,
    size: size.value,
    defaultPageSize: 50,
    pageSizes: [50, 100, 500, 1000],
    total: list.value?.length || 0,
    handleSizeChange: (value: number) => {
      size.value = value
    },
    pageLayout: 'total, sizes, prev, pager, next, jumper',
    handleCurrentChange: (value: number) => {
      page.value = value
    },
  }
  if (props.elPaginationConfig)
    paginationConfig = Object.assign(paginationConfig, props.elPaginationConfig)

  return paginationConfig
})

// 计算分页列表
function computedList(list: any[]) {
  if (list && list.length) {
    const { page, size } = elPaginationConfig.value
    const result = list.slice((page - 1) * size, page * size)
    gridOptions.data = result
    TableRef.value?.loadData(result)
  }
}

watch(
  () => props.data,
  () => {
    list.value = props.data
    if (!props.elPaginationConfig) {
      computedList(list.value)
    }
    else {
      nextTick(() => {
        TableRef.value?.loadData(list.value)
      })
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => props.config.loading,
  (newValue) => {
    gridOptions.loading = newValue
  },
  {
    immediate: true,
  },
)
// 没有分页配置时
// if (!props.elPaginationConfig) {
//   computedList(list.value)
// }

// 监听 page size 当页码切换时slice出当前页的数据， 监听list.length 当数组新增或减少时重新slice
watch([() => page.value, () => size.value, () => list.value?.length], () => {
  computedList(list.value)
})

async function getSubmit(data: any[]) {
  if (!props.config.fieldApiKey)
    return ElMessage.warning('请先设置key')

  await fetchDataUpdate({
    key: props.config.fieldApiKey,
    habits: JSON.stringify(data || ''),
    habits_temp: JSON.stringify(props.columns || ''),
  })
  if (successUpdate) {
    ElMessage.success('字段模板已更新')
    await getFieldData()
  }
  else {
    ElMessage.error('字段模板更新失败')
  }
  await nextTick(() => {
    TableRef.value?.refreshColumn()
  })
}

async function getSubmitAll(data: any[]) {
  ElMessageBox.confirm('该操作会覆盖所有用户的保存记录，确定要执行？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      if (!props.config.fieldApiKey)
        return ElMessage.warning('请先设置key')
      await fetchDataAllUpdate({
        key: props.config.fieldApiKey,
        habits: JSON.stringify(data),
      })
      if (successAllUpdate) {
        ElMessage.success('字段模板已更新')
        await getFieldData()
      }
      else {
        ElMessage.error('字段模板更新失败')
      }
      nextTick(() => {
        TableRef.value?.refreshColumn()
      })
    })
    .catch(() => {})
}

defineExpose({
  TableRef,
  multipleSelection,
})
</script>

<template>
  <div class="flex-1 flex flex-col h-full overflow-y-hidden">
    <div class="flex-auto overflow-hidden">
      <vxe-grid
        v-bind="gridOptions"
        ref="TableRef"
        v-on="gridEvents"
      >
        <template v-for="([slotname], index) in Object.entries(slots)" :key="index" #[slotname]="row">
          <slot :name="slotname" v-bind="row" />
        </template>
      </vxe-grid>
    </div>
    <div
      v-if="props.showPagition"
      class="w-full flex items-center mt-3"
      :class="[
        props.config.showCheckListAll ? 'justify-between' : 'justify-end',
      ]"
    >
      <el-checkbox
        v-if="props.config.showCheckListAll"
        v-model="checkAll"
        size="large"
        @change="handleChangeCheckAll"
      >
        列表全选
      </el-checkbox>
      <el-pagination
        :default-page-size="elPaginationConfig.defaultPageSize"
        :current-page="elPaginationConfig.page"
        :page-sizes="elPaginationConfig.pageSizes"
        :page-size="elPaginationConfig.size"
        :layout="elPaginationConfig.pageLayout"
        :total="elPaginationConfig.total"
        @size-change="elPaginationConfig.handleSizeChange"
        @current-change="elPaginationConfig.handleCurrentChange"
      />
    </div>
  </div>
  <TableFile
    ref="tableFieldRef"
    v-model="showField"
    :default-column-list="handleColumns(props.columns)"
    :save-default-column-list="saveDefaultColumnList"
    show-fixed
    :column-list="tableFieldcolumnUser"
    children-key="children"
    @submit-all="getSubmitAll"
    @submit="getSubmit"
  />
</template>

<style>
.search-input {
  width: 100%;
}

/* 顶部标题不换行 */
.vxe-header--column .vxe-cell {
  display: inline-flex;
  align-items: center;
  max-width: 100%;
}
.vxe-header--column .vxe-cell .vxe-cell--title{
  overflow: hidden;
  text-overflow: clip;
  white-space: nowrap;
}
</style>
