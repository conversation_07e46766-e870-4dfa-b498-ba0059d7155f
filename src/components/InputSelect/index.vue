<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { GetDictionaryDetailEnumListApi } from '@/api/employees'

const props = defineProps(['placeholder', 'query']) // 'defaultValue'
// const emit = defineEmits(['update:SId', 'update:SName'])
const keys = defineModel('keys')
const keyValue = defineModel('keyValue')

// 存储获取的数据
const restaurants = ref<any[]>([])

// 查询方法，模糊搜索
function querySearch(queryString: string, cb: any) {
  const results = queryString
    ? restaurants.value.filter(createFilter(queryString))
    : restaurants.value
  cb(results)
}

// 自定义过滤方法
function createFilter(queryString: string) {
  return (restaurant: any) => {
    return (
      restaurant.name.toLowerCase().includes(queryString.toLowerCase())
    )
  }
}

// 选择回调函数
function handleSelect(item: any) {
  keyValue.value = item.name
  keys.value = item.id
}

// 获取字典数据
const { fetchData } = GetDictionaryDetailEnumListApi()

// 获取数据
async function getData() {
  const result: any = await fetchData({ ...props.query })
  if (result.success && result.total > 0)
    restaurants.value = result.data.list
  else
    restaurants.value = []
}

// 初始化获取数据
onMounted(async () => {
  await getData()
})

// 监听输入变化，更新绑定值
watch(keyValue, (newValue) => {
  if (!restaurants.value.find(item => item.name === newValue)) {
    // 如果输入的内容没有匹配到任何项，则更新 id 为 0，name 为用户输入的值
    keys.value = 0
    keyValue.value = newValue
  }
})
</script>

<template>
  <el-autocomplete
    v-model="keyValue"
    :fetch-suggestions="querySearch"
    clearable
    value-key="name"
    :placeholder="props.placeholder"
    :suffix-icon="ArrowDown"
    @select="handleSelect"
  />
</template>
