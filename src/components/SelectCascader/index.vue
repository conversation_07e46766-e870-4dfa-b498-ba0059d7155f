<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import BottomBar from '../SelectComponents/SelectBottomBar.vue'
import { GetDepartmentApi } from '@/api/department'

const emits = defineEmits(['changeValue'])
const { fetchData, data } = GetDepartmentApi()
async function getData() {
  await fetchData()
}
const dataList = ref<any>([])
watch(
  () => data.value,
  (val) => {
    dataList.value = val.list || []
  },
)
onMounted(() => {
  getData()
})
const cascader = ref()
const modelValue = ref([])
const bottomBarRef = ref()
function handleChange(val: any) {
  emits('changeValue', { ids: val, row: cascader.value.getCheckedNodes()[0]?.data })
}
function visibleChange(val: boolean) {
  if (val)
    bottomBarRef.value?.handleComponentPosition(cascader.value)
}
const props = {
  checkStrictly: true,
  children: 'sub_department',
  value: 'id',
  label: 'name',
}
</script>

<template>
  <el-cascader ref="cascader" v-model="modelValue" :options="dataList" :props="props" @change="handleChange" @visible-change="visibleChange" />
  <BottomBar ref="bottomBarRef" :cascader="cascader" custom-class="p-2 border-[#e5e7eb] hidden border-t" quick-add-link="/peopleManagement/departmentManagement" @refresh="getData" />
</template>
