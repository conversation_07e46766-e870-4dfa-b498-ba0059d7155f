<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import BottomBar from '../SelectComponents/SelectBottomBar.vue'
import { getPhysicalWarehouseListTreeEnum } from '@/api/clothType'

interface Props {
  props?: any
}
const props = withDefaults(defineProps<Props>(), {
  props: {}, // 配置项
})
const emits = defineEmits(['changeValue'])
const { fetchData, data } = getPhysicalWarehouseListTreeEnum()
async function getData() {
  await fetchData()
}
const dataList = ref<any>([])
watch(
  () => data.value.list,
  (val) => {
    dataList.value = val
  },
)
onMounted(() => {
  getData()
})
const cascader = ref()
const modelValue = defineModel()
const bottomBarRef = ref()
function handleChange(val: any) {
  emits('changeValue', { ids: val, row: cascader.value.getCheckedNodes()[0]?.data })
}
function visibleChange(val: boolean) {
  if (val)
    bottomBarRef.value?.handleComponentPosition(cascader.value)
}
const propsConfigDefault = {
  children: 'sub_type_fabric',
  value: 'id',
  label: 'name',
  size: 'small',
}
const newPropsConfig = computed(() => {
  return { ...props.props, ...propsConfigDefault }
})
defineExpose({
  // 关闭面板
  close: () => {
    cascader.value.togglePopperVisible()
  },
})
</script>

<template>
  <el-cascader
    ref="cascader"
    v-model="modelValue"
    class="w-full"
    size="small" popper-class="vxe-table--ignore-clear" placeholder="请选择布种类型" clearable :options="dataList" :show-all-levels="false" :props="newPropsConfig" @change="handleChange" @visible-change="visibleChange"
  />

  <BottomBar ref="bottomBarRef" custom-class="p-2 border-[#e5e7eb] hidden border-t" quick-add-link="/basicData/clothType" @refresh="getData" />
</template>
