<template>
  <div v-if="_show" class="tour" :style="{ top: (infoWindowRect?.top || 0) + 'px', left: (infoWindowRect?.left || 0) + 'px' }">
    <slot>
      <div class="tour-inner">
        <div class="tour-top">
          <!-- 标题 -->
          <slot name="title" v-bind="{ ...stepInfo }">
            <div>{{ stepInfo.title }}</div>
          </slot>
          <!-- 关闭按钮 -->
          <slot name="close" v-bind="{ ...stepInfo }">
            <svg @click="close" t="1672734903811" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2908" width="200" height="200">
              <path d="M0 0h1024v1024H0z" fill="#FF0033" fill-opacity="0" p-id="2909"></path>
              <path
                d="M240.448 168l2.346667 2.154667 289.92 289.941333 279.253333-279.253333a42.666667 42.666667 0 0 1 62.506667 58.026666l-2.133334 2.346667-279.296 279.210667 279.274667 279.253333a42.666667 42.666667 0 0 1-58.005333 62.528l-2.346667-2.176-279.253333-279.253333-289.92 289.962666a42.666667 42.666667 0 0 1-62.506667-58.005333l2.154667-2.346667 289.941333-289.962666-289.92-289.92a42.666667 42.666667 0 0 1 57.984-62.506667z"
                fill="#111111"
                p-id="2910"
              ></path>
            </svg>
          </slot>
        </div>
        <!-- 描述 -->
        <slot name="description" v-bind="{ ...stepInfo }">
          <div class="tour-description">{{ stepInfo.description }}</div>
        </slot>
        <div class="tour-bottom">
          <!-- 底部 -->
          <slot name="footer" v-bind="{ ...stepInfo }">
            <!-- 步骤 -->
            <div class="step-box">
              <span>{{ _current + 1 }}/{{ props.steps.length }}</span>
              <span style="margin-left: 5px; cursor: pointer" @click="close">跳过</span>
            </div>
            <!-- 按钮 -->
            <div class="step-but">
              <div class="step-but-item pre" v-show="tourHandler.current > 0" @click="last">上一步</div>
              <div class="step-but-item" v-show="!isLast" @click="next">下一步</div>
              <div class="step-but-item" v-show="isLast" @click="close">完成</div>
            </div>
          </slot>
        </div>
      </div>
    </slot>
  </div>
  <div v-if="_show" class="arrow" :style="{ top: (arrowRect?.top || 0) + 'px', left: (arrowRect?.left || 0) + 'px' }"></div>
  <svg v-if="maskRect && props.mask && _show" class="mask">
    <mask id="ant-tour-mask-:r0:">
      <rect x="0" y="0" width="100%" height="100%" fill="white"></rect>
      <rect fill="black" rx="2" :x="maskRect.center.left" :y="maskRect.center.top" :width="maskRect.center.width" :height="maskRect.center.height"></rect>
    </mask>
    <rect x="0" y="0" width="100%" height="100%" pointer-events="auto" fill="rgba(0,0,0,0.5)" mask="url(#ant-tour-mask-:r0:)"></rect>
    <!--  上  -->
    <rect fill="transparent" pointer-events="auto" :x="maskRect.top.left" :y="maskRect.top.top" :width="maskRect.top.width" :height="maskRect.top.height"></rect>
    <!--  下  -->
    <rect fill="transparent" pointer-events="auto" :x="maskRect.bottom.left" :y="maskRect.bottom.top" :width="maskRect.bottom.width" :height="maskRect.bottom.height"></rect>
    <!--  左  -->
    <rect fill="transparent" pointer-events="auto" :x="maskRect.left.left" :y="maskRect.left.top" :width="maskRect.left.width" :height="maskRect.left.height"></rect>
    <!--  又  -->
    <rect fill="transparent" pointer-events="auto" :x="maskRect.right.left" :y="maskRect.right.top" :width="maskRect.right.width" :height="maskRect.right.height"></rect>
  </svg>
</template>

<script setup lang="ts">
import { Step, MaskRect, TourHandler, Rect, TargetRect, ArrowRect } from './TourHandler'
import { computed, ComputedRef, Ref, ref, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    current?: number
    show?: boolean
    steps: Step[]
    mask?: boolean
    continueAction?: () => void
    finishAction?: () => void
  }>(),
  {
    current: 0,
    show: false,
    steps: () => [],
    mask: true,
  }
)

const emit = defineEmits(['update:current', 'update:show', 'open', 'close', 'change', 'next', 'last'])

const tourHandler = new TourHandler(props.steps)

tourHandler.onNext = rect => {
  _current.value = tourHandler.current
  maskRect.value = rect.maskRect
  infoWindowRect.value = rect.infoWindowRect
  targetRect.value = rect.targetRect
  arrowRect.value = rect.arrowRect
  emit('update:current', tourHandler.current)
  emit('change', tourHandler.current)
}
const _current = ref(props.current)
const _show = ref(props.show)

const stepInfo: ComputedRef<Step> = computed(() => props.steps[_current.value])

const isLast = computed(() => _current.value >= (props.steps?.length || 0) - 1)

const maskRect: Ref<MaskRect | null> = ref(null)
const infoWindowRect: Ref<Rect> = ref({
  width: 0,
  height: 0,
  left: 0,
  top: 0,
})
const targetRect: Ref<TargetRect | null> = ref(null)
const arrowRect: Ref<ArrowRect | null> = ref(null)

const arrowShadow = computed(() => {
  switch (arrowRect.value?.direction || '') {
    case 'top':
      return 'rgba(0,0,0,0.2) 1px 1px 0px 0px;'
    case 'bottom':
      return 'rgba(0,0,0,0.2) -1px -1px 0px 0px;'
  }
  return 'rgba(0,0,0,0.2) -1px -1px 0px 0px;'
})

watch(
  () => props.show,
  newVal => {
    newVal ? open() : close()
  }
)

watch(
  () => props.current,
  newVal => {
    _current.value = props.current
    tourHandler.toStep(newVal)
  }
)

function open() {
  _show.value = true
  tourHandler.start()
  emit('update:show', true)
  emit('open')
}

function last() {
  tourHandler.last()
  emit('last')
}

function next() {
  tourHandler.next()
  emit('next')
}

function close() {
  _show.value = false
  tourHandler.close()
  emit('update:show', false)
  emit('close')
}

defineExpose({
  open,
  last,
  next,
  close,
})
</script>

<script lang="ts">
export default {
  name: 'Tour',
  inheritAttrs: false,
}
</script>

<style lang="scss" scoped>
.tour {
  position: fixed;
  min-width: 400px;
  min-height: 150px;
  background: #ffffff;
  border-radius: 5px;
  z-index: 1010;
  transition: all 0.2s;
  box-shadow: rgba(0, 0, 0, 0.2) 1px 1px 5px 2px;
  .tour-inner {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 20px;
    min-width: 400px;
    min-height: 150px;
    box-sizing: border-box;
    .tour-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      .icon {
        width: 15px;
        height: 15px;
        cursor: pointer;
      }
    }
    .tour-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .step-box {
        //font-weight: 600;
        color: #757575;
      }
      .step-but {
        display: flex;
        .step-but-item {
          padding: 2px 5px;
          background: #3f9eff;
          box-sizing: border-box;
          border-radius: 4px;
          color: #ffffff;
          cursor: pointer;
          user-select: none;
          &:active {
            background: #54a7ff;
          }
        }
        .pre {
          background: #ffffff;
          color: #3f9eff;
          border: 1px solid #3f9eff;
          &:active {
            background: #f2f2f2;
          }
        }
        .step-but-item + .step-but-item {
          margin-left: 5px;
        }
      }
    }
  }
}
.arrow {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 100px;
  left: 100px;
  width: 30px;
  height: 30px;
  z-index: 1020;
  transition: all 0.2s;
  &:after {
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    background: #ffffff;
    border-radius: 3px;
    transform: rotate(45deg);
    box-shadow: v-bind(arrowShadow);
  }
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  transition: all 0.3s;
}
</style>
