export interface Step {
  el: () => HTMLElement
  title?: string
  description?: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
  beforeActive?: (run: (rectMap?: RectMap) => void, getRect: () => RectMap) => void
  afterActive?: () => void
}

export interface Rect {
  width: number
  height: number
  left: number
  top: number
}

export interface TargetRect extends Rect {
  right: number
  bottom: number
}

export interface ArrowRect extends Pick<TargetRect, 'top' | 'left'> {
  direction: string
  size: number
}

export interface MaskRect {
  center: Rect
  top: Rect
  bottom: Rect
  left: Rect
  right: Rect
}

export interface RectMap {
  infoWindowRect: Rect
  targetRect: TargetRect
  arrowRect: ArrowRect
  maskRect: MaskRect
}

export class TourHandler {
  // 全部步骤
  private readonly steps: Step[]
  // 遮罩层包裹的目标内边距
  private readonly padding = 5
  // 当前步骤
  public current = 0

  public onNext: ((rectMap: RectMap) => void) | null = null

  private infoWindowRect: Rect = {
    width: 400,
    height: 150,
    left: 0,
    top: 0,
  }

  constructor(steps: Step[]) {
    this.steps = steps
  }

  private run() {
    const step = this.steps[this.current]
    const el = step.el()
    if (!el) throw 'el is ' + el
    if (step.beforeActive) {
      step.beforeActive(rect => {
        rect = rect || this.getRect(el, step.placement)
        this.onNext && this.onNext(rect)
      }, this.getRect.bind(this, el, step.placement))
    } else {
      const rect = this.getRect(el, step.placement)
      this.onNext && this.onNext(rect)
    }
  }

  // 开始
  start() {
    this.current = 0
    this.run()
  }
  // 停止
  close() {
    this.current = 0
  }
  // 下一步
  next() {
    if (this.current < this.steps.length - 1) {
      this.current++
      this.run()
    }
  }
  // 上一步
  last() {
    if (this.current > 0) {
      this.current--
      this.run()
    }
  }
  toStep(current: number) {
    if (current >= 0 && current < this.steps.length - 1) {
      this.current = current
      this.run()
    }
  }

  getRect(el: HTMLElement, placement: Step['placement'] = 'bottom'): RectMap {
    let _targetRect = el.getBoundingClientRect()
    // 目标元素位置信息
    let targetRect: TargetRect = {
      width: _targetRect.width,
      height: _targetRect.height,
      left: _targetRect.left,
      right: window.innerWidth - _targetRect.left - _targetRect.width,
      top: _targetRect.top,
      bottom: window.innerHeight - _targetRect.top - _targetRect.height,
    }
    // 箭头位置
    let arrowRect: ArrowRect = {
      top: 0,
      left: targetRect.left + targetRect.width / 2 - 15,
      direction: placement,
      size: 30,
    }
    const arrowCenter = arrowRect.left + arrowRect.size / 2
    // 计算步骤窗口横向位置
    arrowCenter < this.infoWindowRect.width / 2
      ? (this.infoWindowRect.left = 0)
      : window.innerWidth - arrowCenter < this.infoWindowRect.width / 2
      ? (this.infoWindowRect.left = window.innerWidth - this.infoWindowRect.width)
      : (this.infoWindowRect.left = arrowCenter - this.infoWindowRect.width / 2)

    // 计算步骤窗口的位置
    switch (arrowRect.direction) {
      case 'bottom':
        this.infoWindowRect.top = targetRect.top + targetRect.height + this.padding + arrowRect.size / 2
        arrowRect.top = this.infoWindowRect.top - arrowRect.size / 2
        break
      case 'top':
        this.infoWindowRect.top = targetRect.top - arrowRect.size / 2 - this.infoWindowRect.height
        arrowRect.top = targetRect.top - arrowRect.size
        break
      case 'left':
        this.infoWindowRect.left = this.infoWindowRect.left - this.infoWindowRect.width
        arrowRect.top = this.infoWindowRect.top + this.infoWindowRect.height / 2
        arrowRect.left = this.infoWindowRect.left + this.infoWindowRect.width - arrowRect.size / 2
        break
    }

    const verify = (num: number) => (num < 0 ? 0 : num)
    // 遮罩层
    const maskRect: MaskRect = {
      center: {
        top: targetRect.top - this.padding,
        left: targetRect.left - this.padding,
        width: targetRect.width + this.padding * 2,
        height: targetRect.height + this.padding * 2,
      },
      top: {
        top: 0,
        left: 0,
        width: window.innerWidth,
        height: verify(targetRect.top - this.padding),
      },
      bottom: {
        top: targetRect.top + targetRect.height + this.padding,
        left: 0,
        width: window.innerWidth,
        height: verify(targetRect.bottom - this.padding),
      },
      left: {
        top: targetRect.top - this.padding,
        left: 0,
        width: verify(targetRect.left - this.padding),
        height: targetRect.height + this.padding * 2,
      },
      right: {
        top: targetRect.top - this.padding,
        left: targetRect.left + targetRect.width + this.padding,
        width: verify(targetRect.right - this.padding),
        height: targetRect.height + this.padding * 2,
      },
    }

    return {
      infoWindowRect: this.infoWindowRect,
      targetRect,
      arrowRect,
      maskRect,
    }
  }
}
