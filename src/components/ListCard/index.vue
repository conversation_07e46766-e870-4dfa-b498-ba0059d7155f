<script setup lang="ts">
import { computed } from 'vue'
import type { ProductGetFinishProductData } from '@/api/finishedProductInformation/rule'

interface PropsType {
  list: ProductGetFinishProductData[] | null
  pageConfig: { size: number, page: number, total: number, handleSizeChange: (val: any) => any, handleCurrentChange: (val: any) => any }
  // eslint-disable-next-line vue/prop-name-casing
  url_show: boolean
}
const props = withDefaults(defineProps<PropsType>(), {
  list: null,
  pageConfig: () => ({
    size: 10,
    page: 1,
    total: 0,
    handleSizeChange: (val: any) => val,
    handleCurrentChange: (val: any) => val,
  }),
  url_show: true,
})

const emits = defineEmits(['click'])

function handDetail(row: any) {
  emits('click', row)
}
const dataList = computed<any[]>(() => {
  return props.list || []
})
</script>

<template>
  <div class="product_list">
    <div v-for="item in dataList" :key="item.id" class="product_card" @click="handDetail(item)">
      <div class="head">
        <div v-if="url_show && item.main_texture_url" class="imageClass" @click.stop="($event:any) => $event.stopPropagation()">
          <el-image class="imageClass" :preview-src-list="[item.texture_url]" :src="item.main_texture_url" fit="cover" />
        </div>
        <div class="name">
          <slot name="header" :row="item" />
        </div>
      </div>
      <div class="con">
        <slot name="body" :row="item" />
      </div>
    </div>
  </div>
  <div class="w-full flex justify-end mt-[10px]">
    <el-pagination
      :current-page="props.pageConfig.page"
      :page-sizes="[1, 10, 50, 100, 200, 300, 400, 600, 1000]"
      :page-size="props.pageConfig.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="props.pageConfig.total"
      @size-change="props.pageConfig.handleSizeChange"
      @current-change="props.pageConfig.handleCurrentChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.product_list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20px;
  .product_card {
    box-shadow: 1px 0px 11px #d1d1d1;
    -moz-box-shadow: 1px 0px 11px #d1d1d1;
    -webkit-box-shadow: 1px 0px 11px #d1d1d1;
    border-radius: 5px;
    font-size: 13px;
    cursor: pointer;
    .head {
      padding: 10px;
      display: flex;
      align-items: center;
      background-color: #e9f2ffff;
      .imageClass {
        border-radius: 5px;
        width: 100px;
        height: 60px;
      }
      .name {
        font-size: 13px;
        font-weight: bold;
        margin-left: 10px;
      }
    }
    .con {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      padding: 10px;
      font-size: 12px;
      .self {
        grid-column: 1 / -1;
      }
      .con_item {
        margin-bottom: 7px;
        span {
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
}
</style>
