<template>
  <div :class="{ con_item: true, self: props.self }">
    <span>{{ props.label }}:</span>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
interface PropsType {
  label: string
  self: boolean
}
const props = withDefaults(defineProps<PropsType>(), {
  label: '',
  self: false,
})
</script>
<style lang="scss" setup>
.self {
  grid-column: 1 / -1;
}
.con_item {
  margin-bottom: 7px;
  span {
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
