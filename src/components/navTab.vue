<template>
  <el-scrollbar ref="scrollRef">
    <div class="flex_box" id="ele">
      <div
        ref="item_tab"
        @contextmenu.prevent="handleContextMenu(index)"
        v-for="(item, index) in state.tabs"
        :key="index"
        :class="item?.isActive ? 'item_tab_active' : 'item_tab'"
        @click="handNav(item, $event, index)"
        draggable="true"
        @dragstart="handleDragStart($event, index)"
        @dragover="handleDragOver($event, index)"
        @drop="handleDrop($event)"
      >
        <el-dropdown trigger="contextmenu" placement="bottom-end">
          <div class="flex items-center">
            <div class="item_font" :style="{ 'margin-right': index === 0 ? '24px' : '8px' }">{{ getTitle(item) }}</div>
            <el-icon v-if="index !== 0" @click.stop="handClose(item, index)" style="margin-right: 8px"><Close /></el-icon>
          </div>
          <template #dropdown v-if="item.showTabs">
            <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(1, item, index)">关闭左侧</el-dropdown-item>
            <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(2, item, index)">关闭右侧</el-dropdown-item>
            <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(3, item, index)">关闭其他</el-dropdown-item>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { CircleCloseFilled, Close } from '@element-plus/icons-vue'
const state = reactive<any>({
  tabs: [
    {
      path: 'Dashboard',
      title: '首页',
      query: {},
      params: {},
    },
  ],
  dragIndex: null,
})

const router = useRouter()

const scrollRef = ref()

watch(
  () => router.currentRoute.value,
  () => {
    const existArr = state.tabs.filter((item: any) => {
      return item.path === router.currentRoute.value.name && item.query?.id === router.currentRoute.value.query?.id && item?.params?.id === router.currentRoute.value.params?.id
    })

    if (!existArr.length) {
      state.tabs.push({
        path: router.currentRoute.value.name,
        title: router.currentRoute.value.meta.title,
        query: router.currentRoute.value.query,
        params: router.currentRoute.value.params,
      })
      nextTick(() => {
        if (state.tabs.length > 7) {
          scrollRef.value?.scrollTo({
            left: 50000,
            behavior: 'instant',
          })
        }
      })
    }

    state.tabs.map((item: any) => {
      if (item.title === router.currentRoute.value.meta.title && item.query?.id === router.currentRoute.value.query?.id && item?.params?.id === router.currentRoute.value.params?.id) {
        item.isActive = true
      } else {
        item.isActive = false
      }
      return item
    })
  },
  { immediate: true }
)

const item_tab = ref()

// const distance = ref()

const handNav = (it: any, e: any, index: number) => {
  // const targetElement = item_tab.value[index]
  // const width = document.getElementById('ele')?.clientWidth as number

  // distance.value = targetElement
  nextTick(() => {
    // if (e.currentTarget.offsetLeft < width / 2) {
    //   scrollRef.value?.scrollTo({
    //     left: 0,
    //     behavior: 'smooth',
    //   })
    // } else {
    //   scrollRef.value?.scrollTo({
    //     left: 300,
    //     behavior: 'smooth',
    //   })
    // }
  })
  state.tabs.map((item: any) => {
    if (item.title === it.title && item.query?.id === router.currentRoute.value.query?.id && item?.params?.id === router.currentRoute.value.params?.id) {
      item.isActive = true
    } else {
      item.isActive = false
    }
    return item
  })
  router.push({
    name: it.path,
    query: it?.query || {},
    params: it?.params || {},
  })
}

const getTitle = (item: any) => {
  if ((Object.keys(item?.query).length === 0 && Object.keys(item?.params).length === 0) || (!item.query.id && Object.keys(item?.params).length === 0)) {
    return item.title
  } else {
    return `${item.query?.id?.toString().substring(0, 3) || item.params?.id?.toString().substring(0, 3)}:${item.title}`
  }
}

// 关闭选项卡
const handClose = (item: any, index: number) => {
  if (index !== 0 && item.title === router.currentRoute.value.meta.title && item.query?.id === router.currentRoute.value.query?.id && item?.params?.id === router.currentRoute.value.params?.id) {
    router.push({
      name: state.tabs[index - 1].path,
      query: state.tabs[index - 1].query,
      params: state.tabs[index - 1].params,
    })
    state.tabs.splice(index, 1)
  } else {
    state.tabs.splice(index, 1)
  }
}

const handleContextMenu = (itemTabIndex: number) => {
  if (itemTabIndex === 0) return
  state.tabs = state.tabs.map((item: any, index: number) => {
    if (index === itemTabIndex) {
      item.showTabs = true
    } else {
      item.showTabs = false
    }
    return item
  })
}

const handCloseClear = (val: number, itemTab: any, itemTabIndex: number) => {
  if (val === 1) {
    // TODO:处理激活下删除左侧的选项
    const before = state.tabs.slice(0, itemTabIndex + 1)
    const result = before.filter((item: any, i: number) => i === 0 || i === itemTabIndex)
    const after = state.tabs.slice(itemTabIndex + 1)
    state.tabs = result.concat(after)
    // TODO:处理不是激活下删除左侧的选项
    if (!itemTab.isActive) {
      getNewTabs()
    }
  } else if (val === 2) {
    // TODO:处理激活下删除右侧的选项
    state.tabs.splice(itemTabIndex + 1, state.tabs.length - itemTabIndex - 1)
    // TODO:处理不是激活下删除右侧的选项
    if (!itemTab.isActive) {
      getNewTabs()
    }
  } else if (val === 3) {
    state.tabs = state.tabs.filter((item: any, index: number) => {
      return itemTabIndex === index || item.title === '首页'
    })
    getNewTabs()
  }
}

const getNewTabs = () => {
  const lengthIndex = state.tabs.length - 1

  state.tabs.map((item: any, index: number) => {
    if (lengthIndex === index) {
      item.isActive = true
    } else {
      item.isActive = false
    }
  })
  router.push({
    name: state.tabs[lengthIndex].path,
    query: state.tabs[lengthIndex].query,
    params: state.tabs[lengthIndex].params,
  })
}

const handleDragStart = (event: any, index: number) => {
  state.dragIndex = index // 设置正在拖动的元素的索引
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', index)
}

const handleDragOver = (event: any, index: number) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
  const targetIndex = index // 获取目标位置的索引
  if (state.dragIndex !== targetIndex) {
    // 交换元素位置
    // const targetElement = state.tabs[targetIndex]
    const dragElement = state.tabs[state.dragIndex]
    if (targetIndex > state.dragIndex) {
      state.tabs.splice(targetIndex + 1, 0, dragElement)
      state.tabs.splice(state.dragIndex, 1)
    } else {
      state.tabs.splice(state.dragIndex, 1)
      state.tabs.splice(targetIndex, 0, dragElement)
    }
    state.dragIndex = targetIndex
  }
}

const handleDrop = (event: any) => {
  event.stopPropagation()
}
</script>

<style lang="scss" scoped>
.flex_box {
  //   overflow: hidden;
  //   overflow-x: auto;
  //   white-space: nowrap;
  //   width: 100%;
  //   display: inline-flex;
  display: flex;

  .item_tab {
    transition: all 0.3s;
    flex-shrink: 0;
    // padding: 10 8 10 24px;
    max-width: 300px;
    height: 46px;
    margin-right: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 3px 9px -4px rgba(84, 130, 253, 0.16);
    border-radius: 8px 8px 0px 0px;
    opacity: 0.5;
    .item_font {
      margin-left: 24px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      color: rgba(0, 0, 0, 0.8);
      margin-right: 8px;
    }
  }
  .item_tab_active {
    flex-shrink: 0;
    transition: all 0.3s;

    max-width: 300px;
    height: 46px;
    margin-right: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    background: #ffffff;
    box-shadow: 0px 3px 9px -4px rgba(84, 130, 253, 0.16);
    border-radius: 8px 8px 0px 0px;
    .item_font {
      margin-left: 24px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      color: #0077ff;
      margin-right: 8px;

      font-weight: 600;
    }
  }
}
</style>
