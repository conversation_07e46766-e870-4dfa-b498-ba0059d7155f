<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'

defineOptions({
  name: 'SelectProductColorDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'product_color_name',
  disabled: false,
  showAddLink: true,
  api: 'GetFinishProductColorDropdownList',
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  // (e: 'update:product_color_id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const modelValue = defineModel<number>('modelValue', { required: true })
interface Props {
  query?: any
  defaultValue?: {
    id: number
    product_color_name?: string
    product_color_code?: string
  }
  field?: 'product_color_code' | 'product_color_name'
  api?: keyof typeof selectApi | ''
  disabled?: boolean
  // 是否显示新增客户链接
  showAddLink?: boolean
  isPushDefaultData?: boolean
}
const fieldName = {
  product_color_name: '色号名称',
  product_color_code: '色号编号',
}

const productColorRef = ref()

const componentRemoteSearch = reactive({
  product_color_name: '',
  product_color_code: '',
})
const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})
function handleInput(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  emits('changeValue', val)
  modelValue.value = val.id
  // emits('update:product_color_id', val.id)
}

const router = useRouter()
function handleSelectCustomer() {
  router.push({
    name: 'FinishedProductColorInformationAdd',
  })
}
defineExpose({
  productColorRef,
})
</script>

<template>
  <SelectDialog
    ref="productColorRef"
    v-model="modelValue"
    :label-field="field"
    :table-config="tableConfig"
    :api="api"
    :disabled="disabled"
    :is-push-default-data="isPushDefaultData"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :column-list="[
      {
        field: 'product_color_code',
        colGroupHeader: true,
        title: '色号编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'product_color_code',
            title: '色号编号',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'product_color_name',
        colGroupHeader: true,
        title: '色号名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'product_color_name',
            title: '色号名称',
            minWidth: 100,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: defaultValue ? {
          id: defaultValue?.id,
          product_color_name: defaultValue?.product_color_name,
          product_color_code: defaultValue?.product_color_code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInput"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
</template>

<style scoped></style>
