# SelectProductColorDialog

## 简介

SelectProductColorDialog 是一个基于 SelectDialog 的子组件，用于选择成品色号信息。它可以根据成品编号或成品名称进行搜索，并显示成品的相关信息，如电话和销售员。
默认支持根据name或code自动输入搜索，数据联动请通过changeValue事件自行处理

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| id | number \| null | null | 选中的成品的 id，可以使用 v-model 双向绑定 |
| defaultValue | object \| null | null | 默认的成品色号信息，包含 id  \| finish_product_name \| finish_product_code |
| field | 'finish_product_code' \| 'finish_product_name' | 'finish_product_name' | 用于显示的成品色号信息的字段，可以是成品编号或成品名称 |
| query | object | {} | 额外请求参数 |

## 事件

| 事件名                | 参数 | 说明 |
|--------------------| --- | --- |
| update:id | val: number | 当选中的成品 id 发生变化时，触发该事件，同时更新 id 的值 |
| changeValue        | val: number | 当选中的成品 id 发生变化时，触发该事件，可以用于自定义处理逻辑 |
| onInput            | val: string | 当输入的成品名称发生变化时，触发该事件，可以用于自定义处理逻辑 |

## 示例

```vue
<script setup lang="ts">
import SelectProductColorDialog from '@/components/SelectProductColorDialog/index.vue'

defineOptions({
  name: 'Example',
})

const id = ref<number | null>(null)

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
})

function changeProductSelect(val: number) {
  console.log('id changed to', val)
}

function changeProductInput(val: string) {
  console.log('input is', val)
}
</script>

<template>
  <SelectProductColorDialog
    v-model="id"
    field="finish_product_code"
    :default-value="{
      id: filterData.product_id,
      finish_product_name: componentRemoteSearch.finish_product_name,
      finish_product_code: componentRemoteSearch.finish_product_code,
    }"
    @change-value="changeProductSelect"
    @on-input="changeProductInput"
  />
</template>
