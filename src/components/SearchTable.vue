<template>
  <vxe-table size="mini" border stripe show-header-overflow show-overflow show-footer highlight-hover-row highlight-current-row highlight-hover-column highlight-current-columnn :data="searchList">
    <vxe-table-column type="checkbox" width="40" fixed="left"></vxe-table-column>
    <vxe-table-column type="seq" fixed="left" title="序号" width="50">
      <template #header>
        <span>序号</span>
      </template>
    </vxe-table-column>
    <template>
      <vxe-table-column
        v-for="(item, index) in columnList"
        :width="item?.width || ''"
        :fixed="item?.fixed"
        :key="index"
        :min-width="item.minWidth || ''"
        :field="item?.field"
        :title="item?.title"
        show-overflow
      >
        <template #default="{ row }" v-if="!item.showSearchTitle">
          <vxe-input v-if="item?.selectType === 'input'" v-model="item.searchFiledValue"></vxe-input>
          <SelectComponents
            v-else-if="item?.selectType === 'select'"
            :api="item?.selectApi || 'StatusListApi'"
            :label-field="item?.select_labelField || 'name'"
            :value-field="item?.select_valueField || 'id'"
            v-model="item.searchFiledValue"
            clearable
          />
          <SelectDate type="date" v-else-if="item.isDate" v-model="item.searchFiledValue"></SelectDate>
        </template>
      </vxe-table-column>
    </template>
  </vxe-table>
</template>
<script setup lang="ts">
import SelectComponents from '@/components/SelectComponents/index.vue'
import { computed, ref, watch } from 'vue'
import { debounce, getFilterData } from '@/common/util'
import SelectDate from './SelectDate/index.vue'

interface tableProps {
  columnList?: any // 行/列数据
}

const emit = defineEmits(['update:columnList', 'changList'])

const props = withDefaults(defineProps<tableProps>(), {
  columnList: [],
})

const valueList = computed(() => {
  return props.columnList
})

watch(
  () => props.columnList,
  debounce(() => {
    const result: any = valueList.value.filter(({ searchFiledValue, searchFiled }: any) => searchFiledValue && searchFiled)
    const result2: any = result.map(({ searchFiledValue, searchFiled }: any) => ({ searchFiledValue, searchFiled }))
    const result3: any = result2.reduce((acc: any, { searchFiled, searchFiledValue }: any) => ({ ...acc, [searchFiled]: searchFiledValue }), {})
    emit('changList', getFilterData(result3))
  }, 400),
  { deep: true }
)

const searchList = ref([{}])
</script>
