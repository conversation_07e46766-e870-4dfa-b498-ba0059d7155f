import { SaleModeEnum } from '@/enum/orderEnum'

export const saleModeArr = [
  {
    label: '大货',
    value: SaleModeEnum.Bulk,
  },
  {
    label: '剪板',
    value: SaleModeEnum.Plate,
  },
  {
    label: '客订-大货',
    value: SaleModeEnum.CustomerBulk,
  },
  {
    label: '客订-剪板',
    value: SaleModeEnum.CustomerPlate,
  },
]

// 判断是否为大货
export function isBulk(order_type: number) {
  return [SaleModeEnum.Bulk, SaleModeEnum.CustomerBulk].includes(
    Number(order_type),
  )
}
// 判断是否为客订
export function isCustomerBook(order_type: number) {
  return [
    SaleModeEnum.CustomerPlate,
    SaleModeEnum.CustomerBulk,
  ].includes(Number(order_type))
}
