<!--
  @LastEditTime: 2025-01-21 11:57:27
  @Description:  订单类型选择
 -->
<script setup lang="ts">
import { computed } from 'vue'
import { isCustomerBook, saleModeArr } from './common'

interface Props {
  showCustomerBook?: boolean // 是否显示客订
}
const props = withDefaults(defineProps<Props>(), {
  showCustomerBook: true,
})
const selectValue = defineModel('')
const filterSaleModeArr = computed(() => {
  return props.showCustomerBook ? saleModeArr : saleModeArr.filter(item => !isCustomerBook(item.value))
})
</script>

<template>
  <el-radio-group v-model="selectValue">
    <el-radio-button v-for="item in filterSaleModeArr" :key="item.value" plain :label="item.label" :value="item.value" />
  </el-radio-group>
</template>

<style lang="scss" scoped>

</style>
