<script setup lang="ts">
import { UserFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import screenfull from 'screenfull'
import { computed, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import MenuSelect from './components/menuSelect.vue'
import { AdminLogout } from '@/api/login'
import { useComponentsRequestStore, useKeepAliveStore } from '@/stores'
import { useGlobalData } from '@/stores/globalData'
import { useScanerStore } from '@/stores/scaner'
import { useScaner } from '@/use/useScaner'
import { routeListStore } from '@/stores/routerList'
import { findNodePathWithCache } from '@/common/util'
import { GetMenuResourceListApi } from '@/api/menu'
import CustomRequest from '@/components/CustomRequest/index.vue'

const routerList = routeListStore()
const activeName = ref('message')

const users = JSON.parse(localStorage.getItem('user') as string)
const showNotification = ref(true)

const state = reactive<any>({
  currentFullScreen: false,
  breadcrumb: [],
  searchValue: '',
  selectValue: '',
})

const router = useRouter()
// 获取菜单列表（包含按钮权限的）
const { fetchData: fetchDataMenu, data: dataMenu } = GetMenuResourceListApi()

// 根据获取的菜单列表，获取到当前路由的面包屑
async function getBreadcrumb() {
  const route = router.currentRoute.value
  // 首页
  if (route.name === 'Dashboard') {
    state.breadcrumb = [route.meta.title]
    return
  }

  // 读取菜单配置
  if (!dataMenu.value.total)
    await fetchDataMenu({ show_children: true })
  const resourceList = dataMenu.value?.list || []
  const breadcrumb = [...findNodePathWithCache(resourceList, route.name, 'resource_router_name', 'sub_menu', 'resource_list', route)]

  // 如果没有面包屑，则显示当前写死的路由名称
  if (!breadcrumb.length) {
    const arrBread: any = router.currentRoute.value.meta?.navName || []
    state.breadcrumb = arrBread.map((item: any, index: number) => {
      if (arrBread.length - 1 !== index)
        item = `${item}/`

      return item
    })
    return
  }

  state.breadcrumb = breadcrumb.map((item: any, index: number) => {
    let title = item.name
    if (breadcrumb.length - 1 !== index)
      title = `${title}/`

    return title
  })
}

watch(
  () => router.currentRoute.value,
  () => {
    // let arrBread: any = []

    // arrBread = router.currentRoute.value.meta?.navName || []
    // state.breadcrumb = arrBread.map((item: any, index: number) => {
    //   if (arrBread.length - 1 !== index)
    //     item = `${item}/`

    //   return item
    // })

    getBreadcrumb()
  },
  { immediate: true },
)

function handleRequestFullScreen() {
  if (screenfull.isEnabled) {
    if (screenfull.isFullscreen) {
      screenfull.exit()
      state.currentFullScreen = false
    }
    else {
      screenfull.request()
      state.currentFullScreen = true
    }
  }
}

const { fetchData: outFetch, success: outSuccess, msg: outMsg } = AdminLogout()

async function handleLogout() {
  await outFetch()
  if (outSuccess.value) {
    // 判断有无已看漫游导航
    const transferSalesTour = localStorage.getItem('transferSalesTour')
    if (transferSalesTour) {
      localStorage.clear()
      localStorage.setItem('transferSalesTour', transferSalesTour)
    }
    else {
      localStorage.clear()
    }
    // 重置路由
    routerList.clear()
    router.push({ name: 'Login' })
    ElMessage.success('成功')
  }
  else {
    ElMessage.error(outMsg.value)
  }
}

const keepAliveStore = useKeepAliveStore()
const componentsRequestStore = useComponentsRequestStore()
function refreshHand() {
  ElMessageBox.confirm('刷新当前页面所填未提交内容将会消失,确定刷新?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    componentsRequestStore.count = {}
    // 获取当前路由名称
    const name = router.currentRoute.value.name as string
    keepAliveStore.reCachePage(name)
  })
}

const scanerStore = useScanerStore()
const defaultScaner = computed(() => scanerStore.defaultScaner)
const {
  open,
  getList,
  readyState,
  sendData,
  onMessage,
  // close,
} = useScaner('ws://127.0.0.1:10008')

watch(
  () => readyState.value,
  (val) => {
    if (val === 1) {
      // 链接成功后 自动绑定扫描设备
      getList()
      setTimeout(() => {
        sendData(`open|${defaultScaner.value?.path}`)
      }, 2000)
    }
    else if ([2, 3].includes(val)) {
      // 断线后5秒重连
      setTimeout(() => {
        open()
      }, 5000)
    }
  },
)
const globalData = useGlobalData()
onMessage((val: any) => {
  const obj = JSON.parse(val)
  if (obj.type === 'scan') {
    ElMessage({
      type: 'info',
      message: `${obj.data}`,
    })
    handleScanQrCodeOrBarCode(obj.data)
    globalData.setScanerCode(obj.data)
  }
  else if (obj.type === 'list') {
    const scaner_list: any[] = []
    let i = 0
    obj.data.map((v: any) => {
      scaner_list.push({
        index: i,
        ...v,
      })
      i++
    })
    scanerStore.setScanerList(scaner_list)
  }
})

/**
 * 扫码跳转
 */
async function handleScanQrCodeOrBarCode(_code: any) {
  // const newCode = code.trim()

  // scanHandler(newCode)
}
</script>

<template>
  <div class="top-bar-nav">
    <div class="breadcrumb">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <div class="flex">
          <div v-for="(item, index) in state.breadcrumb" :key="index" :class="state.breadcrumb.length - 1 === index ? 'item_active' : 'item_box'">
            {{ item }}
          </div>
        </div>
      </el-breadcrumb>
    </div>
    <div class="top-bar-oper">
      <!-- <el-input input-style="background-color: #f5f5f5" style="background-color: #f5f5f5" v-model="state.searchValue" class="w-50 m-2" clearable placeholder="请输入搜索内容">
        <template #prefix>
          <el-icon color="#939393" class="el-input__icon"><search /></el-icon>
        </template>
      </el-input> -->
      <!-- 搜索框 -->
      <MenuSelect />
      <!-- 刷新按钮 -->
      <el-icon class="ml-3 cursor-pointer" :size="19" @click="refreshHand">
        <RefreshLeft />
      </el-icon>
      <el-popover v-if="showNotification" placement="bottom" :width="300" trigger="hover" popper-class="top-bar-btn-notification">
        <template #reference>
          <div class="item" title="消息">
            <el-badge :value="0" :show-zero="false" type="danger">
              <el-icon>
                <Bell />
              </el-icon>
            </el-badge>
          </div>
        </template>

        <el-tabs v-model="activeName" class="notification">
          <el-tab-pane label="通知 (5)" name="notification">
            <div class="list">
              通知
              <p>asd</p>
              <p>asd</p>
              <p>asd</p>
              <p>asd</p>
              <p>asd</p>
            </div>
            <div class="more" @click="handleToNotification">
              进入通知列表
            </div>
          </el-tab-pane>
          <el-tab-pane label="消息" name="message">
            <div class="list">
              消息
            </div>
          </el-tab-pane>
          <el-tab-pane label="代办 (2)" name="wait">
            <div class="list">
              代办
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-popover>

      <div v-if="!state.currentFullScreen" class="item" title="全屏" @click="handleRequestFullScreen">
        <span>
          <el-icon>
            <FullScreen />
          </el-icon>
        </span>
      </div>
      <div v-if="state.currentFullScreen" class="item" title="退出全屏" @click="handleRequestFullScreen">
        <span>
          <i class="fas fa-compress" />
        </span>
      </div>

      <div class="item">
        <el-dropdown>
          <span class="el-dropdown-link">
            <div class="person_box">
              <el-avatar :size="30" :icon="UserFilled" />
              <div class="person_name">{{ users?.user?.user_name }}</div>
            </div>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleLogout">
                <div><i class="el-icon-switch-button" /></div>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
  <CustomRequest />
</template>

<style scoped>
.top-bar-nav ::v-deep(.el-input__wrapper) {
  background: #f5f5f5 !important;
  border: 1px solid #f5f5f5 !important;
}
.top-bar-nav ::v-deep(.el-input__wrapper.is-focus) {
  box-shadow: none !important;
}
.top-bar-nav ::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
.top-bar-nav .el-header {
  padding: 0 !important;
  background: #fff;
}
.top-bar-nav {
  align-items: center;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 4px 16px;
  background: #fff;
}

.item_active {
  color: #0e7eff;
}
.item_box {
  color: #b4b4b4;
}
.top-bar-oper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 4px 10px 4px 20px;
}
.top-bar-oper .item {
  margin-left: 5px;
  padding: 6px 8px;
  border-radius: 5px;
  outline: none;
  cursor: pointer;
  vertical-align: middle;
  transition: all 0.3s;
  user-select: none;
}
.top-bar-oper .item i {
  font-size: 18px;
  font-weight: 400;
}
.top-bar-oper .item .text {
  margin-left: 6px;
}
.top-bar-oper .person_box {
  min-width: 110px;
  max-width: 200px;
  display: flex;
  align-items: center;
}
.top-bar-oper .person_box .person_name {
  margin-left: 5px;
  font-size: 14px;
  font-weight: normal;
  color: #a1a1a1;
}

.top-bar-btn {
  display: inline-block;
}
</style>
