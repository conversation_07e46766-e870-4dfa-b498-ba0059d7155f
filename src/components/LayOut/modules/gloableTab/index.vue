<script lang="ts" setup>
import { CircleCloseFilled, Grid } from '@element-plus/icons-vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { routeListStore } from '@/stores/routerList'
import { useComponentsRequestStore, useKeepAliveStore } from '@/stores'

const state = reactive<any>({
  // tabs: [
  //   {
  //     path: 'Dashboard',
  //     title: '首页',
  //     closable: false,
  //     query: {},
  //     params: {},
  //   },
  // ],
  dragIndex: null,
  routerOptions: [],
  findTab: false,
  filterTab: [],
})

const currentTabs = ref(0)

const router = useRouter()

const routerList = routeListStore()

function flattenSubMenu(arr: any) {
  return arr?.reduce((flat: any, item: any) => {
    if (item.sub_menu.length > 0)
      flat = flat.concat(flattenSubMenu(item.sub_menu))

    if (item.resource_router_name !== '')
      flat.push(item)

    return flat
  }, [])
}

const routerOptions = computed(() => {
  return flattenSubMenu(routerList.list) ?? []
})

const keepAliveStore = useKeepAliveStore()
const componentsRequestStore = useComponentsRequestStore()

watch(
  () => router.currentRoute.value,
  (newRoute) => {
    const existArr = routerList.tabs.filter((item: any) => {
      return item.path === newRoute.name && item.query?.id === newRoute.query?.id && item?.params?.id === newRoute.params?.id
    })

    // TODO:先找出后端是否有返回这个路由
    state.findTab = routerOptions.value.some((item: any) => {
      return item.resource_router_name === newRoute.name
    })

    // TODO:过滤后拿到后端返回路由的对象
    if (state.findTab) {
      state.filterTab = routerOptions.value.filter((item: any) => {
        return item.resource_router_name === newRoute.name
      })
    }

    // TODO:如果找到了就用后端的绑定的路由名称，找不到就用我们自己前端路由表的名称
    if (!existArr.length) {
      routerList.tabs.push({
        path: newRoute.name,
        title: !state.findTab ? newRoute.meta.title : state.filterTab[0].name,
        query: newRoute.query,
        params: newRoute.params,
        closable: true,
      })
      // 第一次进入页面自动刷新一次
      componentsRequestStore.count = {}
      // 进入缓存
      keepAliveStore.list.push(newRoute?.name)

      // keepAliveStore.eCachePage()
    }

    routerList.tabs.map((item: any) => {
      item.isActive = item.path === newRoute.name && item.query?.id === newRoute.query?.id && item?.params?.id === newRoute.params?.id
      return item
    })

    nextTick(() => {
      getActive()
    })
  },
  { immediate: true },
)

function getTitle(item: any) {
  // 判断是否有query或者params的id参数，有的话把id截取前三位，拼接到title上
  if (item.query?.id || item.params?.id)
    return `${item.query?.id?.toString().substring(0, 3) || item.params?.id?.toString().substring(0, 3)}:${item.title}`

  return item.title
}

// 关闭选项卡
function handClose(val: any) {
  const path = routerList.tabs[val].path
  const queryId = routerList.tabs[val].query?.id
  const paramsId = routerList.tabs[val]?.params?.id
  if (val !== 0 && path === router.currentRoute.value.name && queryId === router.currentRoute.value.query?.id && paramsId === router.currentRoute.value.params?.id) {
    router.push({
      name: routerList.tabs[val - 1].path,
      query: routerList.tabs[val - 1].query,
      params: routerList.tabs[val - 1].params,
    })
  }
  const index = keepAliveStore.list.findIndex((item: any) => {
    return item === path
  })
  keepAliveStore.list.splice(index, 1)
  routerList.tabs.splice(val, 1)

  getActive()
}

function handNav(val: any) {
  routerList.tabs.map((item: any) => {
    item.isActive = item.title === routerList.tabs[val.index].title && item.query?.id === router.currentRoute.value.query?.id && item?.params?.id === router.currentRoute.value.params?.id
    return item
  })
  router.push({
    name: routerList.tabs[val.index].path,
    query: routerList.tabs[val.index]?.query || {},
    params: routerList.tabs[val.index]?.params || {},
  })
}

function handleContextMenu() {
  const currentIndex = routerList.tabs.findIndex((item: any) => {
    return item.isActive
  })

  if (currentIndex === 0)
    return
  routerList.tabs = routerList.tabs.map((item: any, index: number) => {
    item.showTabs = index === currentIndex
    return item
  })
}

function handCloseClear(val: number) {
  const curIndex = routerList.tabs.findIndex((item: any) => {
    return item.isActive
  })

  if (val === 1) {
    // TODO:处理激活下删除左侧的选项
    const before = routerList.tabs.slice(0, curIndex + 1)
    const result = before.filter((item: any, i: number) => i === 0 || i === curIndex)
    const after = routerList.tabs.slice(curIndex + 1)

    routerList.tabs = result.concat(after)

    getActive()

    // currentTabs.value = currentIndex
  }
  else if (val === 2) {
    // TODO:处理激活下删除右侧的选项
    routerList.tabs.splice(curIndex + 1, routerList.tabs.length - curIndex - 1)
    getActive()
  }
  else if (val === 3) {
    routerList.tabs = routerList.tabs.filter((item: any, index: number) => {
      return curIndex === index || item.title === '首页'
    })
    getActive()
  }
}

function getActive() {
  const currentIndex = routerList.tabs.findIndex((item: any) => {
    return item.isActive
  })

  currentTabs.value = currentIndex
}

function handBlack() {
  window.history.back()
}
</script>

<template>
  <div class="flexBox">
    <div style="width: 97%">
      <el-tabs v-model="currentTabs" type="card" style="--el-index-normal: '0'" @contextmenu.prevent="handleContextMenu" @tab-click="handNav" @tab-remove="handClose">
        <el-tab-pane v-for="(item, index) in routerList.tabs" :key="index" :closable="item.closable" stretch :label="getTitle(item)" :name="index" />
      </el-tabs>
    </div>
    <el-dropdown trigger="hover" placement="bottom-end" class="icon">
      <el-icon class="el-dropdown-link" :size="20">
        <Grid />
      </el-icon>
      <template #dropdown>
        <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(1)">
          关闭左侧
        </el-dropdown-item>
        <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(2)">
          关闭右侧
        </el-dropdown-item>
        <el-dropdown-item :icon="CircleCloseFilled" @click.stop="handCloseClear(3)">
          关闭其他
        </el-dropdown-item>
        <el-dropdown-item @click.stop="handBlack">
          返回上一页
        </el-dropdown-item>
      </template>
    </el-dropdown>
  </div>
</template>

<style scoped>
.customNav {
  z-index: 0;
}
.flexBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.icon {
  margin-right: 20px;
}
::v-deep(.el-tabs__item.is-active) {
  background: #ffffff !important;
}
::v-deep(.el-tabs__header) {
  margin-bottom: 0;
}
::v-deep(.el-tabs__item) {
  color: #c6c7c8;
}
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
</style>
