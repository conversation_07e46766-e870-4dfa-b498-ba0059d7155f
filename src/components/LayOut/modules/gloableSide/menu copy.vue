<template>
  <!-- <el-menu :collapse="state.isCollapse" router :default-active="state.currentPath.toString()" active-text-color="#ffffff" text-color="#ffffff" class="el-menu-vertical-demo">
    <middleMenu @handCollpase="handCollpase" :collapse="state.isCollapse" :menuList="state.menus" @hand-enter="handEnter" @hand-leave="handLeave"></middleMenu>
  </el-menu> -->
  <div :class="!state.isCollapse ? 'menu_mian' : 'el_main_active'">
    <div class="scroll_box" @scroll.passive="getScroll($event)">
      <!-- <el-scrollbar height="100vh" ref="scrollMenuRef" class="scroll_box"> -->
      <div class="flex items-center justify-center mb-[15px]">
        <el-image :style="[{ marginRight: !state.isCollapse ? '20px' : '0px' }]" class="w-[32px] h-[30px] imgStyle" fit="fill" :src="`${IMG_CND_Prefix}/public/login.png`"></el-image>
        <div v-if="!state.isCollapse" class="text-[#ffffff] font-medium text-[27px]">HCSCM</div>
      </div>
      <div class="border_line"></div>

      <div class="item_group" v-for="(item, index) in state.menus" :key="index">
        <template v-if="!item.hideType">
          <div v-if="!state.isCollapse">
            <div class="text-[12px] mt-[5px] ml-[10px] text-[#8da1b7] mb-[5px]">{{ item.kindNameLong }}</div>
            <div :class="item.isActive ? 'item_box_active' : 'item_box'" @mouseenter="handMouseenter(item, $event)" @click="handMenu(item, $event)">
              <div class="flex items-center">
                <component :is="item?.icon" class="menu-icon"></component>
                <div class="item_title">{{ item?.meta?.title }}</div>
              </div>
              <el-icon v-if="item.children" size="20" color="#ffffff"><ArrowRight /></el-icon>
            </div>
          </div>
          <div v-else>
            <div class="text-[12px] mt-[5px] ml-[20px] text-[#8da1b7] mb-[5px]">{{ item.kindNameShort }}</div>
            <div :class="item.isActive ? 'icon_box_acitve' : 'icon_box'" @mouseenter="handMouseenter(item, $event)" @click="handMenu(item, $event)">
              <component :is="item?.icon" class="menu-icon"></component>
            </div>
          </div>
        </template>
      </div>
      <div class="border_line" style="margin-top: 20px"></div>
      <div v-if="!state.isCollapse" class="flex items-center cursor-pointer justify-center mb-[30px]" @click="handCollpase(1)">
        <el-icon color="#ffffff" size="32"><Fold /></el-icon>
        <div class="ml-[20px] text-[#ffffff]">收起</div>
      </div>
      <div v-else class="flex items-center cursor-pointer justify-center" @click="handCollpase(2)">
        <el-icon color="#ffffff" size="32"><Expand /></el-icon>
      </div>
      <!-- </el-scrollbar> -->
    </div>
    <div v-if="state.hasChildren" @mouseleave="handMouseLeave">
      <div ref="element" id="elementId" :style="[state.firstTop]" class="children_big">
        <Child :childrenList="state.childrenList" @handChild="handChild"></Child>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IMG_CND_Prefix } from '@/common/constant'
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import Child from './components/child.vue'

// import middleMenu from './middleMenu.vue'
const props = defineProps({
  collpase: Boolean,
})

const router = useRouter()

const state = reactive<any>({
  menus: [],
  showChilden: false,
  isCollapse: false,
  currentPath: '',
  hasChildren: false,
  firstTop: {},
  scrollTop: '',
  scrollBottom: '',
  popupHeight: 300,
  childrenList: [],
  eleDom: '',
  screenHeight: '',
  clientY: '',
})

onMounted(() => {})

watch(
  () => router.currentRoute.value,
  () => {
    state.currentPath = router.currentRoute.value.fullPath
    state.menus = router.options.routes?.map((item: any) => {
      if (item.children?.length === 1 && (item.children[0]?.children?.length === 0 || !item.children[0]?.children)) {
        delete item.children
      }
      if (router.currentRoute.value.matched[0]?.name === item.name) {
        item.isActive = true
      } else {
        item.isActive = false
      }
      return item
    })
  },
  { immediate: true }
)

const element = ref()

// 鼠标移入事件
const handMouseenter = (item: any, refDom: any) => {
  state.childrenList = []
  if (item.children?.length) {
    state.hasChildren = true
    const arr: any[] = item?.children || []
    item.children.map((item: any, index: number) => {
      if (index === 0) {
        item.chilList = arr
      }
      return item
    })
    state.childrenList = [item.children[0]]
    //
  } else {
    state.hasChildren = false
    return
  }

  state.hasChildren = true
  state.eleDom = refDom.currentTarget.offsetTop
  state.clientY = refDom.clientY
  state.screenHeight = document.body.scrollHeight
  state.firstTop = {
    top: `${0}px`,
    // height: `${0}px`,
  }
  nextTick(() => {
    const height = document.getElementById('elementId')?.offsetHeight

    if (state.clientY < state.screenHeight / 2) {
      state.firstTop = {
        top: `${state.clientY}px`,
        maxHeight: `${state.screenHeight - state.clientY}px`,
      }
    } else {
      state.firstTop = {
        bottom: `${state.screenHeight - state.clientY}px`,
        maxHeight: `${state.clientY}px`,
      }
    }
  })
}

const handMenu = async (item: any, refDom: any) => {
  if (item.children?.length) {
    state.hasChildren = true
    const arr: any[] = item?.children || []
    item.children.map((item: any, index: number) => {
      if (index === 0) {
        item.chilList = arr
      }
      return item
    })
    state.childrenList = [item.children[0]]
  } else {
    state.hasChildren = false
  }
  state.menus.map((it: any) => {
    if (item.path === it.path) {
      it.isActive = true
    } else {
      it.isActive = false
    }
    return it
  })
  if (!item.children && !item.children?.length) {
    router.push({
      name: item.name,
    })
  }
}

const handChild = (item: any, index: number) => {
  if (!item.children || !item.children.length) {
    state.childrenList?.map((it: any) => {
      it.chilList.map((edg: any) => {
        if (edg.title == item.title) {
          edg.isActive = true
          return edg
        } else {
          edg.isActive = false
        }
        return it
      })
    })
    state.childrenList.splice(1, 6)
    state.menus.map((it: any) => {
      if (item.parentName === it.name) {
        it.isActive = true
      } else {
        it.isActive = false
      }
      return it
    })
    router.push({
      name: item.name,
    })
    state.hasChildren = false
    state.childrenList?.map((it: any) => {
      it.chilList.map((edg: any) => {
        edg.isActive = false
        return it
      })
    })
    return
  }

  if (!item.isActive) {
    state.childrenList.splice(1, 6)
    const arr: any[] = item?.children || []

    item.children?.map((item: any, index: number) => {
      if (index === 0) {
        item.chilList = arr
      }
      return item
    })
    const queryArr = []
    queryArr.push({
      icon: item?.icon,
      path: item.path,
      chilList: item.children[0]?.chilList,
      parentName: item.parentName,
    })
    state.childrenList = [...state.childrenList, ...queryArr]

    state.childrenList?.map((it: any) => {
      it.chilList.map((edg: any) => {
        if (edg.title === item.title) {
          edg.isActive = true
          return edg
        } else {
          edg.isActive = false
        }
        return it
      })
    })
    //   router.push({
    //   name: item.name,
    // })
    // state.hasChildren = false
  }
}

// 获取滚动条距离的高度
const getScroll = (event: any) => {
  const scrollBottom = event.target.scrollHeight - event.target.scrollTop - event.target.clientHeight
  const scrollHeightTop = event.target.scrollTop
  state.scrollBottom = scrollBottom
  state.scrollTop = scrollHeightTop
}

// 鼠标离开
const handMouseLeave = () => {
  state.hasChildren = false
  state.childrenList = []
}

// 获取弹出层高度
const getElementToPageTop = (el: any): void => {
  if (el.parentElement) {
    return getElementToPageTop(el.parentElement) + el.offsetTop
  }
  return el.offsetTop
}

const handCollpase = (val: number) => {
  if (val === 1) {
    state.isCollapse = true
    // state.hasChildren = false
  } else {
    state.isCollapse = false
  }
}
</script>
<style scoped lang="scss">
svg {
  width: 1em;
  height: 1em;
  margin-right: 5px;
}
// 记得要有这个，控制侧边菜单宽度，意思是折叠的时候，宽度自适应，不着折叠的时候宽度为200px
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}
.el-menu {
  padding: 5px;
  background: #012e5e;
  height: 100%;
  color: #fff;
}

.menu_mian {
  position: relative;
  padding: 5px;
  background: #032e5f;
  height: 100vh;
  min-width: 170px;
  transition: all 0.4s;
  .scroll_box {
    height: 100%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .border_line {
    margin: 5px;
    height: 2px;
    background: rgba(255, 255, 255, 0.14);
  }
  .item_box {
    padding: 10px;
    width: 100%;
    justify-content: space-between;
    height: 48px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .menu-icon {
      color: #fff;
    }
    .item_title {
      color: #fff;
      font-size: 14px;
    }
  }
  .item_box_active {
    background: #0071df;
    border-radius: 8px;
    padding: 10px;
    width: 100%;
    justify-content: space-between;
    height: 48px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .menu-icon {
      color: #fff;
    }
    .item_title {
      color: #fff;
      font-size: 14px;
    }
  }
  .item_box:hover {
    background: #01478e;
    border-radius: 8px;
  }
  .children_big {
    // width: 700px;
    left: 174px;
    position: absolute;
    z-index: 99;
    display: flex;
    // min-height: 50px;
    transition: all 0.4s;
    // max-height: 336px;
  }
}

.el_main_active {
  min-width: 72px;
  padding: 5px;
  background: #032e5f;
  height: 100vh;
  transition: all 0.4s;
  position: relative;
  .scroll_box {
    height: 100%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .menu-icon {
    color: #fff;
  }
  .border_line {
    margin: 5px;
    height: 2px;
    background: rgba(255, 255, 255, 0.14);
  }
  .icon_box {
    margin-bottom: 10px;
    width: 100%;
    height: 40px;
    justify-content: center;
    flex-direction: column;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
  }
  .icon_box:hover {
    background: #01478e;
    border-radius: 8px;
  }
  .icon_box_acitve {
    margin-bottom: 10px;

    width: 100%;
    height: 40px;
    justify-content: center;
    flex-direction: column;
    display: flex;
    align-items: center;
    cursor: pointer;
    background: #0071df;
    border-radius: 8px;
  }
  .children_big {
    // width: 700px;
    left: 75px;
    position: absolute;
    z-index: 99;
    display: flex;
    // height: 100%;
    // min-height: 50px;
    transition: all 0.4s;
    // max-height: 336px;
  }
}
</style>
