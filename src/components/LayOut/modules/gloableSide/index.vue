<script setup lang="ts">
import { computed, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import SubMenu from './components/subMenu.vue'
import { jointUrl } from '@/common/util'
import { useUserStore } from '@/stores/user'
import { routeListStore } from '@/stores/routerList'

const props = withDefaults(defineProps<MenuProp>(), {
  showChildrenPopup: false,
})
const { user: userInfo }: any = useUserStore()
const routerList = routeListStore()

interface MenuProp {
  showChildrenPopup: boolean
}

const icon = jointUrl(import.meta.env.VITE_APP_LOGO)
const iconWithName = jointUrl(import.meta.env.VITE_APP_LOGO_WITH_NAME_WHITE)

const router = useRouter()

const state = reactive<any>({
  menus: [],
  showChilden: false,
  isCollapse: false,
  currentPath: '',
  hasChildren: false,
  subMenu: [], // 下级菜单
  mouseEnterTimer: null, // 添加定时器变量
})

watch(
  () => props.showChildrenPopup,
  (newVal) => {
    if (!newVal)
      state.hasChildren = false
  },
)

watch(
  () => router.currentRoute.value,
  (newRoute) => {
    const userInfoSerializable = JSON.stringify(userInfo) || '{}'
    if (userInfoSerializable === '{}') {
      router.push({ name: 'Login' })
      return
    }
    state.currentPath = router.currentRoute.value.fullPath

    if (newRoute.fullPath === '/dashboard') {
      // 改为首页默认展开菜单
      state.isCollapse = false
    }
  },
  { immediate: true },
)

// 鼠标移入事件
function handMouseenter(item: any) {
  // 清除之前的定时器
  if (state.mouseEnterTimer) {
    state.mouseEnterTimer = null
    clearTimeout(state.mouseEnterTimer)
  }

  state.subMenu = []
  state.hasChildren = false
  if (item.sub_menu?.length) {
    const arr: any[] = item?.sub_menu || []
    item.sub_menu.map((item: any, index: number) => {
      if (index === 0)
        item.chilList = arr

      return item
    })
    state.subMenu = item.sub_menu
    state.hasChildren = true
    // 添加300ms延迟
    // state.mouseEnterTimer = setTimeout(() => {
    //   state.hasChildren = true
    // }, 300)
  }
  else {
    state.hasChildren = false
  }
}

// 点击菜单
async function handMenu(item: any) {
  if (item.sub_menu?.length) {
    state.hasChildren = true
    const arr: any[] = item?.sub_menu || []
    item.sub_menu.map((item: any, index: number) => {
      if (index === 0)
        item.chilList = arr

      return item
    })
  }
  else {
    state.hasChildren = false
  }
  if (!item.sub_menu || !item.sub_menu?.length) {
    router.push({
      name: item.resource_router_name,
    })
  }
}

// 点击跳转
function handleMenu(item: any) {
  router.push({
    name: item.resource_router_name,
  })
  state.hasChildren = false
}

// 鼠标离开
function handMouseLeave() {
  if (state.mouseEnterTimer) {
    state.mouseEnterTimer = null
    clearTimeout(state.mouseEnterTimer)
  }

  state.hasChildren = false
  state.subMenu = []
}

function handCollpase(val: number) {
  if (val === 1)
    state.isCollapse = true
  // state.hasChildren = false
  else
    state.isCollapse = false
}

const menus = computed(() => {
  return (
    routerList.list?.map((item: any) => {
      item.isActive = router.currentRoute.value.matched[0]?.name === item.name
      return item
    }) ?? []
  )
})

// 跳转到菜单栏设置
function handleSetting() {
  router.push({
    name: 'Menu',
  })
  state.hasChildren = false
}
</script>

<template>
  <div class="main flex max-w-screen h-full" :class="state.isCollapse ? 'mini-main' : ''" @mouseleave="handMouseLeave">
    <div class="max-h-screen main-left">
      <!-- 顶部logo -->
      <div class="main-left-top">
        <div class="flex justify-center items-center main-left-logo-wrapper">
          <el-image class="main-left-logo" :src="state.isCollapse ? icon : iconWithName" fit="contain" />
        </div>
        <div class="border_line w-[100%]" />
      </div>

      <!-- 菜单栏 -->
      <div class="scroll_box overflow-y-auto">
        <div v-for="(item, index) in menus" :key="index" class="item_group">
          <div
            class="item_box" :class="item.isActive ? 'item_box_active' : ''" @mouseenter="handMouseenter(item)"
            @click="handMenu(item)"
          >
            <svg-icon :name="item?.avatar_url" size="24px" color="#fff" />
            <div v-show="!state.isCollapse" class="item_title truncate">
              {{ item?.name }}
            </div>
          </div>
        </div>
      </div>

      <!-- 收起按钮 -->
      <div id="collapseBtn" class="sticky top-[100vh]">
        <div class="border_line w-[80%] ml-[10%]" />
        <div
          v-show="!state.isCollapse" class="flex items-center cursor-pointer justify-center py-[10px]"
          @click="handCollpase(1)"
        >
          <el-icon color="#ffffff" size="22">
            <Fold />
          </el-icon>
          <div class="ml-[10px] text-[#ffffff]">
            收起
          </div>
        </div>
        <div
          v-show="state.isCollapse" class="flex items-center cursor-pointer justify-center py-[10px]"
          @click="handCollpase(2)"
        >
          <el-icon color="#ffffff" size="22">
            <Expand />
          </el-icon>
        </div>
      </div>
    </div>
    <!-- 右边栏 -->
    <div v-show="state.hasChildren" class="main-right" @mouseleave="handMouseLeave">
      <div v-has="'Menu_edit'" class="main-right-set flex justify-end">
        <svg-icon
          class="cursor-pointer" name="xitongshezhi" size="24px" color="rgba(0,0,0,0.45)"
          @click="handleSetting"
        />
      </div>
      <div class="main-right-children">
        <div class="main-right-children-box">
          <SubMenu :list="state.subMenu" @hand-child="handleMenu" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/style/main.scss';

.main {
  position: relative;
  display: flex;
}

// 左边栏
.main-left {
  background: $base;
  height: 100vh;
  transition: all 0.4s;
  display: flex;
  flex-direction: column;

  .main-left-top {
    .main-left-logo {
      width: 120px;
      padding: 10px;
    }
  }

  .scroll_box {
    flex: 1;
    overflow-y: scroll;
  }

  .scroll_box::-webkit-scrollbar {
    display: none;
  }

  .border_line {
    height: 2px;
    background: rgba(255, 255, 255, 0.06);
  }

  .item_box {
    padding: 10px 24px;
    width: 100%;
    height: 48px;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .item_box .item_title {
    color: #fff;
    font-size: 14px;
    margin-left: 12px;
    flex: 1;
  }

  .item_box:hover {
    background: #115CAD;
  }
}

.mini-main .main-left {
  width: 48px;

  .item_box {
    padding: 10px 12px;
  }
}

/* 右边栏 */
.main-right {
  max-height: 100vh;
  min-height: 50vh;
  position: absolute;
  z-index: 99999;
  top: 0;
  left: -1px;
  margin-left: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transition: all 0.4s;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 0 12px 12px 0;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.05), 0px 4px 5px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.12);

  .main-right-children {
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
  }

  .main-right-children-box {
    display: flex;
    // flex-direction: column;
    flex-wrap: wrap;
    max-height: calc(100vh - 20px);
    writing-mode: vertical-lr;
    // width: max-content;
  }

  .main-right-set+.main-right-children .main-right-children-box{
    max-height: calc(100vh - 44px);
  }

}
</style>
