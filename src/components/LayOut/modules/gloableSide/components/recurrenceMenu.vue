<script lang="ts" setup>
interface List {
  path?: string
  icon?: any
  title?: string
  children?: any
  kindNameLong?: string
  kindNameShort?: string
}

interface Ptypes {
  menuList: List[]
  collapse?: boolean
}

const { menuList = [], collapse = false } = defineProps<Ptypes>()
</script>

<template>
  <template v-for="item in menuList" :key="item?.path">
    <div v-if="!collapse" class="text-[12px] mt-[15px] ml-[10px] text-[#8da1b7]">
      {{ item?.kindNameLong }}
    </div>
    <div v-if="collapse" class="text-[12px] mt-[15px] ml-[10px] text-[#8da1b7]">
      {{ item?.kindNameShort }}
    </div>

    <el-sub-menu v-if="item?.children && item?.children?.length > 0" :index="item?.path">
      <template #title>
        <el-icon class="menu-icon">
          <component :is="item?.icon" />
        </el-icon>
        <span>{{ item?.title }}</span>
      </template>

      <mideMenu :menu-list="item?.children" :index="item?.path" />
    </el-sub-menu>

    <el-menu-item v-else :index="item?.path">
      <component :is="item?.icon" class="menu-icon" />
      <template #title>
        {{ item?.title }}
      </template>
    </el-menu-item>
  </template>
</template>

<style>
.el-sub-menu__title {
  background-color: #032e5f !important;
}
.el-menu .el-menu--inline {
  background-color: #032e5f !important;
}
.el-menu-item.is-active {
  background-color: #0071df !important;
  border-radius: 8px;
}
.el-menu-item:hover {
  background-color: #0071df !important;
  border-radius: 8px;
}
.el-menu--popup {
  padding: 5px !important;
  border-radius: 8px !important;
  background-color: #032e5f !important;
}
</style>
