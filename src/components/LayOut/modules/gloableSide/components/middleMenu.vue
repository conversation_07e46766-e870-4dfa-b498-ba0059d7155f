<script setup lang="ts">
import mideMenu from './recurrenceMenu.vue'

interface List {
  path?: string
  icon?: any
  title?: string
}

interface Ptypes {
  menuList?: List[]
  collapse?: boolean
}

const { menuList = [], collapse = false } = defineProps<Ptypes>()

const emit = defineEmits(['handEnter', 'handLeave', 'handCollpase'])

function _handEnter() {
  emit('handEnter')
}

function _handLeave() {
  emit('handLeave')
}

function handCollpase(val: number) {
  emit('handCollpase', val)
}
</script>

<template>
  <div class="toggle-button relative" @click="handCollpase">
    |||
  </div>
  <div class="border_line" />
  <mideMenu :collapse="collapse" :menu-list="menuList" />
  <div class="border_line" />
  <div v-if="!collapse" class="flex items-center cursor-pointer justify-center" @click="handCollpase(1)">
    <el-icon class="" size="32">
      <Fold />
    </el-icon>
    <div class="ml-[20px]">
      收起
    </div>
  </div>
  <div v-else class="flex items-center cursor-pointer justify-center" @click="handCollpase(2)">
    <el-icon size="32">
      <Expand />
    </el-icon>
  </div>
</template>

<style scoped>
.border_line {
  margin: 5px;
  height: 2px;
  background: rgba(255, 255, 255, 0.14);
}
</style>
