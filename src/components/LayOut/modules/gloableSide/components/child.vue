<script lang="ts" setup>
interface ChildProps {
  childrenList: any
}

const props = withDefaults(defineProps<ChildProps>(), {
  childrenList: [],
})

const emit = defineEmits(['handChild'])

function handChild(item: any, index: number) {
  emit('handChild', item, index)
}
</script>

<template>
  <div v-for="(item, index) in props.childrenList" :key="index" class="flex">
    <div class="children_box">
      <el-scrollbar>
        <template v-for="it in item.chilList" :key="it.id">
          <div :class="it.isActive ? 'item_box_active' : 'item_box'" @click="handChild(it, index)">
            <div class="flex items-center">
              <!-- <el-icon :size="25">
                <component :is="it?.avatar_url" class="menu-icon"></component>
              </el-icon> -->
              <div class="item_title">
                {{ it?.name }}
              </div>
            </div>
            <el-icon v-if="it.sub_menu.length && it.sub_menu" size="20" color="#ffffff">
              <ArrowRight />
            </el-icon>
          </div>
        </template>
      </el-scrollbar>
    </div>
  </div>
</template>

<style scoped>
.children_box {
  padding: 5px;
  margin-right: 10px;
  width: 200px;
  background-color: #032e5f;
  border-radius: 8px;
  transition: all 0.4s;
}
.children_box .item_box {
  padding: 10px;
  width: 100%;
  justify-content: space-between;
  height: 48px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.children_box .item_box .menu-icon {
  color: #fff;
}
.children_box .item_box .item_title {
  margin-left: 10px;
  color: #fff;
  font-size: 14px;
}
.children_box .item_box_active {
  background: #0071df;
  border-radius: 8px;
  padding: 10px;
  width: 100%;
  justify-content: space-between;
  height: 48px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.children_box .item_box_active .menu-icon {
  color: #fff;
}
.children_box .item_box_active .item_title {
  margin-left: 10px;
  color: #fff;
  font-size: 14px;
}
.children_box .item_box:hover {
  background: #01478e;
  border-radius: 8px;
}
</style>
