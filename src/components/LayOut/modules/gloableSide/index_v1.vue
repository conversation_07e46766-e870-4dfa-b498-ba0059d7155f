<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useWindowSize } from '@vueuse/core'
import Child from './components/child.vue'
import SubMenu from './components/subMenu.vue'
import { jointUrl } from '@/common/util'
import { useUserStore } from '@/stores/user'
import { routeListStore } from '@/stores/routerList'

const props = withDefaults(defineProps<MenuProp>(), {
  showChildrenPopup: false,
})
const { user: userInfo }: any = useUserStore()
const routerList = routeListStore()

interface MenuProp {
  showChildrenPopup: boolean
}

const icon = import.meta.env.VITE_APP_LOGO
const iconWithName = jointUrl(import.meta.env.VITE_APP_LOGO_WITH_NAME_WHITE)

const router = useRouter()

const state = reactive<any>({
  menus: [],
  showChilden: false,
  isCollapse: false,
  currentPath: '',
  hasChildren: false,
  firstTop: {},
  scrollTop: '',
  scrollBottom: '',
  popupHeight: 300,
  childrenList: [],
  eleDom: '',
  screenHeight: '',
  clientY: '',
})

watch(
  () => props.showChildrenPopup,
  (newVal) => {
    if (!newVal)
      state.hasChildren = false
  },
)

watch(
  () => router.currentRoute.value,
  (newRoute) => {
    const userInfoSerializable = JSON.stringify(userInfo) || '{}'
    if (userInfoSerializable === '{}') {
      router.push({ name: 'Login' })
      return
    }
    state.currentPath = router.currentRoute.value.fullPath

    if (newRoute.fullPath === '/dashboard') {
      // 改为首页默认展开菜单
      state.isCollapse = false
    }
  },
  { immediate: true },
)

const element = ref()

// 鼠标移入事件
function handMouseenter(item: any, refDom: any) {
  state.childrenList = []
  if (item.sub_menu?.length) {
    state.hasChildren = true
    const arr: any[] = item?.sub_menu || []
    item.sub_menu.map((item: any, index: number) => {
      if (index === 0)
        item.chilList = arr

      return item
    })
    state.childrenList = [item.sub_menu[0]]
    //
  }
  else {
    state.hasChildren = false
    return
  }

  state.hasChildren = true
  // state.eleDom = refDom.currentTarget.offsetTop
  // state.clientY = refDom.clientY
  state.screenHeight = document.body.scrollHeight
  state.firstTop = {
    top: `${0}px`,
    // height: `${0}px`,
  }

  // 获取元素到屏幕顶部的距离然后定位
  const targetElement = refDom.target
  const targetRect = targetElement.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const targetTop = targetRect.top + scrollTop
  state.clientY = targetTop

  nextTick(() => {
    // const height = document.getElementById('elementId')?.offsetHeight
    if (state.clientY < state.screenHeight / 2) {
      state.firstTop = {
        top: `${state.clientY}px`,
        maxHeight: `${state.screenHeight - state.clientY}px`,
      }
    }
    else {
      // 48是元素的固定高度
      state.firstTop = {
        bottom: `${state.screenHeight - state.clientY - 48}px`,
        maxHeight: `${state.clientY}px`,
      }
    }

    //
  })
}

async function handMenu(item: any, refDom: any) {
  if (item.sub_menu?.length) {
    state.hasChildren = true
    const arr: any[] = item?.sub_menu || []
    item.sub_menu.map((item: any, index: number) => {
      if (index === 0)
        item.chilList = arr

      return item
    })
    state.childrenList = [item.sub_menu[0]]
  }
  else {
    state.hasChildren = false
  }
  // state.menus.map((it: any) => {
  //   if (item.path === it.path) {
  //     it.isActive = true
  //   } else {
  //     it.isActive = false
  //   }
  //   return it
  // })
  if (!item.sub_menu || !item.sub_menu?.length) {
    router.push({
      name: item.resource_router_name,
    })
  }
}

function handChild(item: any, _index: number) {
  if (!item.sub_menu || !item.sub_menu.length) {
    state.childrenList.splice(1, 6)
    router.push({
      name: item.resource_router_name,
    })
    state.hasChildren = false
    return
  }

  if (!item.isActive) {
    state.childrenList.splice(1, 6)
    const arr: any[] = item?.sub_menu || []

    item.sub_menu?.map((item: any, index: number) => {
      if (index === 0)
        item.chilList = arr

      return item
    })
    const queryArr = []
    queryArr.push({
      icon: item?.icon,
      path: item.path,
      chilList: item.sub_menu[0]?.chilList,
      parentName: item.parentName,
    })
    state.childrenList = [...state.childrenList, ...queryArr]
  }
}

// 获取滚动条距离的高度
function getScroll(event: any) {
  const scrollBottom = event.target.scrollHeight - event.target.scrollTop - event.target.clientHeight
  const scrollHeightTop = event.target.scrollTop
  state.scrollBottom = scrollBottom
  state.scrollTop = scrollHeightTop
}

// 鼠标离开
function handMouseLeave() {
  state.hasChildren = false
  state.childrenList = []
}

const menuHeight = ref(800)
function handCollpase(val: number) {
  if (val === 1)
    state.isCollapse = true
    // state.hasChildren = false
  else
    state.isCollapse = false
}

/**
 * 处理动态的菜单高度问题
 */
function handScreenHeightChange() {
  // 获取网页可呈现的高度
  const screenHeight = window.innerHeight
  // 获取logo和收缩按钮所占用的高度
  const logoHeight = document.querySelector('#logo')?.clientHeight
  const collapseBtnHeight = document.querySelector('#collapseBtn')?.clientHeight
  // 计算出菜单内容需要占用的高度
  menuHeight.value = screenHeight - logoHeight - collapseBtnHeight - 10
}

const menus = computed(() => {
  return (
    routerList.list?.map((item: any) => {
      item.isActive = router.currentRoute.value.matched[0]?.name === item.name
      return item
    }) ?? []
  )
})

onMounted(async () => {
  // 在dom生成后再去操作
  handScreenHeightChange()
  const { height } = useWindowSize()
  watch(height, () => {
    handScreenHeightChange()
  })
})
</script>

<template>
  <div :class="!state.isCollapse ? 'menu_mian' : 'el_main_active'" class="max-h-screen">
    <div id="logo" class="sticky top-0 py-[5px] h-16">
      <div class="flex justify-center bg-[#032e5f]">
        <el-image v-show="state.isCollapse" :style="[{ marginRight: !state.isCollapse ? '20px' : '0px' }]" class="w-[28.8px] h-[47px] imgStyle" fit="contain" :src="icon" />
        <el-image v-show="!state.isCollapse" class="text-[#ffffff] font-medium text-[22px] py-[7px] h-[47px]" :src="iconWithName" fit="contain" />
      </div>
      <div class="border_line w-[90%]" />
    </div>

    <div class="scroll_box overflow-y-auto" :style="`height:${menuHeight}px`" @scroll.passive="getScroll($event)">
      <div v-for="(item, index) in menus" :key="index" class="item_group">
        <div v-show="!state.isCollapse">
          <div :class="item.isActive ? 'item_box_active' : 'item_box'" @mouseenter="handMouseenter(item, $event)" @click="handMenu(item, $event)">
            <div class="flex items-center">
              <svg-icon :name="item?.avatar_url" size="26px" color="#fff" />
              <div class="item_title">
                {{ item?.name }}
              </div>
            </div>
            <el-icon v-if="item.sub_menu?.length && item.sub_menu" size="20" color="#ffffff">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
        <div v-show="state.isCollapse">
          <!-- <div class="text-[12px] mt-[5px] ml-[20px] text-[#8da1b7] mb-[5px]">{{ item.kindNameShort }}</div> -->
          <div :class="item.isActive ? 'icon_box_acitve' : 'icon_box'" @mouseenter="handMouseenter(item, $event)" @click="handMenu(item, $event)">
            <svg-icon :name="item?.avatar_url" size="26px" color="#fff" />
          </div>
        </div>
      </div>
    </div>

    <!--      收起按钮 -->
    <div id="collapseBtn" class="bg-[#032e5f] sticky top-[100vh]">
      <div class="border_line w-[90%]" />
      <div v-show="!state.isCollapse" class="flex items-center cursor-pointer justify-center" @click="handCollpase(1)">
        <el-icon color="#ffffff" size="32">
          <Fold />
        </el-icon>
        <div class="ml-[20px] text-[#ffffff]">
          收起
        </div>
      </div>
      <div v-show="state.isCollapse" class="flex items-center cursor-pointer justify-center" @click="handCollpase(2)">
        <el-icon color="#ffffff" size="32">
          <Expand />
        </el-icon>
      </div>
    </div>
    <!-- <div v-if="state.hasChildren" @mouseleave="handMouseLeave">
      <div id="elementId" ref="element" :style="[state.firstTop]" class="children_big">
        <Child :children-list="state.childrenList" @hand-child="handChild" />
      </div>
    </div> -->
    <div v-if="state.hasChildren" @mouseleave="handMouseLeave">
      <div id="elementId" ref="element" :style="[state.firstTop]" class="children_big">
        <SubMenu :children-list="state.childrenList" @hand-child="handChild" />
      </div>
    </div>
  </div>
</template>

<style scoped>
svg {
  margin-right: 5px;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}

.el-menu {
  padding: 5px;
  background: #012e5e;
  height: 100%;
  color: #fff;
}

.menu_mian {
  position: relative;
  padding: 0 5px;
  background: #032e5f;
  height: 100vh;
  min-width: 170px;
  transition: all 0.4s;
}
.menu_mian .scroll_box::-webkit-scrollbar {
  display: none;
}
.menu_mian .border_line {
  margin: 5px;
  height: 2px;
  background: rgba(255, 255, 255, 0.14);
}
.menu_mian .item_box {
  padding: 10px;
  width: 100%;
  justify-content: space-between;
  height: 48px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.menu_mian .item_box .menu-icon {
  color: #fff;
}
.menu_mian .item_box .item_title {
  color: #fff;
  font-size: 14px;
}
.menu_mian .item_box_active {
  background: #0071df;
  border-radius: 8px;
  padding: 10px;
  width: 100%;
  justify-content: space-between;
  height: 48px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.menu_mian .item_box_active .menu-icon {
  color: #fff;
}
.menu_mian .item_box_active .item_title {
  color: #fff;
  font-size: 14px;
}
.menu_mian .item_box:hover {
  background: #01478e;
  border-radius: 8px;
}
.menu_mian .children_big {
  left: 174px;
  position: absolute;
  z-index: 99999;
  display: flex;
  transition: all 0.4s;
}

.el_main_active {
  min-width: 72px;
  padding: 0 5px;
  background: #032e5f;
  height: 100vh;
  transition: all 0.4s;
  position: relative;
}
.el_main_active .scroll_box::-webkit-scrollbar {
  display: none;
}
.el_main_active .menu-icon {
  color: #fff;
}
.el_main_active .border_line {
  margin: 5px;
  height: 2px;
  background: rgba(255, 255, 255, 0.14);
}
.el_main_active .icon_box {
  margin-bottom: 10px;
  width: 100%;
  height: 40px;
  justify-content: center;
  flex-direction: column;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 8px;
}
.el_main_active .icon_box:hover {
  background: #01478e;
  border-radius: 8px;
}
.el_main_active .icon_box_acitve {
  margin-bottom: 10px;
  width: 100%;
  height: 40px;
  justify-content: center;
  flex-direction: column;
  display: flex;
  align-items: center;
  cursor: pointer;
  background: #0071df;
  border-radius: 8px;
}
.el_main_active .children_big {
  left: 75px;
  position: absolute;
  z-index: 99;
  display: flex;
  transition: all 0.4s;
}
</style>
