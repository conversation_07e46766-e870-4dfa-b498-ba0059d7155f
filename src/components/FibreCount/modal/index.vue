<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import { useRouter } from 'vue-router'
import FibreCount from '@/components/FibreCount/index.vue'

const emits = defineEmits(['handleSure'])
const state = reactive<any>({
  showModal: false,
  defaultList: [{ fibre_id: '', fibre_code: '', count: 0 }],
  ingredient_item: [] as any[],
  composition: '',
})
function handCancel() {
  state.showModal = false
}

const fibreCountRef = ref()
function fibreChange(val: any) {
  state.ingredient_item = val?.composition_arr
  state.composition = val?.composition
}

function handleSure() {
  let count = 0
  const list = state.ingredient_item?.map((item: any) => {
    count = currency(item.count).add(count).value
    return { ...item, count: item.count }
  })

  if (count < 100)
    return ElMessage.error('纤维比例不能小于100,必须等于100')
  if (count > 100)
    return ElMessage.error('纤维比例不能大于100,必须等于100')

  emits('handleSure', { list, composition: state.composition })
}

// function compositionAdd() {
//   fibreCountRef.value.compositionAdd(null)
// }
const router = useRouter()
function handleClick() {
  router.push({
    path: '/systemTools/detail/10003',
  })
}

watch(
  () => state.showModal,
  (val) => {
    if (val)
      fibreCountRef.value?.updateIncomingData() // 重新渲染已选择的数据
  },
  { immediate: true },
)
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer lock-view title="成分比例" :mask="false" :esc-closable="true" width="500px">
    <div class="w-full mb-2">
      <!--      <el-button :icon="Plus" type="primary" @click="compositionAdd" /> -->
      <!-- {{ state.composition }} -->
      <el-link type="primary" :underline="false" @click="handleClick">
        补充成分选择>
      </el-link>
    </div>
    <FibreCount ref="fibreCountRef" :default-list="state.defaultList" @change="fibreChange" />
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
