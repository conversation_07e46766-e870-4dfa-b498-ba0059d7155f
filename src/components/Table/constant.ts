import type { TableConfig } from '@/components/Table/type'

const defaultPageSizes = [50, 100, 500, 1000]
export const INIT_DATA = {
  loading: false,
  page: 1,
  size: 400,
  total: 0,
  showPagition: false,
  showFooterBtn: false,
  minHeight: 100,
  defaultPageSize: defaultPageSizes[0],
  pageSizes: defaultPageSizes,
  scrollY: { enabled: true, gt: 40 },
  sortConfig: {

  },
  // 页码跳转方法
  handleSizeChange: (val: number) => {
    return val
  },
  // 上、下一页
  handleCurrentChange: (val: number) => {
    return val
  },
  // 全选
  handAllSelect: (val: any) => {
    return val
  },
  // 单选
  handleSelectionChange: (val: any) => {
    return val
  },
  // 底部合并方法
  footerMethod: (val: any) => {
    return val
  },
  footerColspanMethod: (val: any) => {
    return val
  },
  // 单选框事件
  radioChangeEvent: (val: any) => {
    return val
  },
  // 单元格双击事件
  cellDBLClickEvent: (val: any) => {
    return val
  },
  // 单元格单击事件
  cellClick: (val: any) => {
    return val
  },
  clickFooterItem: () => {
    return ''
  },
  handUpdateRow: (row: any) => {
    return row
  },
  // table的高度
  height: '100%', // 外部传进，不然不生效
  // 是否显示多选
  showCheckBox: false,
  // 是否显示单选
  showRadio: false,
  // 单选框的宽度
  radioWidth: '48',
  // 展示操作栏
  showOperate: false,
  // 操作栏的宽度
  operateWidth: '80',
  // 是否展示前面的序号
  showSlotNums: true,
  // 合并表头表格
  showSpanHeader: false,
  // 是否显示拖拽图标
  showSort: false,
  // 否需要表格展开或者收缩
  expand: false,
  expandConfig: {},
  // 展示或者收缩插槽的名字
  expandName: '',
  // 是否展示需要已勾选、回选的数据
  backSelection: false,
  // 展示要勾选回去的数据源
  backSelectionArr: [],
  // 设置列颜色：格式{字段名:自定义类}
  rowColor: null,
  // 批量修改提交事件
  bulkSubmit: (val: any) => {
    return val
  },
  // 展开或者收缩的方法
  toggleExpandChangeEvent: (val: any) => {
    return val
  },
  // 复选框配置
  checkboxConfig: { checkField: 'selected', highlight: true, reserve: true },
  // 合并列
  colspanMethod: (val: any) => {
    return val
  },
  show_footer: true,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  filterStatus: true, // 全局配置是否显示表头筛选
  fieldApiKey: '', // 获取根据key获取字段
  showFixed: true, // 自定义字段组件是否设置固定
  needCellColor: true,
}

export const DEFAULT_TABLE_CONFIG: TableConfig = {
  loading: false,
  page: 1,
  size: 400,
  total: 0,
  showPagition: false,
  showFooterBtn: false,
  pageSizes: [1, 10, 50, 100, 200, 300, 400, 500, 600, 1000],
  minHeight: '100',
  // 页码跳转方法
  handleSizeChange: (val: any) => {
    return val
  },
  // 上、下一页
  handleCurrentChange: (val: any) => {
    return val
  },
  // 全选
  handAllSelect: (val: any) => {
    return val
  },
  // 单选
  handleSelectionChange: (val: any) => {
    return val
  },
  // 底部合并方法
  footerMethod: (val: any) => {
    return val
  },
  footerColspanMethod: (val: any) => {
    return val
  },
  // 单选框事件
  radioChangeEvent: (val: any) => {
    return val
  },
  // 单元格单击事件
  cellClick: (val: any) => {
    return val
  },
  // table的高度
  height: '100%', // 外部传进，不然不生效
  // 是否显示多选
  showCheckBox: false,
  // 是否显示单选
  showRadio: false,
  // 展示操作栏
  showOperate: false,
  // 操作栏的宽度
  operateWidth: '80',
  // 是否展示前面的序号
  showSlotNums: true,
  // 合并表头表格
  showSpanHeader: false,
  // 是否显示拖拽图标
  showSort: false,
  // 否需要表格展开或者收缩
  expand: false,
  // 展示或者收缩插槽的名字
  expandName: '',
  // 是否展示需要已勾选、回选的数据
  backSelection: false,
  // 展示要勾选回去的数据源
  backSelectionArr: [],
  // 设置列颜色：格式{字段名:自定义类}
  rowColor: null,
  // 批量修改提交事件
  bulkSubmit: (val: any) => {
    return val
  },
  // 展开或者收缩的方法
  toggleExpandChangeEvent: (val: any) => {
    return val
  },
  // 复选框配置
  checkboxConfig: { checkField: 'selected', highlight: true, reserve: true },
  radioConfig: {},
  editConfig: {},
  rowConfig: {},
  validConfig: {},
  keyboardConfig: {},
  // 合并列
  colspanMethod: (val: any) => {
    return val
  },
  show_footer: true,
  footerCellClassName: () => {
    return ''
  },
  clickFooterItem: () => {
    return ''
  },
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  rowIndex: '',
  filterStatus: true,
  fieldApiKey: '',
  needCellColor: true,
}
