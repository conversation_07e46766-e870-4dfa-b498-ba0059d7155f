import { ElLink, ElMessage } from 'element-plus'
import { h } from 'vue'
import { VxeUI } from 'vxe-pc-ui'
import router from '@/router'

/**
 * 自定义跳转渲染器
 * @param value 显示的值，默认当前field
 * @param routerName 路由名，默认FinishedProductInformationDetail
 * @param params 路由传参：(row:any)=>{}
 * @param query 路由查询参数：(row:any)=>{}
 */
VxeUI.renderer.add('LinkJump', {
  renderTableDefault(renderOpts, params) {
    return h(
      ElLink,
      {
        type: 'primary',
        onClick: () => {
          try {
            router.push({
              name: renderOpts.props?.routerName || 'FinishedProductInformationDetail',
              params: renderOpts.props?.params ? renderOpts.props?.params(params.row) : {},
              query: renderOpts.props?.query ? renderOpts.props?.query(params.row) : {},
            })
          }
          catch (error) {
            return ElMessage.warning('跳转失败，检查路由')
          }
        },
      },
      renderOpts.props?.value || params.row[params.column.field],
    )
  },
})
