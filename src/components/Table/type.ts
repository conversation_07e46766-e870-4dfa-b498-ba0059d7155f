import type {
  VxeColumnProps,
  VxeTableEvents,
  VxeTablePropTypes,
} from 'vxe-table'
import type {
  VxeInputEvents,
} from 'vxe-pc-ui'
import type { Component } from 'vue'

export interface TableConfig {
  loading: boolean
  page: number
  defaultPageSize: number
  size: number
  total: number
  showPagition: boolean
  pageSizes: number[]
  showFooterBtn?: boolean
  minHeight: number | string
  maxHeight: number | string
  scrollY: VxeTablePropTypes.ScrollY
  // 页码跳转方法
  handleSizeChange: (val: number) => void
  // 上、下一页
  handleCurrentChange: (val: number) => void
  // 全选
  handAllSelect: VxeTableEvents.CheckboxAll
  // 单选
  handleSelectionChange: VxeTableEvents.CheckboxChange
  // 底部合并方法
  footerMethod: VxeTablePropTypes.FooterMethod
  footerColspanMethod: VxeTablePropTypes.FooterSpanMethod
  // 单选框事件
  radioChangeEvent: VxeTableEvents.RadioChange
  // 单元格单击事件
  cellClick: VxeTableEvents.CellClick
  // table的高度
  height: string | number // 外部传进，不然不生效
  // 是否显示多选
  showCheckBox: boolean
  // 是否显示单选
  showRadio: boolean
  // 单选的宽度
  radioWidth: string | number
  // 展示操作栏
  showOperate: boolean
  // 操作栏的宽度
  operateWidth: string | number
  // 是否展示前面的序号
  showSlotNums: boolean
  // 合并表头表格
  showSpanHeader: boolean
  // 是否显示拖拽图标
  showSort: boolean
  // 否需要表格展开或者收缩
  expand: boolean
  // 展示或者收缩插槽的名字
  expandConfig: VxeTablePropTypes.ExpandConfig
  expandName: string
  // 是否展示需要已勾选、回选的数据
  backSelection: false
  // 展示要勾选回去的数据源
  backSelectionArr: any[]
  // 设置列颜色：格式{字段名:自定义类}
  rowColor: CSSPageRule
  // 批量修改提交事件
  bulkSubmit: <T>(val: T) => T
  // 展开或者收缩的方法
  toggleExpandChangeEvent: VxeTableEvents.ToggleRowExpand
  autoResize: boolean
  // 复选框配置
  checkboxConfig: VxeTablePropTypes.CheckboxConfig
  // 单选框配置
  radioConfig: VxeTablePropTypes.RadioConfig
  rowConfig: VxeTablePropTypes.RowConfig
  sortConfig: VxeTablePropTypes.SortConfig
  // 合并列
  colspanMethod: VxeTablePropTypes.SpanMethod
  cellDBLClickEvent: VxeTableEvents.CellDblclick
  editConfig: VxeTablePropTypes.EditConfig
  validConfig: VxeTablePropTypes.ValidConfig
  show_footer: boolean
  footerCellClassName: VxeTablePropTypes.FooterCellClassName
  pageLayout: string
  rowIndex: string
  filterStatus: boolean
  fieldApiKey: string
  clickFooterItem: () => void
  editRules: VxeTablePropTypes.EditRules
  mergeCells: VxeTablePropTypes.MergeCells
  needCellColor: boolean
}

export interface TableColumn {
  show?: boolean
  width?: string | number
  minWidth?: number
  align?: VxeColumnProps['align']
  fixed?: VxeColumnProps['fixed']
  field?: VxeColumnProps['field']
  title?: VxeColumnProps['title']
  sortable?: VxeColumnProps['sortable']
  isDate?: boolean
  formatTime?: string
  isPrice?: boolean
  isWeight?: boolean
  isLength?: boolean
  isUnitPrice?: boolean
  isUnderweightRate?: boolean
  is_date?: boolean
  soltName?: string
  colGroupHeader?: boolean // 是否是表头分组
  headerSlot?: string | Component // 表头插槽 h渲染函数或者组件名称 或 VNode
  onInputSearch?: VxeInputEvents.SearchClick // 触发表头搜索按钮 注意：表头field必填，作为搜索的字段传给后端
  childrenList?: TableColumn[]
  headerInputValue?: string | number
  isEdit?: boolean // 是否开启编辑
  defaultData?: any // 默认数据 一般是后端传过来的数据 初次显示在表格上
  required?: boolean // 是否必填
  filterMethod?: Function | null // 表头筛选方法
  showOrder_status?: boolean // 是否显示订单状态
  cellRender?: any // 自定义渲染
}

export type TableColumnType = Partial<TableConfig>
