<script lang="ts" setup>
import { computed, reactive, watch } from 'vue'
import { searchFormatDate, searchOneFormatDate } from '@/common/format'

export interface Props {
  modelValue: string | string[]
  type: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  type: 'daterange',
})
const emits = defineEmits(['changeDate', 'update:modelValue'])

const state = reactive<any>({
  searchData: [],
  shortcuts: [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      },
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      },
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    },
  ],
})

watch(
  () => props.modelValue,
  (val) => {
    state.searchData = val || []
  },
  { immediate: true },
)

const type = computed(() => {
  return props.type
})

function handleSelectDate(val: any) {
  let res = null
  if (['datetimerange', 'daterange', 'monthrange'].includes(props.type))
    res = searchFormatDate(val)
  else
    res = searchOneFormatDate(val)

  emits('update:modelValue', val)
  emits('changeDate', res)
}
</script>

<template>
  <el-date-picker
    v-model="state.searchData"
    style="width: 100%; min-width: 100px"
    :type="type"
    unlink-panels
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    :shortcuts="type === 'date' ? '' : state.shortcuts"
    clearable
    @change="handleSelectDate"
  />
</template>
