<template>
  <el-date-picker
    v-model="state.searchData"
    :type="type"
    unlink-panels
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    :shortcuts="type === 'date' ? '' : state.shortcuts"
    @change="handleSelectDate"
  ></el-date-picker>
</template>
<script lang="ts">
import { searchFormatDate } from '@/common/format'
import { computed, reactive, toRefs, watch } from 'vue'

export default {
  // year/month/date/dates/datetime/ week/datetimerange/daterange/ monthrange
  name: 'SelectDate',
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: 'daterange',
    },
  },
  emits: ['changeDate', 'update:modelValue'],
  setup(props, ctx) {
    const state = reactive<any>({
      searchData: [],
      shortcuts: [
        {
          text: '最近一周',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            return [start, end]
          },
        },
        {
          text: '最近一个月',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            return [start, end]
          },
        },
        {
          text: '最近三个月',
          value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            return [start, end]
          },
        },
      ],
    })

    watch(
      () => props.modelValue,
      val => {
        state.searchData = val || []
      },
      { immediate: true }
    )

    const type = computed(() => {
      return props.type
    })

    const handleSelectDate = (val: any) => {
      console.log('res changeDateaaaaa', val)
      const res = searchFormatDate(val)
      console.log('res changeDate', res, val)
      ctx.emit('changeDate', res)
      ctx.emit('update:modelValue', val)
    }

    return {
      handleSelectDate,
      ...toRefs(state),
      state,
      type,
    }
  },
}
</script>
