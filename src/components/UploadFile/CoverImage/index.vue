<!--
  @LastEditTime: 2025-02-07 17:59:16
  @Description:  表格显示的首图：文件：点击跳转，图片点击放大
 -->
<script lang="ts" setup>
import { fileIsPdf, getFileType } from '@/common/uploadImage'
import { formatFileName, getFileName } from '@/common/util'

interface ParamType {
  fileUrl: string
}
const props = withDefaults(defineProps<ParamType>(), {
  fileUrl: '',
})
</script>

<template>
  <div class="">
    <div v-if="!props.fileUrl" />
    <el-link v-else-if="getFileType(fileUrl) !== 'image'" type="primary" :href="formatFileName(fileUrl)" :target="fileIsPdf(fileUrl) ? '_blank' : '_self'">
      {{ getFileName(fileUrl) }}
    </el-link>
    <el-image
      v-else
      style="width: 60px; height: 30px"
      preview-teleported
      :src="fileUrl"
      fit="scale-down"
      :preview-src-list="[fileUrl]"
    />
  </div>
</template>

<style lang="scss" scoped>

</style>
