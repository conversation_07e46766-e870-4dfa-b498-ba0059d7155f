import type { UploadWay } from '@/common/uploadImage'

export interface UploadFilePropsType {
  dragable: boolean
  multiple: boolean
  accept: string
  secene: string
  fileName: string // 文件名称
  type: string
  uploadText: string
  paste?: boolean // 复制黏贴上传功能
  showSubmitBtn: boolean
  fileList: string[]
  containerClassName?: string
  onlyOne?: boolean // 是否只允许上传单张
  defaultImage?: string // 默认图片
  autoUpload: boolean // 是否默认上传
  imageShown?: boolean // 是否显示图片
  status: UploadWay
  additionalText?: string // 额外提示文字
}
// import 'vue-cropper/dist/index.css'
export interface FileListCache {
  url: string
  file: File
}
