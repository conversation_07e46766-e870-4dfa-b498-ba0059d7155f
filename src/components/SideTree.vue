<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElTree } from 'element-plus'
import type { TreeKey } from 'element-plus/es/components/tree/src/tree.type'
import { onMounted, ref, watch } from 'vue'
import { debounce, getFilterData } from '@/common/util'
import selectApi from '@/api/treeApi'
import SelectComponents from '@/components/SelectComponents/index.vue'

interface DepartmentTreeProps {
  type: 'checkbox' | 'radio'
  modelValue: TreeKey | TreeKey[]
  api: keyof typeof selectApi
  showInput: boolean
  selectApi: any
  labelField: string
  valueField: string
  showSelectAll: boolean
  showAll: boolean
  unitId: number | string
  isName?: boolean
}
const props = withDefaults(defineProps<DepartmentTreeProps>(), {
  type: 'radio',
  modelValue: 0,
  showInput: true,
  selectApi: '',
  labelField: 'name',
  valueField: 'id',
  soltLeftLabelField: 'code',
  soltRightLabelField: 'name',
  showSelectAll: false,
  showAll: true,
  unitId: '',
  isName: false,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: TreeKey | TreeKey[]): void
  (e: 'change', value: TreeKey | TreeKey[]): void
  (e: 'select', value: any): void
}>()

const isChecked = ref(true)

const isIndeterminate = ref(true)

interface Tree {
  id: TreeKey
  name: string
  sub_department?: Tree[]
}
const defaultProps = {
  children: 'sub_type_fabric',
  label: 'name',
}

const treeRef = ref<InstanceType<typeof ElTree>>()

const { fetchData, data, success } = selectApi[props.api]()

function resetChecked() {
  treeRef.value!.setCheckedKeys([], false)
}

function handleCheckChange(data: Tree) {
  if (props.type === 'radio') {
    resetChecked()
    treeRef.value!.setCheckedKeys([data.id], false)
    emit('update:modelValue', data.id)
    emit('change', data.id)
    return
  }
  const checkedKeys = treeRef.value!.getCheckedKeys(false)
  emit('update:modelValue', checkedKeys)
  emit('change', checkedKeys)
}

function changeCheck(val: boolean) {
  if (val) {
    treeRef.value?.setCheckedNodes(data.value?.list)
    const checkedKeys = treeRef.value!.getCheckedKeys(false)
    isIndeterminate.value = true
    emit('update:modelValue', checkedKeys)
  }
  else {
    resetChecked()
    isIndeterminate.value = false
    emit('update:modelValue', [])
  }
}

const code = ref('')

const departmentList = ref<any>([])

async function getData() {
  const unit_type_id = props.api === 'BusinessUnitSupplierEnumlist' ? 12 : props.unitId

  const query: any = {
    code: code.value,
    name: code.value,
    unit_type_id,
  }

  if (props.isName)
    delete query.code

  await fetchData(getFilterData(query))
  if (!success.value)
    return ElMessage.error('获取失败')

  departmentList.value = data.value.list
  departmentList.value.unshift(
    {
      id: 0,
      name: '未指定',
    },
  )
}

watch(
  code,
  debounce(() => {
    emit('select', '')
    getData()
  }, 300),
)

onMounted(async () => {
  await getData()
  if (props.showSelectAll) {
    treeRef.value?.setCheckedNodes(data.value?.list)
    const checkedKeys = treeRef.value!.getCheckedKeys(false)
    isIndeterminate.value = true
    emit('update:modelValue', checkedKeys)
  }
})

defineExpose({
  resetChecked,
})
</script>

<template>
  <div class="list-page">
    <div v-if="props.showAll">
      <el-input v-if="props.showInput" v-model="code" :suffix-icon="Search" class="mb-3" placeholder="请输入搜索" clearable />
      <SelectComponents
        v-else
        v-model="code"
        show-slot
        :api="props.selectApi"
        :solt-right-label-field="props.soltRightLabelField"
        :solt-left-label-field="props.soltLeftLabelField"
        :label-field="props.labelField"
        :value-field="props.valueField"
        clearable
      />
    </div>
    <div v-if="showSelectAll" class="flex items-center">
      <el-checkbox v-model="isChecked" :indeterminate="isIndeterminate" @change="changeCheck">
        全部
      </el-checkbox>
    </div>
    <div class="table-card-full">
      <ElTree ref="treeRef" v-bind="$attrs" node-key="id" show-checkbox :data="departmentList" :props="defaultProps" @check="handleCheckChange">
        <template #empty>
          <slot name="empty" />
        </template>
      </ElTree>
    </div>
    <div class="justify-start">
      <slot name="footer" />
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep(.el-tree-node) {
  white-space: normal;
}

::v-deep(.el-tree-node__content) {
    height: 100%;
    align-items: start;
  }
</style>
