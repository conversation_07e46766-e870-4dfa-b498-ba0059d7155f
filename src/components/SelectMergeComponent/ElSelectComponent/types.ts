export const defaultElSelectComponentProps = {
  modelValue: undefined,
  multiple: false,
  virtual: false,
  filterable: true,
  clearable: true,
  labelField: 'name',
  valueField: 'id',
  disabledField: 'disabled',
  placeholder: '请选择',
  showSlot: false,
}

export interface ElSelectComponentProps {
  modelValue?: number | string | number[] | string[] | undefined
  filterable?: boolean
  clearable?: boolean
  virtual?: boolean
  size?: string
  // 可选项
  options: any[]
  exclude?: number[]
  // 多选
  multiple?: boolean
  // options 的 字段名
  labelField?: string
  valueField?: string
  placeholder?: string
  disabledField?: string
  filterMethod?: () => void
  customLabel?: <T>(row: T) => string // 自定义label
  soltLeftLabelField?: string
  soltRightLabelField?: string
  visibleChangeClose?: boolean
  showSlot?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
}
