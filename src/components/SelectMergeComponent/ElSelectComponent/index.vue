<script lang="ts" setup>
import { ref, toRefs, useAttrs, watch } from 'vue'
import type { ElSelectComponentProps } from './types'
import { defaultElSelectComponentProps } from './types'
import SelectVirtualOptions from '@/components/SelectVirtualOptions/index.vue'

defineOptions({
  name: 'ElSelectComponent',
  inheritAttrs: true,
})

const props = withDefaults(defineProps<ElSelectComponentProps>(), {
  options: () => [],
  ...defaultElSelectComponentProps,
  showSlot: false,
})

const emit = defineEmits<{
  (e: 'update:modelValue', val: ElSelectComponentProps['modelValue']): void
  (e: 'change', val: ElSelectComponentProps['modelValue'], label: string): void
  (e: 'visibleChange', val: boolean): void
  (e: 'removeTag', val: ElSelectComponentProps['modelValue']): void
  (e: 'clear'): void
  (e: 'blur', val: FocusEvent): void
  (e: 'focus', val: FocusEvent): void
  (e: 'keydown', event: KeyboardEvent): void
  (e: 'clickOption', val: any): void
}>()

const attr = useAttrs()

const { modelValue, options } = toRefs(props)

const value = ref<ElSelectComponentProps['modelValue']>(modelValue.value)

watch(
  () => props.modelValue,
  (newValue) => {
    value.value = newValue
  },
  {
    immediate: true,
  },
)

const ElSelectRef = ref(null)

function handleChange(val: any) {
  emit('update:modelValue', val)
  let label
  if (props.multiple)
    label = options.value.filter(item => val.includes(item[props.valueField]))

  else
    label = options.value.find(item => item[props.valueField] === val)?.[props.labelField]

  emit('update:modelValue', val)
  emit('change', val, label)
}

function handleVisibleChange(isVisible: boolean) {
  emit('visibleChange', isVisible)
}

function handleRemoveTag(val: any) {
  emit('removeTag', val)
}
function handleClear() {
  emit('clear')
}
function handleBlur(e: FocusEvent) {
  emit('blur', e)
}
function handleFocus(e: FocusEvent) {
  emit('focus', e)
}

function handleKeyDown(e: KeyboardEvent) {
  emit('keydown', e)
}

function handleClickOption(item: any) {
  emit('clickOption', item)
}

defineExpose({
  ElSelectRef,
})
</script>

<template>
  <template v-if="!virtual">
    <el-select
      ref="ElSelectRef"
      :clearable="props.clearable"
      :filterable="filterable"
      :model-value="value"
      :multiple="multiple"
      :placeholder="placeholder"
      :size="props.size"
      :remote="remote"
      :remote-method="remoteMethod"
      popper-class="vxe-table--ignore-clear"
      style="width: 100%"
      v-bind="attr"
      @blur="handleBlur"
      @change="handleChange"
      @clear="handleClear"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @keydown="handleKeyDown"
    >
      <slot name="select" />
      <template #prefix>
        <slot name="prefix" />
      </template>
      <template #empty>
        <slot name="empty" />
      </template>
      <template #loading>
        <svg class="circular" viewBox="0 0 50 50">
          <circle class="path" cx="25" cy="25" r="20" fill="none" />
        </svg>
      </template>
      <template #label="{ label, value }">
        <!-- 处理默认值不在list中时，则显示传入的labelValue -->
        <span v-if="label !== value">{{ label }}</span>
        <span v-else>{{ attr['default-value'] || attr['default-label']?.label || label || '' }}</span>
      </template>
      <el-option
        v-for="(item, index) in options"
        :key="index"
        :disabled="item[disabledField]"
        :label="typeof customLabel === 'function' ? customLabel(item) : item[labelField]"
        :value="item[valueField]"
        class="vxe-table--ignore-clear"
        @click="handleClickOption(item)"
      >
        <slot :row="item" name="option" />
      </el-option>
    </el-select>
  </template>

  <template v-else>
    <SelectVirtualOptions
      :clearable="props.clearable"
      :label-field="props.labelField"
      :model-value="props.modelValue"
      :multiple="props.multiple"
      :options="options"
      :placeholder="props.placeholder"
      :size="props.size"
      :show-slot="props.showSlot"
      :solt-left-label-field="props.soltLeftLabelField"
      :solt-right-label-field="props.soltRightLabelField"
      :value-field="props.valueField"
      :visible-change-close="props.visibleChangeClose"
      v-bind="attr"
      @change="handleChange"
    />
  </template>
</template>

<style scoped>
.circular {
  display: inline;
  height: 30px;
  width: 30px;
  animation: loading-rotate 2s linear infinite;
}
.path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-color-primary);
  stroke-linecap: round;
}
@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}
@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style>
