<!--
  @Description: 成品编号名称选择组件 - 复制ly
 -->
<script lang="ts" setup>
import type { Ref } from 'vue'
import { inject, ref, toRefs, useAttrs, watch } from 'vue'
import { every, isEmpty, isEqual } from 'lodash-es'
import ElSelectComponent from './ElSelectComponent/index.vue'
import type { ElSelectComponentProps } from './ElSelectComponent/types'
import { defaultElSelectComponentProps } from './ElSelectComponent/types'
import selectApi from '@/api/selectInit'
import { getFilterData } from '@/common/util'

defineOptions({
  name: 'SelectComponent',
  inheritAttrs: true,
})

const props = withDefaults(defineProps<Props>(), {
  ...defaultElSelectComponentProps,
  injectName: '',
  query: () => ({}),
  queryConfig: () => ({}),
})

const emit = defineEmits(['update:modelValue', 'change', 'clickOption', 'clear'])

const attr = useAttrs()

interface Props extends Omit<ElSelectComponentProps, 'options'> {
  apiName: keyof typeof selectApi
  injectName: string
  query: Record<string, any>
  remoteKey?: string
  queryConfig?: Record<string, any>
  defaultLabel?: {
    value: string | number
    label: string
    [key: string]: any
  }
}

const { apiName, multiple, placeholder, clearable, modelValue, query = {} } = toRefs(props)

const value = ref<Props['modelValue']>(modelValue.value)

watch(
  () => props.modelValue,
  (newValue) => {
    value.value = newValue
  },
  {
    immediate: true,
  },
)

if (!selectApi[apiName.value]) {
  // apiName 用于 defineExpose 暴露处理出来的 fetchData
  throw new TypeError('apiName 不可为空')
}

const { fetchData, data, loading } = selectApi[apiName.value](props.queryConfig)

const options = ref<any[]>([])
// 注入的异步数据
let fetchResult: any = []

const injectAsyncData = inject<Ref<Record<string, any>>>(props.injectName)

async function getData() {
  const tempQuery = getFilterData({
    size: 50,
    ...query.value,
  })
  if ((!injectAsyncData && !props.injectName) || !isEmpty(tempQuery)) {
    await fetchData(getFilterData(tempQuery))
    fetchResult = JSON.parse(JSON.stringify(data.value))
  }
  else {
    fetchResult = JSON.parse(JSON.stringify(injectAsyncData?.value) || '{}')
  }

  if (props.exclude != null && Array.isArray(props.exclude))
    options.value = fetchResult.list?.filter((v: any) => !props.exclude!.includes(v.id)) || []
  else
    options.value = fetchResult.list || []
  // 添加默认选项逻辑
  if (!every(props.defaultLabel, isEmpty)) {
    // 检查是否已经存在相同的值
    const existingOption = options.value.find(
      option => option[props.valueField] === props.defaultLabel?.value,
    )

    // 只有当不存在相同值的选项时才添加默认选项
    if (!existingOption) {
      options.value.unshift({
        [props.valueField]: props.defaultLabel.value,
        [props.labelField]: props.defaultLabel.label,
        ...props.defaultLabel,
      })
    }
  }
}

function handleChange(val: any, label: string) {
  emit('update:modelValue', val)
  const item = options.value.find((v: any) => v[props.valueField] === val)
  emit('change', item, label)
}

function handleClickOption(val: any) {
  emit('clickOption', val)
}

watch(
  () => props.query,
  (newQuery, oldQuery) => {
    // 比较参数是否一致，去除remoteKey - 因为remoteKey是用于远程搜索的,在此方法中请求了handleRemoteMethod
    const aa = getFilterData({ ...newQuery })
    const bb = getFilterData({ ...oldQuery })
    if (aa[props?.remoteKey])
      delete aa[props?.remoteKey]
    if (bb[props?.remoteKey])
      delete bb[props?.remoteKey]
    if (isEqual(aa, bb))
      return

    getData()
  },
  {
    deep: true,
  },
)

function handleRemoteMethod(input: string) {
  if (!props.remoteKey)
    return
  if (!query.value) {
    query.value = {
      [props.remoteKey]: '',
    }
  }

  query.value[props.remoteKey] = input
  getData()
}
function handleFocus() {
  // getData()
}
function handleClear() {
  emit('clear')
}
defineExpose({
  fetchData,
  getData,
})
</script>

<template>
  <ElSelectComponent
    :clearable="clearable"
    :custom-label="customLabel"
    :filter-method="filterMethod"
    :filterable="filterable"
    :label-field="props.labelField"
    :model-value="value"
    :multiple="multiple"
    :options="options"
    :placeholder="placeholder"
    :value-field="props.valueField"
    :virtual="virtual"
    :remote="remote"
    :default-label="props.defaultLabel"
    :remote-method="handleRemoteMethod"
    :loading="loading"
    v-bind="attr"
    :show-slot="props.showSlot"
    :solt-left-label-field="props.soltLeftLabelField"
    :solt-right-label-field="props.soltRightLabelField"
    @change="handleChange"
    @click-option="handleClickOption"
    @focus="handleFocus"
    @clear="handleClear"
  >
    <template #select>
      <slot name="select" />
    </template>
    <template #prefix>
      <slot name="prefix" />
    </template>
    <template #empty>
      <slot name="empty" />
    </template>
    <template #option="{ row }">
      <slot
        :row="row"
        name="option"
      />
    </template>
  </ElSelectComponent>
</template>

<style scoped></style>
