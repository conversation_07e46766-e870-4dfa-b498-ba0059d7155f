# ElSelectComponent 组件文档

## 组件介绍
ElSelectComponent 是一个基于 Element Plus 的选择框组件的封装，提供了更多的功能特性和便捷的使用方式。支持虚拟滚动、自定义选项渲染、远程搜索等功能。

## 功能特性
- 支持单选和多选模式
- 支持虚拟滚动，适用于大数据量场景
- 支持自定义选项渲染
- 支持远程搜索
- 支持禁用选项
- 支持清空选择
- 支持自定义标签显示
- 支持默认值显示

## Props 说明
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 绑定值 | number/string/array | undefined |
| options | 选项数据 | array | [] |
| multiple | 是否多选 | boolean | false |
| virtual | 是否启用虚拟滚动 | boolean | false |
| filterable | 是否可搜索 | boolean | true |
| clearable | 是否可清空 | boolean | true |
| labelField | 选项标签字段名 | string | 'name' |
| valueField | 选项值字段名 | string | 'id' |
| placeholder | 占位文本 | string | '请选择' |
| customLabel | 自定义标签渲染函数 | function | - |
| default-value | 选项不在列表中时的默认显示值 | string | '' |
| remote | 是否开启远程搜索 | boolean | false |
| remoteMethod | 远程搜索方法 | function | - |
| size | 组件尺寸 | 'large'/'default'/'small' | 'default' |
| disabledField | 禁用选项的字段名 | string | 'disabled' |
| showSlot | 是否显示自定义插槽 | boolean | false |
| soltLeftLabelField | 左侧插槽标签字段名 | string | - |
| soltRightLabelField | 右侧插槽标签字段名 | string | - |
| visibleChangeClose | 是否在可见性改变时关闭 | boolean | false |

## 事件
| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选中值发生变化时触发 | (value, label) |
| clear | 点击清空按钮时触发 | - |
| blur | 失去焦点时触发 | (event: FocusEvent) |
| focus | 获得焦点时触发 | (event: FocusEvent) |
| visible-change | 下拉框出现/隐藏时触发 | (visible: boolean) |
| remove-tag | 多选模式下移除标签时触发 | (value) |
| keydown | 键盘按下时触发 | (event: KeyboardEvent) |
| click-option | 点击选项时触发 | (option) |

## 插槽
| 插槽名 | 说明 |
|--------|------|
| select | 选择框自定义内容 |
| prefix | 选择框前缀内容 |
| empty | 无数据时的自定义内容 |
| option | 选项自定义内容 |

## 使用方法
