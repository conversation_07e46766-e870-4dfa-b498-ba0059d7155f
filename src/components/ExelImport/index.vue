<template>
  <el-upload v-loading="state.loading" accept=".xls,.xlsx" :before-upload="beforeUpload" :http-request="uploadChange" :limit="1" :show-file-list="false">
    <el-button class="!ml-0" type="primary">{{ props?.name }}</el-button>
  </el-upload>
</template>

<script setup>
import { reactive, ref, toRefs } from 'vue'
import { upload } from '@/common/uploadExel.js'
import { ElMessageBox, ElMessage } from 'element-plus'
const props = defineProps({
  // 传入接口
  url: {
    type: String,
    default: '/v1/admin/unit/import',
  },
  name: {
    type: String,
    default: '导入子单元Excel',
  },
})
const emits = defineEmits(['handImport'])
const state = reactive({
  imgs: false,
  fileName: '',
  loading: false,
})
// 上传文件之前先判断该文件是否是Excel文件
const beforeUpload = file => {
  // 获取上传文件
  const formData = new FormData() // FormData对象，添加参数只能通过append('key', value)的形式添加
  formData.append('file', file) // 添加文件对象
  state.formData = formData
  const Xls = file.name.split('.')
  if (Xls[1] === 'xls' || Xls[1] === 'xlsx' || Xls[1] === 'csv') {
    state.imgs = true // 打开图片
    return file
  } else {
    ElMessage.error('请上传excel格式的文件!')
    return false
  }
}

const uploadChange = async ({ file }) => {
  state.fileName = file.name
  state.loading = true
  upload(file, props?.url)
    .then(res => {
      emits('handImport')
    })
    .finally(() => {
      state.loading = false
    })
}
</script>
