<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import FildCard from '@/components/FildCard.vue'
import { OrderAuditStatusEnum } from '@/enum/orderEnum'

export interface Props {
  order_no: string
  status_name: string
  cloth_order_no: string
  src_order_no: string
  out_warehouse_order_no: string
  business_status_name: string
  business_status?: 1 | 2 | 3 | 4 | 5 | 6 | 7
  business_close_name?: string
  business_close?: 1 | 2
  status: 1 | 2 | 3 | 4
  closeLoading?: boolean
  cancelLoading?: boolean
  rejectLoading?: boolean
  auditLoading?: boolean
  eliminateLoading?: boolean
  deliverLoading?: boolean
  printData: any
  permission_close_key?: string // 业务关闭按钮
  permission_cancel_key: string // 作废权限值
  permission_edit_key: string // 重新编辑权限值
  permission_reject_key: string // 驳回权限值
  permission_pass_key: string // 审核权限值
  permission_wait_key: string // 消审权限值
  permission_print_key?: string // 打印权限
  changeBtn?: boolean
  permission_change_key?: string // 变更权限指
  permission_deliver_key?: string // 确认出仓权限值
  showChangeBtn?: boolean // 展示变更按钮
  collect_status_name?: string
  edit_router_name?: string // 重新编辑路由名称
  order_id?: number // 单据id
  edit_query?: any // 重新编辑路由参数
  edit_router_type?: 'params' | 'query' // 重新编辑路由类型
}
const props = withDefaults(defineProps<Props>(), {
  closeLoading: false,
  cancelLoading: false,
  rejectLoading: false,
  auditLoading: false,
  eliminateLoading: false,
  deliverLoading: false,
  order_no: '--',
  status_name: '--',
  cloth_order_no: '',
  src_order_no: '',
  out_warehouse_order_no: '',
  business_status_name: '',
  business_status: 1,
  business_close_name: '',
  business_close: 1,
  status: 1,
  printData: {},
  permission_cancel_key: '',
  permission_reject_key: '',
  permission_pass_key: '',
  permission_wait_key: '',
  permission_change_key: '',
  changeBtn: false,
  collect_status_name: '',
  permission_deliver_key: '',
  order_id: 0,
  edit_router_name: '',
  permission_edit_key: '',
  edit_query: {},
  edit_router_type: 'query',
})

const emit = defineEmits([
  'print',
  'printView',
  'cancel',
  'close',
  'reject',
  'audit',
  'eliminate',
  'deliverFromGodown',
  'change',
  'onModification',
  'edit',
])

const router = useRouter()

// const hiprintTemplate = ref()
// onMounted(() => {
//   getData()
// })

// const { fetchData, data: templateData } = GetPrintTemplate()
// const getData = async () => {
//   await fetchData({ id: 1619517091287296 })
//   const template = templateData.value?.template ? JSON.parse(templateData.value.template) : {}
//   hiprintTemplate.value = new hiprint.PrintTemplate({ template })
// }

// const preViewRef = ref()
// const onPrintView = () => {
//   preViewRef.value.show(hiprintTemplate.value, props.printData)
//   emit('printView')
// }
// const onPrint = () => {
//   hiprintTemplate.value.print(props.printData)
//   emit('print')
// }
function onClose() {
  emit('close', 5)
}
function onCancel() {
  emit('cancel', 4)
}
function onReject() {
  emit('reject', 3)
}
function onAudit() {
  emit('audit', 2)
}

function onEliminate() {
  emit('eliminate', 1)
}
// 确认出仓
function onDeliverFromGodown() {
  emit('deliverFromGodown')
}
function onChange() {
  emit('change')
}
function onModification() {
  emit('onModification')
}
function onEdit() {
  if (!props.order_id || !props.edit_router_name)
    return ElMessage.warning('单据配置不完整')

  router.push({
    name: props.edit_router_name,
    [props.edit_router_type]: {
      ...props.edit_query,
      id: props.order_id,
    },
  })
  emit('edit')
}
</script>

<template>
  <FildCard :tool-bar="false" class="mb-[5px]">
    <div class="flex items-center justify-between">
      <div class="flex">
        <div v-if="props.order_no" class="flex items-center w-[332px]">
          <svg-icon name="dh" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">单号</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.order_no
            }}</span>
          </div>
        </div>
        <slot name="custom" />

        <div v-if="props.cloth_order_no" class="flex items-center w-[332px]">
          <svg-icon name="dh" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">配布单号</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.cloth_order_no
            }}</span>
          </div>
        </div>
        <div
          v-if="props.out_warehouse_order_no"
          class="flex items-center w-[332px]"
        >
          <svg-icon name="dh" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">调拨出仓单号</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.out_warehouse_order_no
            }}</span>
          </div>
        </div>
        <div v-if="props.src_order_no" class="flex items-center w-[332px]">
          <svg-icon name="dh" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">来源单据</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.src_order_no
            }}</span>
          </div>
        </div>
        <div v-if="props.status_name" class="flex items-center w-[332px]">
          <svg-icon name="zt" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">状态</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.status_name
            }}</span>
          </div>
        </div>
        <div
          v-if="props.business_status_name"
          class="flex items-center w-[332px]"
        >
          <svg-icon name="zt" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">业务状态</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.business_status_name
            }}</span>
          </div>
        </div>
        <div
          v-if="props.business_close_name"
          class="flex items-center w-[332px]"
        >
          <svg-icon name="zt" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">关闭状态</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.business_close_name
            }}</span>
          </div>
        </div>
        <div
          v-if="props.collect_status_name"
          class="flex items-center w-[332px]"
        >
          <svg-icon name="zt" size="55px" />
          <div class="flex flex-col ml-[20px]">
            <span class="text-black-60 text-[18px]">收款状态</span>
            <span class="text-color-theme-a text-[20px] font-medium">{{
              props.collect_status_name
            }}</span>
          </div>
        </div>
      </div>
      <div class="flex">
        <!-- <el-button :icon="Printer" @click="onPrint">打印</el-button>
        <el-button @click="onPrintView">预览</el-button> -->
        <slot name="print" />
        <el-button
          v-if="props.business_close !== 2 && props.status === 2"
          v-has="props.permission_close_key"
          :loading="props.closeLoading"
          @click="onClose"
        >
          整单关闭
        </el-button>
        <el-button
          v-if="props.status === 1 || props.status === 3"
          v-has="props.permission_cancel_key"
          :loading="props.cancelLoading"
          @click="onCancel"
        >
          作废
        </el-button>
        <el-button
          v-if="(props.status === OrderAuditStatusEnum.Pending || props.status === OrderAuditStatusEnum.TurnDown) && props.edit_router_name && props.order_id"
          v-has="props.permission_edit_key"
          @click="onEdit"
        >
          重新编辑
        </el-button>
        <el-button
          v-if="props.status === 1"
          v-has="props.permission_reject_key"
          type="danger"
          :loading="props.rejectLoading"
          @click="onReject"
        >
          驳回
        </el-button>
        <el-button
          v-if="props.status === 1"
          v-has="props.permission_pass_key"
          type="primary"
          :loading="props.auditLoading"
          @click="onAudit"
        >
          审核
        </el-button>
        <el-button
          v-if="props.status === 2"
          v-has="props.permission_wait_key"
          :loading="props.eliminateLoading"
          @click="onEliminate"
        >
          消审
        </el-button>
        <el-button
          v-if="props.status === 2 && props.business_status === 3"
          v-has="props.permission_deliver_key"
          type="primary"
          :loading="props.deliverLoading"
          @click="onDeliverFromGodown"
        >
          确认出仓
        </el-button>
        <el-button
          v-if="props.changeBtn && props.status === 2"
          type="primary"
          @click="onChange"
        >
          变更
        </el-button>
        <el-button
          v-if="props.status === 2 && props.showChangeBtn"
          v-has="props.permission_change_key"
          type="primary"
          @click="onModification"
        >
          变更
        </el-button>
      </div>
    </div>
  </FildCard>
  <!-- 预览 -->
  <!-- <print-preview ref="preViewRef" /> -->
</template>
