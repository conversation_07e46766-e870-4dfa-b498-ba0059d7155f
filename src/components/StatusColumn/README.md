# StatusColumn 组件说明

## 概述
StatusColumn 是一个用于显示和管理订单状态的通用组件。它提供了状态显示、操作按钮（如审核、取消、关闭等）以及打印功能。

## 基本用法
```vue
<script lang="ts" setup>
import StatusColumn from '@/components/StatusColumn/index.vue'
</script>

<template>
  <StatusColumn
    :order-no="orderNo"
    :order-id="orderId"
    :status-name="statusName"
    :status="status"
    :print-data="printData"
    @close="handleClose"
    @cancel="handleCancel"
    @reject="handleReject"
    @audit="handleAudit"
    @eliminate="handleEliminate"
    @print="handlePrint"
  />
</template>
```

## 参数说明

| 参数                   | 类型    | 说明             | 默认值 |
| ---------------------- | ------- | ---------------- | ------ |
| order_no               | string  | 订单编号         | '--'   |
| order_id               | number  | 订单ID           | 0      |
| status_name            | string  | 状态名称         | '--'   |
| status                 | number  | 状态值           | 1      |
| cloth_order_no         | string  | 布匹订单号       | ''     |
| src_order_no           | string  | 源订单号         | ''     |
| out_warehouse_order_no | string  | 出库单号         | ''     |
| business_status_name   | string  | 业务状态名称     | ''     |
| business_status        | number  | 业务状态值       | 1      |
| business_close_name    | string  | 业务关闭名称     | ''     |
| business_close         | number  | 业务关闭状态     | 1      |
| printData              | any     | 打印数据         | {}     |
| permission_cancel_key  | string  | 作废权限值       | ''     |
| permission_reject_key  | string  | 驳回权限值       | ''     |
| permission_pass_key    | string  | 审核权限值       | ''     |
| permission_wait_key    | string  | 消审权限值       | ''     |
| permission_print_key   | string  | 打印权限值       | ''     |
| permission_edit_key    | string  | 编辑权限值       | ''     |
| permission_close_key   | string  | 关闭权限值       | ''     |
| permission_change_key  | string  | 变更权限值       | ''     |
| permission_deliver_key | string  | 出仓权限值       | ''     |
| edit_router_name       | string  | 编辑路由名称     | ''     |
| edit_router_type       | string  | 编辑路由类型：'query'\|'params'     | 'query'|
| close-loading          | boolean | 关闭加载状态     | false  |
| cancel-loading         | boolean | 取消加载状态     | false  |
| reject-loading         | boolean | 驳回加载状态     | false  |
| audit-loading          | boolean | 审核加载状态     | false  |
| eliminate-loading      | boolean | 消审加载状态     | false  |
| deliver-loading        | boolean | 出仓加载状态     | false  |
| changeBtn              | boolean | 是否显示变更按钮 | false  |
| showChangeBtn          | boolean | 是否展示变更按钮 | false  |
| collect_status_name    | string  | 采集状态名称     | ''     |
| edit_query             | any     | 编辑路由参数     | {}     |

## 事件说明

| 事件名            | 说明         |
| ----------------- | ------------ |
| close             | 关闭事件     |
| cancel            | 取消事件     |
| reject            | 驳回事件     |
| audit             | 审核事件     |
| eliminate         | 消审事件     |
| print             | 打印事件     |
| printView         | 打印预览事件 |
| deliverFromGodown | 确认出仓事件 |
| change            | 变更事件     |
| onModification    | 修改变更事件 |
| edit              | 编辑事件     |
