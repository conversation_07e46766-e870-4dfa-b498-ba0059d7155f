<template>
  <el-tag :type="statusObj[props.status]">{{ status[props.status] }}</el-tag>
</template>

<script lang="ts" setup>
export interface Props {
  status: 1 | 2 | 3 | 4
  name: string
}
const props = withDefaults(defineProps<Props>(), {
  status: 1,
  name: '',
})

const statusObj: any = {
  1: '',
  2: 'success',
  3: 'danger',
  4: 'warning',
}

const status = {
  1: '待审核',
  2: '已审核',
  3: '驳回',
  4: '作废',
}
</script>

<style></style>
