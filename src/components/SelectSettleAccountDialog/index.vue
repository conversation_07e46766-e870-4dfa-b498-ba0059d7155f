<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'
import AddDialog from '@/pages/basicData/components/AddSettlementDialog.vue'
import { addTypeIntercourseUnits } from '@/api/settlementType'

defineOptions({
  name: 'SelectSettleAccountDialog',
})
const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'code',
  disabled: false,
  showAddLink: true,
  api: 'GetTypeSettleAccountsEnumList',
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  // (e: 'update:grey_fabric_id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const AddDialogRef = ref()

const modelValue = defineModel<number>('modelValue', { required: true })
interface Props {
  query?: any
  defaultValue?: {
    id: number
    name?: string
    code?: string
  }
  field?: 'code' | 'name'
  api?: keyof typeof selectApi | ''
  showAddLink?: boolean
  disabled?: boolean
  isPushDefaultData?: boolean
}
const fieldName = {
  name: '名称',
  code: '编号',
}

const productRef = ref()

const componentRemoteSearch = reactive({
  name: '',
  code: '',
})
function handleInputCustomerName(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  emits('changeValue', val)
  modelValue.value = val.id
  // emits('update:grey_fabric_id', val.id)
}
const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})

defineExpose({
  productRef,
})
function handleSelectCustomer() {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '新增收款账户'
  AddDialogRef.value.state.form.code = ''
  AddDialogRef.value.state.form.name = ''
  AddDialogRef.value.state.form.remark = ''
  AddDialogRef.value.state.form.settle_accounts = ''
  AddDialogRef.value.state.form.account_number = ''
  AddDialogRef.value.state.form.use_by_dept = ''
  AddDialogRef.value.state.form.id = -1
}
const { fetchData: AddFetch, msg: AddMsg, success: AddSuccess } = addTypeIntercourseUnits()

// 新建、编辑往来单位类型
async function handleSure(form: any) {
  const query = {
    id: form.id,
    code: form.code,
    remark: form.remark,
    name: form.name,
    account_number: form.account_number,
    use_by_dept: form.use_by_dept,
    settle_accounts: form.settle_accounts,
  }
  await AddFetch(query)
  if (AddSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    productRef.value.getTableData()
  }
  else {
    ElMessage.error(AddMsg.value)
  }
}
</script>

<template>
  <SelectDialog
    ref="productRef"
    v-model="modelValue"
    :label-field="field"
    :api="api"
    :disabled="disabled"
    :table-config="tableConfig"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :is-push-default-data="isPushDefaultData"
    :column-list="[
      {
        field: 'code',
        colGroupHeader: true,
        title: '编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'code',
            title: '编号',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'name',
        colGroupHeader: true,
        title: '名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'name',
            title: '名称',
            minWidth: 100,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: defaultValue ? {
          id: defaultValue?.id,
          name: defaultValue?.name,
          code: defaultValue?.code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInputCustomerName"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
  <AddDialog ref="AddDialogRef" need_rec_exp_type @handle-sure="handleSure" />
</template>

<style scoped></style>
