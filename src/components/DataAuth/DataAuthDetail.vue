<script lang="ts" setup>
import { computed } from 'vue'

const AuthList = defineModel([])
// 根据AuthList里每一项的selected属性来判断是否选中
const selectedList = computed(() => AuthList.value.filter((auth: any) => auth.selected).map((auth: any) => auth.key))
</script>

<template>
  <div>
    <el-checkbox-group
      v-model="selectedList"
      class="flex flex-col"
    >
      <el-checkbox v-for=" auth in AuthList" :key="auth.key" :label="auth.authName" :value="auth.key" class="h-[26px]" disabled>
        {{ auth.authName }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
