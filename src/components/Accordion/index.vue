<script setup lang="ts">
import { ref, watch } from 'vue'

interface ParamType {
  openStatus: boolean
}

const props = withDefaults(defineProps<ParamType>(), {
  openStatus: false,
})

const searchShow = ref(true)
watch(
  () => props.openStatus,
  () => {
    searchShow.value = localStorage.getItem('salesPriceListCard') ? localStorage.getItem('salesPriceListCard') === '1' : props.openStatus
  },
  { immediate: true },
)

function openBtn() {
  searchShow.value = !searchShow.value
  localStorage.setItem('salesPriceListCard', searchShow.value ? '1' : '2')
}
</script>

<template>
  <div :class="`search_car ${!searchShow && 'search_close'}`">
    <div :class="`search_car_con ${!searchShow && 'search_car_con_close'}`">
      <slot />
    </div>
    <div class="open_btn" @click="openBtn">
      <el-icon v-if="!searchShow">
        <ArrowRight />
      </el-icon>
      <el-icon v-else>
        <ArrowLeft />
      </el-icon>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search_car {
  position: relative;
  height: auto;
  background: #fff;
  box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);
  border-radius: 5px;
  padding: 10px 23px 10px 10px;
  margin: 5px 0 0 0;
  max-width: 360px;
  overflow: hidden;
  transition: all 0.2s ease-in-out;

  .open_btn {
    position: absolute;
    right: 0;
    margin: auto;
    top: 0;
    bottom: 0;
    height: 50px;
    width: 20px;
    background-color: #ccc;
    border-radius: 10px 0 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
  }

  .search_car_con_close {
    opacity: 0 !important;
  }

  .search_car_con {
    transition: all 0.3s ease-in-out;
    height: 100%;
    opacity: 1;
    display: flex;
    flex-flow: column nowrap;
    overflow-y: scroll
  }
}

.search_close {
  max-width: 0px !important;
  // width: 0px !important;
  overflow: hidden;
}
</style>
