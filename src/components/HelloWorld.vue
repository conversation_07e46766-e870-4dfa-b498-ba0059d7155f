<script setup lang="ts">
import { useCounterStore } from '@/stores/counter'

defineProps({
  msg: String,
})
const counter = useCounterStore()

const add = () => {
  counter.increment()
}
</script>

<template>
  <h1>{{ msg }}</h1>

  <div class="card">
    <el-button type="primary" @click="add">pinia加</el-button>
  </div>
</template>

<style lang="scss" scoped>
.read-the-docs {
  color: #888;
}
</style>
