<template>
  <div v-if="labelMode">{{ label }}</div>
  <el-cascader v-if="!labelMode" v-model="value" :multiple="multiple" clearable :placeholder="placeholder" filterable :props="selectProps" @change="handleChange"></el-cascader>
</template>

<script lang="ts">
import { selectAddressReposistories } from '@/api/warehouseInformation'
import { reactive, toRefs, computed, onMounted } from 'vue'
import { useSelectParams } from '@/use/useSelectParams'
/**
 * 选择 地址
 */
export default {
  name: 'SelectAddress',
  props: {
    modelValue: Number,
    // 多选
    multiple: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '选择地址',
    },
    // 组件显示模式 true 为 仅显示不能选择， false 为选择组件
    labelMode: {
      type: Boolean,
      default: false,
    },
  },
  emit: ['update:modelValue', 'handleChange'],
  setup(props, ctx) {
    const { fetchData, data: dataList, success } = selectAddressReposistories()
    const { data, label, code, bindData } = useSelectParams(dataList)

    const state = reactive({
      selectProps: {
        lazy: true,
        async lazyLoad(node: any, resolve: any) {
          const { level } = node
          await fetchData({
            id: node.value == undefined ? 1 : node.value,
          })
          resolve(
            dataList.value?.map((item: any) => {
              return {
                value: item.id,
                label: item.name,
                leaf: level >= 2,
              }
            })
          )
        },
      },
    })
    const value = computed(() => {
      return props.modelValue
    })

    onMounted(() => {})

    // watchEffect(() => {
    //   fetchData({parent_id: 1}).then(() => {
    //     bindData(value.value);
    //   });
    // });

    const handleChange = (val: any) => {
      ctx.emit('update:modelValue', val)
      ctx.emit('handleChange', val)
    }

    return {
      handleChange,
      label,
      value,
      success,
      data,
      code,
      ...toRefs(state),
    }
  },
}
</script>

<style></style>
