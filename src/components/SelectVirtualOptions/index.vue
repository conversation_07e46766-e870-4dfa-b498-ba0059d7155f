<script setup lang="ts">
import { ref } from 'vue'
import { ElSelectV2 } from 'element-plus'
import BottomBar from '../SelectComponents/SelectBottomBar.vue'

export interface Props {
  options: []
  placeholder: string
  modelValue: number | string
  multiple: boolean
  size: string
  disabled: boolean
  labelField: string
  valueField: string
  labelValue: string
  visibleChangeClose: boolean
  clearable: boolean
  showSlot: boolean
  quickAddLink?: string
  quickAddPremission?: string
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  placeholder: '请选择内容',
  modelValue: '',
  multiple: false,
  size: '',
  disabled: false,
  labelField: 'label',
  valueField: 'value',
  labelValue: '',
  soltLeftLabelField: 'code',
  soltRightLabelField: 'name',
  visibleChangeClose: false,
  clearable: true,
  showSlot: false,
  quickAddLink: '',
  quickAddPremission: '',
})

const emit = defineEmits(['update:modelValue', 'change', 'refresh'])

function handChange(val: any) {
  emit('update:modelValue', val)
  emit('change', val)
}

const selectRef = ref<InstanceType<typeof ElSelectV2> | null>(null)

function visibleChange(val: boolean) {
  if (val && props.visibleChangeClose)
    selectRef.value.blur()
}
defineExpose({
  selectRef,
})
</script>

<template>
  <ElSelectV2
    :props="{
      label: labelField,
      value: valueField,
    }"
    :disabled="props.disabled"
    :clearable="props.clearable"
    :model-value="props.modelValue"
    filterable
    :multiple="props.multiple"
    :options="options"
    class="my-select"
    :class="`el-select-vxe-${props.size}`"
    popper-class="custom-select"
    :placeholder="props.placeholder"
    :size="props.size"
    @change="handChange"
    @visible-change="visibleChange"
  >
    <template v-if="props.showSlot" #default="{ item }">
      <span style="float: left">{{ item[props.soltLeftLabelField] }}</span>
      <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item[props.soltRightLabelField] }}</span>
    </template>
    <template #label="{ label, value }">
      <!-- 处理默认值不在list中时，则显示传入的labelValue -->
      <span v-if="label !== value">{{ label }}</span>
      <span v-else>{{ labelValue || label || '' }}</span>
    </template>
    <template #footer>
      <BottomBar :quick-add-link="quickAddLink" :quick-add-premission="quickAddPremission" :="$attrs" @refresh="() => emit('refresh')" />
    </template>
  </ElSelectV2>
</template>

<style scoped lang="scss">
.el-select__tags {
  max-width: calc(100% - 40px) !important;
}

/* 若是传入的size为small 则跟vxe的一样高度 */
.el-select-vxe-small .el-select__wrapper{
  height: var(--vxe-ui-input-height-mini);
}
.custom-select {
  .el-select-dropdown__footer:empty {
    display: none; // 隐藏空的footer;
  }
}
</style>
