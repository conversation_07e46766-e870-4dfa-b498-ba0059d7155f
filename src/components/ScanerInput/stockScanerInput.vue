<template>
  <vxe-input clearable placeholder="请扫描" disabled :modelValue="props.modelValue" @input="changeValue" style="width: 50% !important; margin-right: 5%"></vxe-input>
</template>
<script setup lang="ts">
import { useGlobalData } from '@/stores/globalData'
import { getDetailByCond } from '@/api/fpQualityCheck'
import { watch } from 'vue'
interface Props {
  modelValue: string | number
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
})

const emits = defineEmits(['update:modelValue', 'handleSure'])

const changeValue = (e: any) => {
  emits('update:modelValue', e.value)
}
const globalData = useGlobalData()

watch(
  () => globalData.scanerCode,
  () => {
    console.log('监听打印', globalData.scanerCode)
    getData()
  },
  { deep: true }
)

const { fetchData, data } = getDetailByCond()

const getData = async () => {
  if (!globalData.scanerCode) return
  const scanerCode = globalData.scanerCode.replace('\r', '')
  const query = {}
  if (scanerCode.split('^').length > 1) {
    query.qr_code = scanerCode
  } else {
    query.bar_code = scanerCode
  }
  //   scanerCode
  await fetchData(query)
  emits('handleSure', [data.value])
  emits('update:modelValue', scanerCode)
}
</script>
