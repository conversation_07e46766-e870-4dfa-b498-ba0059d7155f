<script setup lang="ts">
import { computed, provide, reactive, ref, watch } from 'vue'
import type { ItemProps } from './item.vue'
import ItemField from './item.vue'
import { deepClone } from '@/common/util'
import { useModalSize } from '@/hooks/useModalSize'

const props = withDefaults(defineProps<Props>(), {
  columnList: () => [],
  defaultColumnList: () => [],
  showFixed: true,
  childrenKey: 'childrenList',
})

const emits = defineEmits(['submit', 'submitAll'])

// const { user: userInfo }: any = useUserStore()

export interface Props {
  columnList: ItemProps[] // 自定义字段
  defaultColumnList: ItemProps[] // 本地模板
  saveDefaultColumnList: ItemProps[] // 已保存模板
  modelValue: boolean
  showFixed: boolean
  childrenKey?: string
}

const treeRef = ref()
const state = reactive<{ dataList: ItemProps[] }>({
  dataList: [],
})

const showModal = defineModel({
  default: false,
  required: true,
})

const defaultList = computed(() => {
  return props.defaultColumnList || []
})

const checkKeyList = ref<string[]>([])

// 递归获取所有field
const flattenData = ref<any>({}) // 根据key把数据平铺
const fieldObj = reactive<{ endField: string[], parentField: string[] }>({
  endField: [], // 最底层子节点
  parentField: [], // 存在子节点
})
function getField(list: ItemProps[], fieldList: string[] = []) {
  const childKey = props.childrenKey
  list?.map((item) => {
    item.parentKey = item.parentKey
      ? `${item.parentKey}#${item.field}`
      : item.field // 保存路径便于寻找
    flattenData.value[item.field] = { ...item }
    if (item.field && (!item?.[childKey] || item[childKey]?.length <= 0)) {
      fieldList.push(item.field)
      fieldObj.endField.push(item.field)
    }
    else {
      fieldObj.parentField.push(item.field)
    }
    if (item?.[childKey] && item[childKey]?.length > 0)
      getField(item[childKey], fieldList)
  })
  checkKeyList.value = fieldList

  return { fieldList, list: JSON.parse(JSON.stringify(list)) }
}

function submit() {
  showModal.value = false
  emits('submit', state.dataList)
}

function submitAll() {
  showModal.value = false
  emits('submitAll', state.dataList)
}
function onDelete() {
  const { fieldList } = getField(state.dataList)
  treeRef.value.setCheckedKeys(fieldList)
}
// 添加全选功能
function selectAll() {
  // 获取所有节点的 field 值
  const allNodes = treeRef.value.store.nodesMap
  const allKeys = Object.keys(allNodes)
    .filter(key => !fieldObj.parentField.includes(key)) // 只选择叶子节点
    .map(key => key)

  // 设置所有节点为选中状态
  treeRef.value.setCheckedKeys(allKeys)
}

// 添加反选功能
function invertSelection() {
  // 获取当前选中的节点
  const checkedKeys = treeRef.value.getCheckedKeys()
  // 获取所有叶子节点
  const allLeafKeys = fieldObj.endField

  // 计算需要选中的节点（当前未选中的叶子节点）
  const keysToCheck = allLeafKeys.filter(key => !checkedKeys.includes(key))
  // 计算需要取消选中的节点（当前已选中的叶子节点）
  const keysToUncheck = checkedKeys.filter(key => fieldObj.endField.includes(key))

  // 先取消所有选中
  treeRef.value.setCheckedKeys([])
  // 然后选中需要反选的节点
  treeRef.value.setCheckedKeys(keysToCheck)
}
provide('delete', onDelete)

const hasChildren = computed(() => {
  return !!(
    props.columnList
    && props.columnList[0]?.[props.childrenKey]
    && props.columnList?.[0]?.[props.childrenKey]?.length > 0
  )
})

const changeFieldDataList = ref<string[]>([])
const selectFields = ref<string[]>([])
// 新增或者删除
function fieldAddandDel(list: ItemProps[], n_list: ItemProps[] = []) {
  const childKey = props.childrenKey
  list?.map((item) => {
    if (changeFieldDataList.value.includes(item?.field)) {
      if (selectFields.value.includes(item?.field)) {
        n_list.push({
          ...item,
          [childKey]: [],
        })
      }
      else {
        // 如果是非当前操作元素复原原来数据
        // 判断本地对象和保存的模板对象相同字段是否值不同，不同则更新本地
        let key: keyof ItemProps
        if (!flattenData.value?.[item?.field]) {
          flattenData.value[item?.field] = item
        }
        else {
          for (key in item) {
            if (flattenData.value?.[item?.field] !== item[key])
              // flattenData.value[item?.field][key] = item[key]
              flattenData.value[item?.field] = item
          }
        }
        n_list.push({
          ...(flattenData.value[item?.field] || item),
          [childKey]: [],
        })
      }
      if (item?.[childKey] && item?.[childKey]?.length > 0)
        fieldAddandDel(item[childKey], n_list[n_list.length - 1][childKey])
    }
  })

  return n_list
}

const changeCheckStatus = ref<boolean>(false)
function checkChange(data: ItemProps, check: boolean) {
  changeCheckStatus.value = check
}

function checkNew(data: ItemProps, _check: ItemProps) {
  changeFieldDataList.value = treeRef.value
    .getCheckedNodes(false, true)
    ?.map((item: ItemProps) => item.field)
  selectFields.value = getNewSelected(data)
  state.dataList = fieldAddandDel(defaultList.value)
  changeFieldDataList.value = []
  selectFields.value = []
}

// 递归获取当前选中的filed
function getNewSelected(list: ItemProps[] | ItemProps, fields: string[] = []) {
  const childKey = props.childrenKey
  if (Array.isArray(list)) {
    list?.map((item) => {
      fields.push(item.field)
      if (item?.[childKey] && item?.[childKey]?.length > 0)
        getNewSelected(item[childKey], fields)
    })
  }
  else {
    fields.push(list.field)
    if (list?.[childKey] && list?.[childKey]?.length > 0)
      getNewSelected(list[childKey], fields)
  }
  return fields
}

const propsConfig = {
  label: 'title',
  children: props.childrenKey,
}

// 递归获取已保存的模板field数组
const fieldList = ref<string[]>([])
const fieldKeyValue = ref<{ [index: string]: any }>({})
function getTemplateField(list: ItemProps[]) {
  const childKey = props.childrenKey
  list?.map((item) => {
    // 保存field到 fieldList 数组里面
    fieldList.value.push(item.field)
    fieldKeyValue.value[item.field] = item
    if (item[childKey] && item[childKey]?.length > 0)
      getTemplateField(item[childKey])
  })
}

// 判断本地模板与保存模板差异(本地比保存的多)
const isDiff = ref<boolean>(false)
function templateFieldDiff(list: ItemProps[]) {
  const childKey = props.childrenKey
  list?.map((item) => {
    if (!fieldList.value.includes(item.field)) {
      checkKeyList.value.push(item.field)
      isDiff.value = true
    }
    else {
      // 判断本地字段某些值是否改变
      let key: keyof ItemProps
      if (!item[childKey] || item[childKey].length <= 0) {
        for (key in item) {
          if (
            item[key]
            && (!fieldKeyValue.value[item.field][key]
            || fieldKeyValue.value[item.field]?.[key] !== item[key])
          ) {
            fieldKeyValue.value[item.field][key] = item[key]

            isDiff.value = true
          }
        }
      }
    }
    if (item?.[childKey] && item?.[childKey]?.length > 0)
      templateFieldDiff(item[childKey])
  })
  return { list }
}

let saveStatus = false // 确保只执行一次
watch(
  () => props.columnList,
  (val) => {
    const { list } = getField(val || defaultList.value)
    let dataList = list

    // 当保存的模板比本地模板少数据时或者相同的field单其中某些在不一样时，自动保存一次
    if (
      props.saveDefaultColumnList
      && props.saveDefaultColumnList.length > 0
      && !saveStatus
    ) {
      getTemplateField(props.saveDefaultColumnList)
      templateFieldDiff(props.defaultColumnList)
      // 不相同则更新字段
      if (isDiff.value) {
        changeFieldDataList.value = [
          ...new Set([
            ...fieldObj.endField,
            ...fieldObj.parentField,
            ...checkKeyList.value,
          ]),
        ]
        dataList = fieldAddandDel(deepClone(props.defaultColumnList))
        emits('submit', dataList)
        isDiff.value = false
        saveStatus = true
      }
    }
    state.dataList = dataList
  },
  { immediate: true },
)

defineExpose({
  state,
})

const { modalWidth, modalHeight } = useModalSize()
</script>

<template>
  <vxe-modal
    v-model="showModal"
    type="confirm"
    title="字段编辑"
    :width="modalWidth"
    :height="modalHeight"
    min-height="500"
    :mask="true"
    :lock-view="true"
    esc-closable
    show-footer
    @confirm="submit"
  >
    <div class="min-h-[300px] overflow-hidden flex justify-between field_main">
      <div class="field_list">
        <el-tree
          ref="treeRef"
          class="h-full"
          node-key="field"
          :default-checked-keys="checkKeyList"
          :data="defaultList"
          :props="propsConfig"
          show-checkbox
          @check="checkNew"
          @check-change="checkChange"
        />
      </div>
      <div class="field_setting w-[60%]">
        <ItemField
          v-model="state.dataList"
          :show-fixed="props.showFixed"
          :has-children="hasChildren"
          @delete="onDelete"
        />
      </div>
    </div>
    <template #footer>
      <div class="flex justify-between">
        <div>
          <el-button type="primary" @click="selectAll">
            全选
          </el-button>
          <el-button type="info" @click="invertSelection">
            反选
          </el-button>
        </div>
        <div>
          <el-button
            v-has="'FieldApiKeyGlobalBtn'"
            type="warning"
            @click="submitAll"
          >
            全局保存
          </el-button>
          <el-button type="primary" @click="submit">
            保存
          </el-button>
        </div>
      </div>
    </template>
  </vxe-modal>
</template>

<style scoped lang="scss">
.field_main {
  height: 100%;
}

.field_list {
  flex: 1;
  display: flex;
  height: 100%;
  overflow: scroll;
}

.field_setting {
  //height: 500px;
  overflow: scroll;
  border-left: 1px solid #ccc;
}
</style>
