<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { SortableEvent } from 'sortablejs'
import { computed, inject, reactive, ref, watch } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

export interface ItemProps {
  title: string
  field: string
  fixed: 'left' | 'right' | ''
  show?: boolean
  parentKey?: string
  childrenList?: ItemProps[]
  minWidth?: number
  width?: number
  addStatus?: boolean
}

export interface Props {
  modelValue: ItemProps[]
  hasChildren?: boolean // 是否多表头
  showFixed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hasChildren: false,
  showFixed: true,
})
const emits = defineEmits(['update:modelValue', 'delete'])
const onDelete = inject('delete', () => {})
const editField = ref('')
function showEditName(item: ItemProps) {
  editField.value = item.field
}

function handBlur(item: ItemProps) {
  editField.value = ''
  emits('update:modelValue', dataList.value)
}

const dataList = ref<ItemProps[]>()
watch(
  () => props.modelValue,
  (val) => {
    if (!val)
      return false
    dataList.value = val?.map((item: ItemProps) => {
      item.show = item.show !== false
      return item
    })
  },
  { immediate: true, deep: true },
)

function changeFixedStatus(item: any) {
  item.fixed = !item.fixed ? 'left' : item.fixed === 'left' ? 'right' : item.fixed === 'right' ? '' : 'left'
  emits('update:modelValue', dataList.value)
}

function fixedStatus(item: any) {
  return !item.fixed ? '不固定' : item.fixed === 'left' ? '固定左' : '固定右'
}

// const changeShow = (item: any) => {
//   item.show = !item.show
//   emits('update:modelValue', dataList.value)
// }

const list = computed({
  get: () => props.modelValue,
  set: (value) => {
    emits('update:modelValue', value)
  },
})

let startData: ItemProps[]
function onStart() {
  startData = JSON.parse(JSON.stringify(list.value))
}

function onEnd(event: SortableEvent) {
  const newData = list.value[event.newIndex]
  const oldData = list.value[event.oldIndex]
  if (props.hasChildren && startData && (findChildren(newData) || findChildren(oldData))) {
    ElMessage.warning('多表头无法与固定列交换位置')
    list.value = startData
    return false
  }
}

function findChildren(item: ItemProps | ItemProps[], list: ItemProps[] = []) {
  if (!Array.isArray(item)) {
    item.fixed && list.push(item)
    if (item?.childrenList && item?.childrenList.length > 0)
      findChildren(item.childrenList, list)
  }
  else {
    item.map((item) => {
      item.fixed && list.push(item)
      if (item?.childrenList && item?.childrenList.length > 0)
        findChildren(item.childrenList, list)
    })
  }
  return list.length
}

function deleteData(val: ItemProps, index: number) {
  list.value = list.value.filter((item, i) => {
    return index !== i
  })
  onDelete()
}

const updateState = reactive<{ updateShow: boolean, updateItem: Omit<ItemProps, 'field'>, selectItem: Omit<ItemProps, 'field'> }>({
  updateShow: false,
  updateItem: {
    title: '',
    fixed: '',
    minWidth: 100,
  },
  selectItem: {
    title: '',
    fixed: '',
    minWidth: 100,
  },
})
function updateData(val: ItemProps, index: number) {
  updateState.updateShow = true
  updateState.selectItem = val
  updateState.updateItem = {
    ...val,
    minWidth: val?.minWidth || 150,
  }
}
function updateSubmit() {
  updateState.selectItem.title = updateState.updateItem.title
  updateState.selectItem.fixed = updateState.updateItem.fixed
  updateState.selectItem.minWidth = updateState.updateItem.minWidth
  delete updateState.selectItem.width
  updateState.updateShow = false
}

const optionsSelect = [
  { label: '不固定', value: '' },
  { label: '固定左', value: 'left' },
  { label: '固定右', value: 'right' },
]
defineExpose({})
</script>

<template>
  <VueDraggable ref="el" v-model="list" :animation="150" class="self_class" handle=".move_btn" @start="onStart" @end="onEnd">
    <div v-for="(item, index) in dataList" :key="index" :fixed="!!item.fixed" class="flex justify-between p-[3px] field_list text-[13px] self_class">
      <div class="ml-[10px] w-[100%] py-[5px] bg-gray-500/5 rounded px-3">
        <div class="flex justify-between w-[100%]">
          <div class="flex items-center flex-1" @dblclick="showEditName(item)">
            <i class="vxe-icon-menu cursor-move text-[12px] mr-1 move_btn" />
            <el-input v-if="item.field && editField === item.field" v-model="item.title" size="small" @blur="handBlur(item)" />
            <span v-else>{{ item.title }}</span>
          </div>
          <div class="btns">
            <el-link v-if="!props.hasChildren && props.showFixed" :type="item.fixed === 'left' ? 'success' : item.fixed === 'right' ? 'primary' : 'info'" @click="changeFixedStatus(item)">
              {{ fixedStatus(item) }}
            </el-link>
            <el-link type="primary" @click.stop="updateData(item, index)">
              编辑
            </el-link>
            <el-link type="danger" @click.stop="deleteData(item, index)">
              删除
            </el-link>
          </div>
        </div>
        <Item v-model="item.childrenList" :has-children="props.hasChildren" />
      </div>
    </div>
  </VueDraggable>
  <vxe-modal v-if="updateState.updateShow" v-model="updateState.updateShow" width="300" title="编辑" show-footer>
    <el-form label-position="left" label-width="100px" style="max-width: 460px">
      <el-form-item label="字段名称">
        <el-input v-model="updateState.updateItem.title" />
      </el-form-item>
      <el-form-item v-if="!props.hasChildren && props.showFixed" label="固定位置">
        <el-select v-model="updateState.updateItem.fixed" placeholder="Select">
          <el-option v-for="item in optionsSelect" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="最小宽度">
        <el-input-number v-model="updateState.updateItem.minWidth" :min="1" controls-position="right" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" size="small" @click="updateSubmit">
        确定
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.field_list {
  //   border-bottom: 1px solid #ebebeb;
  .btns {
    .el-link {
      margin-left: 5px;
    }
  }
}
</style>
