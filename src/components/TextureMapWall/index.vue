<script setup lang="ts">
import { computed, ref, useAttrs, watch } from 'vue'
import { type UseDraggableReturn, VueDraggable } from 'vue-draggable-plus'
import { Close } from '@element-plus/icons-vue'
import UploadFile from '@/components/UploadFile/index.vue'
import { fileIsPdf, getFileType } from '@/common/uploadImage'
import { formatFileName, getFileName } from '@/common/util'

interface textType {
  // 主体内容
  text: string
  // 是否显示提示文字
  textShow?: boolean
  // 开启复制黏贴上传功能
  paste: boolean
  // 图片
  imageList?: Array<string>
  // 是否可以多选
  multiple: boolean
}

const props = withDefaults(defineProps<textType>(), {
  text: '第一图将作为面料主图，可通过拖拽改变主图',
  paste: true,
  textShow: true,
  multiple: true,
})

const emit = defineEmits(['update:imageList'])
const attrs = useAttrs()
const el = ref<UseDraggableReturn>()
const newFileList = ref<string[]>([])

// 拖拽
function onUpdate() {
  emit('update:imageList', newFileList.value)
}

// 删除图片
function deleteImage(url: string) {
  newFileList.value = newFileList.value.filter((item: any) => item !== url)
  emit('update:imageList', newFileList.value)
}

watch(() => props.imageList, () => {
  if (props.imageList)
    newFileList.value = props.imageList.map(url => url)
  else newFileList.value = []
}, {
  immediate: true,
  deep: true,
})

// 上传图片
function handUpload(fileList: any) {
  // 单张图片
  if (!props.multiple) {
    newFileList.value = fileList
    emit('update:imageList', newFileList.value)
    return
  }
  const tempArray: Array<string> = []
  fileList.forEach((url: string) => {
    if (!newFileList.value.includes(url))
      tempArray.push(url)
  })
  newFileList.value.push(...tempArray)
  emit('update:imageList', newFileList.value)
}
// 图片
const imageList = computed(() => {
  return newFileList.value.filter((e: string) => getFileType(e) === 'image')
})
// 获取当前图片下标
function getImageIndex(url: string) {
  return imageList.value.findIndex(e => e === url)
}
</script>

<template>
  <!--  <div v-if="textShow" class="w-full bg-[#FDF6ED] text-[#E9B16C] leading-[50px] px-[30px] text-sm"> -->
  <!--    {{ props.text }} -->
  <!--  </div> -->
  <div :class="{ 'w-full': newFileList.length > 0 }" class="flex items-center">
    <div class=" flex align--center">
      <UploadFile
        v-model:file-list="imageList"
        :paste="paste"
        :image-shown="false"
        :multiple="multiple"
        v-bind="attrs"
        @on-upload-success="handUpload"
      />
    </div>
    <div class="flex flex-wrap overflow-y-scroll mt-[20px]">
      <VueDraggable
        ref="el"
        v-model="newFileList"
        :animation="150"
        class="flex flex-wrap overflow-y-scroll"
        :class="{ 'w-full': newFileList.length > 0 }"
        @update="onUpdate"
      >
        <div
          v-for="(item) in newFileList" :key="item"
          class="h-[140px] flex justify-center items-center p-1 relative min-w-[130px] "
        >
          <div v-if="getFileType(item) !== 'image'" class="rounded-lg bg-slate-100 flex items-center justify-center w-[130px] h-[130px]">
            <el-link type="primary" :href="formatFileName(item)" :target="fileIsPdf(item) ? '_blank' : '_self'">
              {{ getFileName(item) }}
            </el-link>
          </div>
          <!-- /其他文件 -->
          <el-image
            v-else
            :preview-src-list="imageList" :initial-index="getImageIndex(item)" :src="item"
            fit="contain"
            class="w-full h-full rounded-lg"
          />
          <el-button
            :icon="Close" size="small" circle color="#FFF" dark bg
            class="absolute top-[2px] right-[3px]"
            @click="deleteImage(item)"
          />
        </div>
      </vuedraggable>
    </div>
  </div>
</template>
