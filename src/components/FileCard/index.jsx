import { ref, computed, defineComponent, watch } from 'vue'
import styles from './index.module.scss'
import { getFileType } from '@/common/uploadImage'
import { formatUrl } from '@/common/util'
import PreviewImage from '@/components/PreviewImage/index.vue'
import PreviewVideo from '@/components/PreviewVideo/index.vue'
import { useDownloadFile } from '@/use/useDownLoad'
import { IMG_CND_Prefix } from '@/common/constant'
// import { fileTypeFromFile } from '/node_modules/file-type/index'
// const result = fileTypeFromFile('https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF')
// console.log('filetype===>',result)
// const props = defineProps({

// })

export default defineComponent({
  name: 'FileCard',
  props: {
    url: {
      type: String,
      require: true,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
    leftSlot: {
      type: String,
      default: 'preview',
    },
    rightSlot: {
      type: String,
      default: 'delete',
    },
    fileType: {
      type: String,
      default: '',
    },
  },
  emits: ['preview', 'click', 'remove', 'download'],
  setup(props, { emit }) {
    watch(
      () => props.url,
      () => {
        console.log('props.url:::', props.url)
      },
      { immediate: true }
    )
    const fullUrl = computed(() => {
      console.log('url: ===>', formatUrl(props.url))
      return formatUrl(props.url)
    })
    console.log('styles', styles)
    // 识别文件类型  fileType辅助识别 文件类型
    const fileType = getFileType(props.url) || props.fileType
    // 预览
    let handlePreview = () => {
      return {
        image: () => dispatchImagePreview(),
        video: () => dispatchVideoPreview(),
        audio: () => dispatchAudioPreview(),
        office: () => dispatchOfficePreview(),
      }[fileType]?.()
    }
    // 删除
    const handleRemove = () => {
      emit('remove', props.url)
    }
    // 下载
    const handleDownload = () => {
      console.log('下载')
      useDownloadFile(`${fullUrl.value}`, `${fullUrl.value}`)
    }

    const handleClick = () => {
      emit('click', props.url)
    }

    const ImageIcon = <img class={styles['upload-list__item-thumbnail']} src={fullUrl.value} alt="康康图片" />
    const VideoIcon = <i class={`${styles['file-svg']} ${styles['upload-list__item-thumbnail']} fas fa-file-video`}></i>
    const AudioIcon = <i class={`${styles['file-svg']} ${styles['upload-list__item-thumbnail']} fas fa-file-audio`}></i>
    const Unknown = <i class={`${styles['file-svg']} ${styles['upload-list__item-thumbnail']} fas fa-file`}></i>
    const PDFIcon = <i class="upload-list__item-thumbnail fa-solid fa-file-pdf"></i>
    // 预览视图
    const previewViewWrapper = () => (
      <span class={styles['upload-list__item-preview']} onClick={handlePreview}>
        <i class="fas fa-search-plus"></i>
      </span>
    )
    // 默认视图
    const defaultViewWrapper = () => (
      <span class={styles['upload-list__item-preview']} onClick={handleClick}>
        <i class="fas fa-chart-bar"></i>
      </span>
    )
    // 下载视图
    const downloadViewWrapper = () => (
      <span class={styles['upload-list__item-delete']} onClick={handleDownload}>
        <i class="fas fa-download"></i>
      </span>
    )

    const deleteViewWrapper = () => (
      <span class={styles['upload-list__item-delete']} onClick={handleRemove}>
        <i class="fas fa-trash-alt"></i>
      </span>
    )

    const PreviewImageComp = ref(null)
    const PreviewVideoComp = ref()

    const previewImageWrapper = () => <PreviewImage style="margin: 0;" labelMode src={fullUrl.value} ref={PreviewImageComp} />
    const previewVideoWrapper = () => <PreviewVideo style="margin: 0;" labelMode src={fullUrl.value} ref={PreviewVideoComp} />

    const CardDispatcher = () => {
      console.log('url==>', props.url, fileType)
      if (fileType === 'image') {
        console.log('file image')
        return {
          icon: ImageIcon,
          preview: previewImageWrapper,
        }
      } else if (fileType === 'video') {
        return {
          icon: VideoIcon,
          preview: previewVideoWrapper,
        }
      } else if (fileType === 'audio') {
        return {
          icon: AudioIcon,
          preview: previewVideoWrapper,
        }
      } else {
        return {
          icon: Unknown,
          preview: () => null,
        }
      }
    }
    // 图片
    const dispatchImagePreview = () => {
      console.log('预览', previewImageWrapper)
      PreviewImageComp.value.handleShowModal()
    }
    // 视频
    const dispatchVideoPreview = () => {
      PreviewVideoComp.value.handleShowModal()
    }
    // 音频
    const dispatchAudioPreview = () => {
      // PreviewVideoComp.value.handleShowModal()
    }
    // pdf、docs等等
    const dispatchOfficePreview = () => {
      window.open(fullUrl.value)
    }

    // 左边按钮
    const leftOptions = {
      preview: previewViewWrapper,
      default: defaultViewWrapper,
      none: null,
    }

    // 右边按钮
    const rightOptions = {
      delete: deleteViewWrapper,
      download: downloadViewWrapper,
      default: defaultViewWrapper,
      none: null,
    }

    const contextSlot = () => {
      let leftButton, rightButton
      if (props.leftSlot) {
        console.log('leftSlot==>', props.leftSlot)
        leftButton = leftOptions[props.leftSlot]
      }
      if (props.rightSlot) {
        rightButton = rightOptions[props.rightSlot]
        console.log('rightSlot==>', rightButton)
      }
      return {
        leftButton,
        rightButton,
      }
    }

    const cardCtx = CardDispatcher()
    const context = contextSlot()
    console.log('cardCtx==>', cardCtx)
    return () => {
      let container = `${styles['upload--picture-card']} ${styles['image-container']}`
      return (
        <>
          <div class={container} style={{ width: props.width, height: props.height, ...props.customStyle }}>
            {cardCtx.icon}
            <span class={styles['upload-list__item-actions']}>
              {context.leftButton?.()}
              {context.rightButton?.()}
            </span>
          </div>
          {cardCtx.preview()}
        </>
      )
    }
  },
})
