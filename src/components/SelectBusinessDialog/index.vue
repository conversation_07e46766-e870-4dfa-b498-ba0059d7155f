<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'

defineOptions({
  name: 'SelectBusinessDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'name',
  disabled: false,
  api: 'business_unitlist',
  showUnitType: true, // 是否显示往来单位类型
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  (e: 'update:id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const id = defineModel<number>('')
interface Props {
  query?: any
  defaultValue?: {
    id?: number
    name?: string
    code?: string
  }
  field?: 'name' | 'code'
  api?: keyof typeof selectApi | ''
  disabled?: boolean
  showUnitType?: boolean
  isPushDefaultData?: boolean
}
const fieldName = {
  name: '名称',
  code: '编号',
}

const selectRef = ref()
const matchDefaultValue = ref<{
  id?: number
  name?: string
  code?: string
}>()
watch(() => props.defaultValue, (newVal, oldVal) => {
  if (newVal?.id !== oldVal?.id && newVal?.[props.field] !== oldVal?.[props.field])
    matchDefaultValue.value = props.defaultValue
}, { immediate: true })

const componentRemoteSearch = reactive({
  name: '',
  code: '',
})
function handleInput(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  matchDefaultValue.value = {
    id: val.id,
    name: val.name,
    code: val.code,
  }
  emits('changeValue', val)
  emits('update:id', val.id)
}

defineExpose({
  selectRef,
})
</script>

<template>
  <SelectDialog
    ref="selectRef"
    v-model="id"
    :label-field="field"
    :api="api"
    :disabled="disabled"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :query-config="{
      pagination: true,
    }"
    :is-push-default-data="isPushDefaultData"
    :column-list="[
      {
        field: 'name_group',
        colGroupHeader: true,
        title: '名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'name',
            title: '名称',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'code_group',
        colGroupHeader: true,
        title: '编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'code',
            title: '编号',
            minWidth: 100,
          },
        ],
      },
      ...(showUnitType ? [{
        field: 'unit_type_name_group',
        colGroupHeader: false,
        title: '',
        minWidth: 100,
        childrenList: [
          {
            field: 'unit_type_name',
            title: '往来单位类型',
            minWidth: 100,
          },
        ],
      },
      ] : []),
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: matchDefaultValue ? {
          id: matchDefaultValue?.id,
          name: matchDefaultValue?.name,
          code: matchDefaultValue?.code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInput"
  />
</template>

<style scoped></style>
