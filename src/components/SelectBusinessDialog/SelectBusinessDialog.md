# SelectBusinessDialog

## 简介

SelectBusinessDialog 是一个基于 SelectDialog 的子组件，用于选择加工单位。
默认支持根据name或code自动输入搜索，数据联动请通过changeValue事件自行处理

#### 分页选择时，名称自动赋值到搜索框里

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| id | number \| null | null | 选中的成品的 id，可以使用 v-model 双向绑定 |
| defaultValue | object \| null | null | 默认的加工单位，包含 id  \| name \| code |
| field | 'code' \| 'name' | 'name' | 用于显示的加工单位的字段，可以是成品编号或成品名称 |
| query | object | {} | 额外请求参数 |
| disabled | boolean | false | 是否禁用 |
| showUnitType | boolean | true | 是否显示往来单位类型 |
| isPushDefaultData | boolean | false | 是否需要追加默认数据-分页获取不到的 |

## 事件

| 事件名                | 参数 | 说明 |
|--------------------| --- | --- |
| update:id | val: number | 当选中的成品 id 发生变化时，触发该事件，同时更新 id 的值 |
| changeValue        | val: number | 当选中的成品 id 发生变化时，触发该事件，可以用于自定义处理逻辑 |
| onInput            | val: string | 当输入的成品名称发生变化时，触发该事件，可以用于自定义处理逻辑 |

## 示例

```vue
<script setup lang="ts">
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

defineOptions({
  name: 'Example',
})

const id = ref<number | null>(null)

const componentRemoteSearch = reactive({
  code: '',
  name: '',
})

function changeSelect(val: number) {
  console.log('id changed to', val)
}

function onInput(val: string) {
  console.log('input is', val)
}
</script>

<template>
  <SelectBusinessDialog
    v-model="id"
    field="code"
    :default-value="{
      id: filterData.product_id,
      name: componentRemoteSearch.name,
      code: componentRemoteSearch.code,
    }"
    @change-value="changeSelect"
    @on-input="onInput"
  />
</template>
