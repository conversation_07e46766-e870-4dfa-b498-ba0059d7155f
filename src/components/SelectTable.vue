<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { VxeGrid, type VxeGridInstance } from 'vxe-table'
import { GetFinishProductDropdownList } from '@/api/finishPurchaseWarehouseEntry'
import { getFilterData } from '@/common/util'

const { fetchData: getProductDropdownList } = GetFinishProductDropdownList()
const payload = ref({
  page: 1,
  size: 50,
  total: 0,
})
const dataList = ref<any>([])

async function getData() {
  const result: any = await getProductDropdownList(getFilterData(payload.value))
  dataList.value = result.data.list
}

onMounted(() => {
  getData()
})

const inputRef = ref(null) // 输入框的实例
const pulldownRef = ref() // 下拉输入框实例
const girdRef = ref<VxeGridInstance>() // 模糊搜索的table实例
const loading = ref(false) // 下拉列表加载中
const value = ref('') // 输入框输入内容

// 点击清除按钮清除输入内容
function clearValue() {
  value.value = ''
}
// input输入的内容
function changeInput(_val: any) {
  //
}
// 输入框聚焦
function focusEvent() {
  const $pulldown = pulldownRef.value
  if ($pulldown)
    $pulldown.showPanel()
  nextTick(() => {
    girdRef.value?.setCurrentRow(girdRef.value?.getData(0))
  })
}

// 键盘输入事件
function handleInputKeyup({ $event }: { $event: any }) {
  const currentRow = girdRef.value?.getCurrentRecord() ?? girdRef.value?.getData(0) // 获取当前行
  const currentIndex = girdRef.value?.getRowIndex(currentRow) || 0 // 数据当前行的索引
  switch ($event.keyCode) {
    case 38: // 上键
      if (girdRef.value) {
        const prev = girdRef.value?.getData(currentIndex - 1)
        if (prev) {
          girdRef.value?.setCurrentRow(prev)
          girdRef.value?.scrollToRow(prev)
        }
      }
      break
    case 40: // 下键
      // 下
      if (girdRef.value) {
        const next = girdRef.value?.getData(currentIndex + 1)
        if (next) {
          girdRef.value?.setCurrentRow(next)
          girdRef.value?.scrollToRow(next)
        }
      }

      break
    case 13: // 回车键
      cellClickEvent({ row: girdRef.value?.getData(currentIndex) })
      girdRef.value?.scrollToRow(currentRow)
      pulldownRef.value.hidePanel()
      // 使输入框失去焦点
      if (inputRef.value)
        (inputRef.value as HTMLElement).blur()

      break
    default:
      break
  }
}

// 选中事件
function cellClickEvent({ row }: any) {
  value.value = row.cylinder_number
  pulldownRef.value.hidePanel()
}
// 失去焦点
function blurEvent() {}

// 正在输入
function inputEvent() {}
</script>

<template>
  <vxe-pulldown ref="pulldownRef" transfer>
    <template #default>
      <vxe-input
        ref="inputRef"
        v-model="value"
        placeholder="请输入"
        @change="(val) => changeInput(val)"
        @focus="focusEvent"
        @keyup="handleInputKeyup"
        @blur="blurEvent"
        @input="inputEvent"
      >
        <template #suffix>
          <!--          清除按钮 -->
          <i v-if="value" class="vxe-icon-close vxe_icon" @click="clearValue" />
          <!--          展开模态框按钮 -->
          <i class="vxe-icon-table vxe_icon" />
        </template>
      </vxe-input>
    </template>
    <template #dropdown>
      <el-scrollbar max-height="300">
        <VxeGrid
          ref="girdRef"
          border
          gird-refborder
          auto-resize
          :loading="loading"
          :scroll-y="{ enabled: true }"
          height="300px"
          :row-config="{ isHover: true, isCurrent: true }"
          :keyboard-config="{
            isArrow: true,
            isEnter: true,
          }"
          :data="dataList"
          :columns="[{ title: '成品编号', field: 'finish_product_code' }]"
          @cell-click="cellClickEvent"
        />
      </el-scrollbar>
    </template>
  </vxe-pulldown>
</template>

<style scoped lang="scss">
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0 !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  cursor: pointer;
}

.vxe_icon {
  margin-right: 6px;
  font-size: 14px;
  cursor: pointer;
}
</style>

<style lang="scss" scoped>
::v-deep(.vxe-input--suffix) {
  width: auto;
}

::v-deep(.vxe-input.is--suffix .vxe-input--inner) {
  padding-right: 46px;
}
</style>
