<!--
  @Description: 选择框底部的操作栏
  1、若组件传递quickAddLink则使用该路由Path跳转，显示底部操作栏
  2、新增跟刷新按钮需同时存在
  3、routerPathApiArr 数组里的api请求是固定显示底部操作栏
 -->
<script setup lang="ts">
import { Plus, RefreshRight } from '@element-plus/icons-vue'
import { computed, nextTick, ref, useAttrs } from 'vue'
import { useRouter } from 'vue-router'
import { DictionaryType } from '@/common/enum'
import { useUserStore } from '@/stores'

interface Props {
  quickAddLink?: string
  quickAddPremission?: string
  customClass?: string
}
const props = withDefaults(defineProps<Props>(), {
  quickAddLink: '', // 新增的跳转链接
  quickAddPremission: '', // 新增的权限
  customClass: '', // 自定义的class
})
const emit = defineEmits(['refresh'])
const attrs = useAttrs()
const router = useRouter()
const bottomBarComRef = ref()
const userStore = useUserStore()
// 路由path和api枚举的对应关系
// TODO: 注意没有传入api不会默认配置底部操作栏，有因为这里匹配不到
const routerPathApiArr = [
  { // 基本单位
    routerPath: '/basicData/measuringUnit',
    apiEnums: ['getInfoBaseMeasurementUnitList', 'getInfoBaseMeasurementUnitEnumList'],
    addPermission: 'MeasuringUnit_add',
  },
  { // 布种类型
    routerPath: '/basicData/clothType',
    apiEnums: ['getTypeFabricList', 'GetTypeFabricEnumList'],
    addPermission: 'ClothType_add',
  },
  { // 坯布订单类型
    routerPath: '/basicData/embryoType',
    apiEnums: ['getTypeGreyFabricOrderList'],
    addPermission: 'EmbryoType_add',
  },
  { // 供应商
    routerPath: '/contactUnitMange/supplierAdd',
    apiEnums: ['business_unitsupplierlist', 'BusinessUnitSupplierEnumlist', 'BusinessUnitSupplierEnumAll', 'GetBusinessUnitListApi', 'business_unitlist'],
    addPermission: 'SupplierMange_add',
  },
  { // 供应商类型
    routerPath: '/basicData/contactUnit',
    apiEnums: ['AdminuenumsupplierType'],
    addPermission: 'ContactUnit_add',
  },
  { // 所属营销体系
    routerPath: '/basicData/marketingSystemAdd',
    apiEnums: ['GetSaleSystemDropdownListApi', 'GetSaleSystemDropdownListV2', 'AdminsaleSystemgetSaleSystemDropdownList'],
    addPermission: 'MarketingSystem_add',
  },
  { // 含税项目
    routerPath: '/basicData/taxInclusive',
    apiEnums: ['GetInfoSaleTaxableItemEnumList'],
    addPermission: 'TaxInclusive_add',
  },
  { // 织造组织
    routerPath: `/systemTools/detail/${DictionaryType.weavingOrganization}`,
    apiEnums: ['GetDictionaryDetailEnumListApi'],
    matchQuery: {
      dictionary_id: DictionaryType.weavingOrganization,
    },
    addPermission: 'Dictionary_add',
  },
  { // 织造规格
    routerPath: '/basicData/weavingSpecification',
    apiEnums: ['getInfoProductWeaveSpecificationList'],
    addPermission: 'WeavingSpecification_add',
  },
  { // 坯布资料
    routerPath: '/basicData/greyFabricInformationAdd',
    apiEnums: ['GetGreyFabricInfoListUseByOthersMenu', 'GetGreyFabricInfoListUseByOthers'],
    addPermission: 'GreyFabricInformation_add',
  },
  { // 员工
    routerPath: '/peopleManagement/employeesManagementAdd',
    apiEnums: ['GetEmployeeListEnum', 'Adminemployeelist'],
    addPermission: 'EmployeesManagement_add',
  },
  { // 部门
    routerPath: '/peopleManagement/departmentManagement',
    apiEnums: ['GetDepartmentApi'],
    addPermission: 'DepartmentManagement_add',
  },
  { // 发票抬头
    routerPath: '/basicData/invoiceTitle',
    apiEnums: ['GetInfoPurchaseInvoiceHeaderListUseByOther'],
    addPermission: 'InvoiceTitle_add',
  },
  { // 客户编号/客户名称
    routerPath: '/contactUnitMange/customerAdd',
    apiEnums: ['GetCustomerEnumList'],
    addPermission: 'CustomerMange_add',
  },
  { // 销售区域
    routerPath: '/contactUnitMange/salesArea',
    apiEnums: ['Adminbusiness_unitsale_arealist'],
    addPermission: 'SalesArea_add',
  },
  { // 销售群体
    routerPath: '/contactUnitMange/salesGroup',
    apiEnums: ['Adminbusiness_unitsale_grouplist'],
    addPermission: 'SalesGroup_add',
  },
]
// 读取路由地址
const quickAddRouerPath = computed(() => {
  const apiUrl = attrs.api as string
  // 如果直接配置了则读取配置的路由地址
  if (props.quickAddLink) {
    // 有配置按钮权限
    if (props.quickAddPremission)
      return hasPermission(props.quickAddPremission) ? props.quickAddLink : ''
    // 匹配路由获取权限
    const matchRouterData = routerPathApiArr.find(item => item.routerPath === props.quickAddLink)
    if (matchRouterData)
      return hasPermission(matchRouterData?.addPermission || '') ? props.quickAddLink : ''
    else return props.quickAddLink
  }
  else if (apiUrl) {
    // 处理自定义的路由名称
    for (const item of routerPathApiArr) {
      if (item.apiEnums.includes(apiUrl)) {
        const query: any = attrs.query || {}
        const matchQuery: any = item.matchQuery || {}
        const isMatch = Object.keys(matchQuery).every(key => query[key] === matchQuery[key]) // 匹配查询参数
        if (isMatch)
          return hasPermission(item?.addPermission || '') ? item.routerPath : ''
      }
    }
  }
  return ''
})
// 判断是否有权限
function hasPermission(btnPremissionName: string) {
  if (!btnPremissionName)
    return true
  return userStore.user?.button_codes?.includes(btnPremissionName)
}
// 新增跳转
function handleClickLink() {
  router.push({
    path: quickAddRouerPath.value,
  })
}
function handleRefresh() {
  emit('refresh')
}

/**
 * 把当前组件放置在cascaderPanel框中
 * 1、在el-cascader组件中需要显示该操作栏组件的需要调用此方法
 * 2、在el-cascader组件的visible-change事件中调用此方法
 * 3、参考src\components\SelectCascader\index.vue 里的使用
 * @param cascaderRef
 */
function handleComponentPosition(cascaderRef: any) {
  nextTick(() => {
  // 判断当前是否已经存在底部栏组件
    const cascaderPanelElement = cascaderRef?.cascaderPanelRef?.$el || ''
    if (!cascaderPanelElement?.parentNode?.querySelector('.bottomBar')) {
      const content = bottomBarComRef.value
      if (content) {
        // 清除隐藏样式-在引入组件的地方需要添加hidden样式
        content?.classList.remove('hidden')
        cascaderPanelElement?.parentNode?.appendChild(content)
      }
    }
  })
}
defineExpose({
  handleComponentPosition,
})
</script>

<template>
  <div v-if="quickAddRouerPath" ref="bottomBarComRef" class="bottomBar flex justify-between items-center w-full" :class="customClass">
    <div>
      <el-link type="primary" :underline="false" :icon="Plus" @click="handleClickLink">
        新增
      </el-link>
    </div>
    <div>
      <el-link :underline="false" :icon="RefreshRight" class=" active:scale-90" @click="handleRefresh">
        刷新
      </el-link>
    </div>
  </div>
</template>
