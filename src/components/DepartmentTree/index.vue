<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElTree } from 'element-plus'
import type { TreeKey } from 'element-plus/es/components/tree/src/tree.type'
import { onMounted, ref, watch } from 'vue'
import { debounce, getFilterData } from '@/common/util'
import { GetDepartmentTreeListApi } from '@/api/employees'

defineOptions({
  name: 'DepartmentTree',
})
const props = withDefaults(defineProps<DepartmentTreeProps>(), {
  type: 'radio',
  // eslint-disable-next-line vue/require-valid-default-prop
  modelValue: 0,
})
const emit = defineEmits<{
  (e: 'update:modelValue', value: TreeKey | TreeKey[]): void
  (e: 'change', value: TreeKey | TreeKey[]): void
}>()
interface DepartmentTreeProps {
  type: 'checkbox' | 'radio'
  modelValue: TreeKey | TreeKey[]
}
interface Tree {
  id: TreeKey
  name: string
  sub_department?: Tree[]
}
const defaultProps = {
  children: 'sub_department',
  label: 'name',
}

const treeRef = ref<InstanceType<typeof ElTree>>()

function resetChecked() {
  treeRef.value!.setCheckedKeys([], false)
}

function handleCheckChange(data: Tree) {
  if (props.type === 'radio') {
    resetChecked()
    treeRef.value!.setCheckedKeys([data.id], false)
    // treeRef.value!.setCheckedNodes([] as Node[], false)
    emit('update:modelValue', data.id)
    emit('change', data.id)
  }
  else {
    const checkedKeys = treeRef.value!.getCheckedKeys(false)
    emit('update:modelValue', checkedKeys)
    emit('change', checkedKeys)
  }
}

const { fetchData, data, success } = GetDepartmentTreeListApi()

const departmentName = ref('')

const departmentList = ref<Tree[]>([])

async function getData() {
  await fetchData(
    getFilterData({
      code_or_name: departmentName.value,
    }),
  )
  if (!success.value)
    return ElMessage.error('获取部门列表失败')

  departmentList.value = data.value.list || []
}

watch(departmentName, debounce(getData, 300))

onMounted(() => {
  getData()
})
</script>

<template>
  <div style="min-width: 150px">
    <el-input v-model="departmentName" :suffix-icon="Search" class="mb-10" placeholder="请输入部门名称" clearable />
    <ElTree ref="treeRef" node-key="id" show-checkbox :data="departmentList" :props="defaultProps" @check="handleCheckChange" />
  </div>
</template>

<style scoped></style>
