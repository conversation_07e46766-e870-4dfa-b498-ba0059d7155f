<script setup lang="ts">
import { h, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { handleCustomerMarketingList } from './common'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectSaleSystemDialog from '@/components/SelectSaleSystemDialog/index.vue'
import { getFilterData } from '@/common/util'

defineOptions({
  name: 'SelectCustomerDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  disabled: false,
  sale_system_id: null,
  field: 'name',
  showAddLink: true,
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
  isMerge: false, // 编号名称是否需要合并显示
  showChoiceSystem: false, // 是否需要显示选择营销体系
})

const emits = defineEmits<{
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const customer_id = defineModel<number | string>()

const fieldName = {
  name: '客户名称',
  code: '客户编号',
}

interface Props {
  query?: any
  disabled?: boolean
  sale_system_id?: number | null
  defaultValue?: {
    id: number
    name?: string
    code?: string
  }
  field: 'code' | 'name'
  // 是否显示新增客户链接
  showAddLink?: boolean
  isPushDefaultData?: boolean
  isMerge?: boolean // 编号名称是否需要合并显示
  showChoiceSystem?: boolean // 是否需要显示选择营销体系
}

const customerRef = ref()
const showChoiceSystemModal = ref(false)// 选择营销体系弹窗

const matchDefaultValue = ref<{
  id?: number
  name?: string
  code?: string
}>()

// 监听默认值变化
watch(() => props.defaultValue, (newVal, oldVal) => {
  if (newVal?.id !== oldVal?.id && newVal?.[props.field] !== oldVal?.[props.field])
    matchDefaultValue.value = props.defaultValue
}, { immediate: true })
const componentRemoteSearch = reactive({
  customer_name: '',
})

const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})

function handleInputCustomerName(val: string) {
  componentRemoteSearch.customer_name = val.trim()
  emits('onInput', val)
}
const selectCustomer = ref()// 选中的客户
function handleChangeValue(val: any) {
  matchDefaultValue.value = {
    id: val.id,
    name: val.name,
    code: val.code,
  }

  // 处理营销体系
  const customerInfo = handleCustomerMarketingList(val)
  // 只有一个营销体系
  try {
    if (props.showChoiceSystem && customerInfo.sale_system_list?.length === 1) {
      val.select_sale_system_id = customerInfo?.sale_system_list[0]?.id
    }
    else if (props.showChoiceSystem && val.id) {
      // 选择营销体系
      selectCustomer.value = customerInfo
      showChoiceSystemModal.value = true
    }
    customer_id.value = val.id
    emits('changeValue', val)
  }
  catch (error) {
  }
}

// 选择营销体系
function handleChoiceSystem(val: any) {
  const newCustomer = {
    ...selectCustomer.value,
    select_sale_system_id: val.id,
    select_sale_system_name: val.name,
  }
  showChoiceSystemModal.value = false
  emits('changeValue', newCustomer)
}

const router = useRouter()
function handleSelectCustomer() {
  router.push({
    name: 'CustomerAdd',
  })
}

defineExpose({
  customerRef,
})
</script>

<template>
  <SelectDialog
    ref="customerRef"
    v-model="customer_id"
    :disabled="disabled"
    :label-field="field"
    :table-config="tableConfig"
    api="GetCustomerEnumList"
    :query="getFilterData({
      ...query,
      ...(props.sale_system_id ? { sale_system_id: props.sale_system_id } : {}),
      name: componentRemoteSearch.customer_name,
    })"
    :is-push-default-data="isPushDefaultData"
    :column-list="[
      {
        title: '客户编号',
        minWidth: 90,
        required: true,
        colGroupHeader: true,
        childrenList: [
          {
            field: 'code',
            isEdit: true,
            title: '客户编号',
            minWidth: 90,
          },
        ],
      },
      {
        title: '客户名称',
        minWidth: 100,
        colGroupHeader: true,
        required: true,
        childrenList: [
          {
            isEdit: true,
            field: 'name',
            title: '客户名称',
            minWidth: 100,
          },
        ],
      },
      {
        title: '电话',
        colGroupHeader: true,
        minWidth: 80,
        childrenList: [
          {
            field: 'phone',
            isEdit: true,
            title: '电话',
            minWidth: 80,
          },
        ],
      },
      {
        title: '销售员',
        minWidth: 80,
        colGroupHeader: true,
        childrenList: [
          {
            field: 'seller_name',
            title: '销售员',
            soltName: 'seller_name',
            isEdit: true,
            minWidth: 80,
          },
        ],
      },
      {
        title: '营销体系',
        colGroupHeader: true,
        childrenList: [
          // 用于搜索字段-不显示出来
          {
            field: 'sale_system_name',
            show: false,
          },
          {
            field: 'sale_system_names',
            title: '营销体系',
            isEdit: true,
            minWidth: 200,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: matchDefaultValue ? {
          id: matchDefaultValue?.id,
          name: matchDefaultValue?.name,
          code: matchDefaultValue?.code,
        } : null,
        ...(props.isMerge ? { slots: {
          default: ({ row }:any) => {
            return [
              h('div', { class: 'flex align-center' }, [
                h('div', { style: { minWidth: '80px' } }, row.name),
                h('div', { style: { color: '#999', marginLeft: '10px' } }, row.code),
              ]),
            ]
          },
        } } : {}),
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInputCustomerName"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link v-has="'CustomerMange_add'" type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
  <!-- TODO: 这里如果需要放在单元格里面选择这需要处理不能挂载在单元格下  -->
  <SelectSaleSystemDialog v-if="props.showChoiceSystem" v-model="showChoiceSystemModal" :list="selectCustomer?.sale_system_list || []" :customer-id="selectCustomer?.id || 0" @handle-sure="handleChoiceSystem" />
</template>

<style scoped></style>
