/**
 * 处理客户的营销体系列表
 * @param customerInfo 客户信息
 * @returns
 */
export function handleCustomerMarketingList(customerInfo: any) {
  // 处理营销体系，id与名称对应
  const sale_system_names_arr = customerInfo.sale_system_names?.split(',') || []
  let newSaleSystemList = (customerInfo.sale_system_ids || [])?.map((e: any, i: number) => ({
    id: e,
    name: sale_system_names_arr[i],
  })) || []
  newSaleSystemList = newSaleSystemList.filter((e: any) => e.id) as any[]// 过滤掉空的营销体系
  customerInfo.sale_system_list = newSaleSystemList
  return customerInfo
}
