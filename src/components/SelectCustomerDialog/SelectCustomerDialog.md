# SelectCustomerDialog

## 简介

SelectCustomerDialog 是一个基于 SelectDialog 的子组件，用于选择客户信息。它可以根据客户编号或客户名称进行搜索，并显示客户的相关信息，如电话和销售员。
默认支持根据name或code自动输入搜索，数据联动请通过changeValue事件自行处理

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| customer_id | number \| null | null | 选中的客户的 id，可以使用 v-model 双向绑定 |
| sale_system_id | number \| null | null | 销售系统的 id，用于过滤客户列表 |
| defaultValue | object \| null | null | 默认的客户信息，包含 id 和 name 两个属性 |
| query | object | {} | 额外请求参数 |
| field | 'code' \| 'name' | 'name' | 用于显示的客户信息的字段，可以是客户编号或客户名称 |
| isPushDefaultData | boolean | false | 是否需要追加默认数据-分页获取不到的 |
| isMerge | boolean | false | 编号名称是否需要合并显示 |

## 事件

| 事件名                | 参数 | 说明 |
|--------------------| --- | --- |
| update:customer_id | val: number | 当选中的客户 id 发生变化时，触发该事件，同时更新 customer_id 的值 |
| changeValue        | val: number | 当选中的客户 id 发生变化时，触发该事件，可以用于自定义处理逻辑 |
| onInput            | val: string | 当输入的客户名称发生变化时，触发该事件，可以用于自定义处理逻辑 |
| showAddLink | boolean | true | 是否显示新增客户链接 |

## 示例

```vue
<script setup lang="ts">
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

defineOptions({
  name: 'Example',
})

const customer_id = ref<number | null>(null)
const sale_system_id = ref<number | null>(1)

function handleCustomerChange(val: number) {
  console.log('customer_id changed to', val)
}

function handleCustomerInput(val: string) {
  console.log('customer_name input is', val)
}
</script>

<template>
  <SelectCustomerDialog
    v-model="customer_id"
    :sale_system_id="sale_system_id"
    field="code"
    @change-value="handleCustomerChange"
    @on-input="handleCustomerInput"
  />
</template>
```
