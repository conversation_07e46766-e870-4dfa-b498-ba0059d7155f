<script setup lang="tsx">
import { computed, provide, ref, useSlots } from 'vue'
import { Setting } from '@element-plus/icons-vue'
// 定义字体大小梯度
type TitleSizeType = 'small' | 'default' | 'medium' | 'large' | 'xlarge'
interface FildProps {
  isCollapse?: boolean
  defaultOpen?: boolean
  title?: string
  tooTips?: boolean
  toolBar?: boolean
  columnList?: any // 传进来用来清楚筛选
  noShadow?: boolean
  titleSize?: TitleSizeType // 新增标题字体大小梯度属性
  titleFontSize?: string // 自定义字体大小，优先级高于titleSize
}

const props = withDefaults(defineProps<FildProps>(), {
  isCollapse: false,
  defaultOpen: false,
  columnList: [],
  title: '',
  tooTips: false,
  toolBar: true,
  titleSize: 'default',
})

const emit = defineEmits(['hadleRecycle', 'handField', 'update:columnList'])
const showField = ref<boolean>(false)
const xToolbar = ref<any>()
provide('toolbar', showField)

function handReset() {
  const arr: any = props.columnList
  arr?.map((item: any) => {
    item.searchFiledValue = ''
    return item
  })
  emit('update:columnList', arr)
}
// 根据titleSize计算实际的字体大小
const fontSize = computed(() => {
  // 如果提供了自定义字体大小，优先使用
  if (props.titleFontSize)
    return props.titleFontSize

  // 否则根据梯度返回对应的字体大小
  const sizeMap = {
    small: '12px',
    default: '16px',
    medium: '18px',
    large: '20px',
    xlarge: '24px',
  }

  return sizeMap[props.titleSize]
})
function handField() {
  showField.value = true
  emit('handField')
}
const activeNames = ref(props.defaultOpen ? 'title' : '')
defineExpose({
  xToolbar,
})
const slots = useSlots()
function Title() {
  return (
    <div class={`flex_title w-full  ${slots['right-top'] || props.toolBar || props.title || slots.title ? 'mb-[10px]' : ''}`}>
      <div class="font-[600] text-[#333] leading-none" style={{ fontSize: fontSize.value }}>
        { slots.title?.() || props.title || '' }
      </div>

      <div class="flex items-center">
        {slots['right-top']?.()}
        <slot name="right-top" />
        {
          props.columnList.length
            ? (
              <el-button v-btnAntiShake={handReset} type="primary">
                重置
              </el-button>
              )
            : null
        }
        {
          props.toolBar
            ? (
              <el-button text link type="info" icon={Setting} onClick={handField}>
                列表设置
              </el-button>
              )
            : null
        }
        {
          props.tooTips
            ? (
              <el-dropdown>
                <span class="el-dropdown-link">
                  <el-icon color="#a8bcd7" size={20} style="transform: rotate(90deg); margin-left: 20px"><MoreFilled /></el-icon>
                </span>
              </el-dropdown>
              )
            : null
        }

      </div>
    </div>
  )
}
</script>

<template>
  <el-collapse v-if="isCollapse" v-model="activeNames" class="fild_card" :class="[noShadow && 'no_shadow']">
    <el-collapse-item name="title">
      <template #title>
        <Title />
      </template>
      <slot />
    </el-collapse-item>
  </el-collapse>
  <div v-else class="fild_card" :class="[noShadow && 'no_shadow']">
    <Title />
    <slot />
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
::v-deep(.el-collapse-item__header) {
  outline: 0 !important;
  border-color: transparent !important;
}
::v-deep(.el-collapse-item__content) {
  padding-bottom: 0 !important;
}
.fild_card {
  padding: 5px 10px;
  //min-width: 900px;
  background: #f9f9fa;
  //box-shadow: 0px 0px 11px 0px rgba(8, 8, 8, 0.13);

  border-radius: 0px 4px 4px 4px;

  .flex_title {
    display: flex;
    align-items: center;
    justify-content: space-between;

  }
}

.no_shadow {
  box-shadow: none !important;
}
</style>
