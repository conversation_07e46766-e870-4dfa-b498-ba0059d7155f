<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'

defineOptions({
  name: 'SelectProductDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'finish_product_name',
  disabled: false,
  showAddLink: true,
  api: 'GetFinishProductDropdownList',
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  // (e: 'update:product_id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const modelValue = defineModel<number>('modelValue', { required: true })
interface Props {
  query?: any
  defaultValue?: {
    id: number
    finish_product_name?: string
    finish_product_code?: string
  }
  field?: 'finish_product_code' | 'finish_product_name'
  api?: keyof typeof selectApi | ''
  showAddLink?: boolean
  isPushDefaultData?: boolean
  disabled?: boolean
  labelName?: string
}
const fieldName = {
  finish_product_name: '成品名称',
  finish_product_code: '成品编号',
}

const productRef = ref()

const componentRemoteSearch = reactive({
  finish_product_name: '',
  finish_product_code: '',
})
function handleInputCustomerName(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  emits('changeValue', val)
  modelValue.value = val.id
  // emits('update:product_id', val.id)
}
const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})

defineExpose({
  productRef,
})
const router = useRouter()
function handleSelectCustomer() {
  router.push({
    name: 'FinishedProductInformationAdd',
  })
}
</script>

<template>
  <SelectDialog
    ref="productRef"
    v-model="modelValue"
    :label-name="labelName"
    :label-field="field"
    :api="api"
    :disabled="disabled"
    :table-config="tableConfig"
    :is-push-default-data="isPushDefaultData"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :column-list="[
      {
        field: 'finish_product_code',
        colGroupHeader: true,
        title: '成品编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'finish_product_code',
            title: '成品编号',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'finish_product_name',
        colGroupHeader: true,
        title: '成品名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'finish_product_name',
            title: '成品名称',
            minWidth: 100,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: defaultValue ? {
          id: defaultValue?.id,
          finish_product_name: defaultValue?.finish_product_name,
          finish_product_code: defaultValue?.finish_product_code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInputCustomerName"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
</template>

<style scoped></style>
