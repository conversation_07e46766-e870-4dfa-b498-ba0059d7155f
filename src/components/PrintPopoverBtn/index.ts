import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import PrintPopoverBtn from './index.vue'
import { GetPrintTemplateList } from '@/api/print'
import { getDataHandler } from '@/components/PrintBtn/formatData'
import type { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'

interface Props {
  api?: string
  printType: PrintType
  list?: any[]
  id?: number
  query?: Record<string, any>
  dataType: PrintDataType
}

export function usePrintTemplate({
  api,
  printType,
  list,
  id,
  query,
  dataType,
}: Props) {
  const {
    fetchData: getPrintTemplateListApi,
    data,
    msg,
    success,
  } = GetPrintTemplateList()
  const printList = ref([])

  const getData = async () => {
    await getPrintTemplateListApi({
      type: printType,
    })
    if (success.value) {
      printList.value = data.value.list
        .filter((item: any) => item.status === 1 && item.data_type === dataType)
        .map((item: any) => {
          return {
            btnText: item.order_name,
            api,
            tid: item.id,
            type:
              getDataHandler(printType)?.find(
                (it: any) => it.id === item.front_handler,
              )?.code || null,
            list,
            id,
            query,
          }
        })
    }
    else {
      return ElMessage.error(msg.value)
    }
  }

  onMounted(() => {
    getData()
  })

  return {
    options: printList,
  }
}

export default PrintPopoverBtn
