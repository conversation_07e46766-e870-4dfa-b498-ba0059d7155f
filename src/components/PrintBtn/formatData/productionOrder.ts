import { formatDate, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

export default (data: any) => {
  data.production_notify_material_ratio = data?.production_notify_material_ratio?.map((item: any) => {
    return {
      ...item,
      yarn_ratio: formatTwoDecimalsDiv(item.yarn_ratio || 0),
      yarn_loss: formatTwoDecimalsDiv(item.yarn_loss || 0),
      use_yarn_quantity: formatWeightDiv(item.use_yarn_quantity || 0),
    }
  })
  const weaving_specifications_str = data.weaving_specifications?.map((val: any) => {
    return val.weaving_specifications_name
  })
  const data2 = {
    ...data,
    weaving_mill_name_title: data.weaving_mill_name + '合同单',
    scheduling_weight: formatWeightDiv(data.scheduling_weight) + 'Kg',
    weight_of_fabric: formatWeightDiv(data.weight_of_fabric) + 'Kg',
    process_price: formatUnitPriceDiv(data.process_price) + '元/KG',
    receipt_grey_fabric_date: formatDate(data.receipt_grey_fabric_date),
    weaving_specifications_str: weaving_specifications_str.join(','),
    sale_plan_order_no: data.production_notify_grey_fabric_detail?.[0]?.sale_plan_order_no,
  }
  return data2
}
