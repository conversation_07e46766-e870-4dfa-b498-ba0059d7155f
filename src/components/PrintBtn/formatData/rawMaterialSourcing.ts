import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { dealBigMoney } from '@/util/printFn'
export default (data: any) => {
  let totalWeight = 0
  let totalPrice = 0
  const tables = data?.items?.map((item: any, index: number) => {
    totalWeight += formatWeightDiv(item.total_weight)
    totalWeight = Number(totalWeight.toFixed(2))
    totalPrice += formatPriceDiv(item.total_price || 0)
    totalPrice = Number(totalPrice.toFixed(2))
    return {
      ...item,
      index: index + 1, // 序号
      raw_material_name: item.raw_material_name, // 产品名称
      ingredient: item.ingredient, // 成分比例
      unit_name: item.unit_name, // 单位
      total_weight: formatWeightDiv(item.total_weight), // 数量
      // 上下限（%） 暂时不用管
      unit_price: formatUnitPriceDiv(item.unit_price), // 单价
      total_price: formatPriceDiv(item.total_price), // 金额
      remark: item.remark, // 备注
    }
  })
  return {
    sale_system_name: data?.sale_system_name, // 公司名
    sale_system_addr: `${data.sale_system_addr} 电话:${data.sale_system_phone}`, // 地址   后端给
    supplier_name: data.supplier_name, // 乙方
    order_no: data.order_num, // 合同编号
    pay_method: `微信/短信`, // 下单方式 写死
    purchase_date: formatDate(data.purchase_date), // 订单日期
    receipt_date: formatDate(data.receipt_date), // 交货日期
    // customer: `张三 13265326554`, // 需方联系人、电话 写死
    customer: `${data?.sale_system_contacts} / ${data?.sale_system_phone}`,
    supplier: `${data.supplier_contact} ${data.supplier_contact_phone}`, // 供方联系人、电话  后端给
    tables, // 表格
    totalPrice, // 合计金额
    totalWeight, // 合计数量
    totalPriceText: dealBigMoney(totalPrice), // 合计金额大写
    remark: data.remark, // 备注
  }
}
