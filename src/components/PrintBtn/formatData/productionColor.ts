import { joinValues } from 'arcdash'

export default (data: any) => {
  return data?.map((item: any) => {
    return {
      ...item,
      product_color_code_and_color_name: `${item.product_color_code}#-${item.product_color_name}`,
      finish_product_code: item.finish_product_code,
      product_color_code: item.product_color_code,
      product_code_and_color_code: `${item.finish_product_code}-${item.product_color_code}`,
      product_color_name: item?.product_color_name,
      F_product_gram_weight_and_unit_name_A: joinValues([item?.product_gram_weight, item?.finish_product_gram_weight_unit_name]), // 成品克重及单位名称
      F_product_width_and_unit_name_A: joinValues([item?.product_width, item?.finish_product_width_unit_name]), // 成品幅宽及单位名称
      F_product_gram_weight_and_unit_name_B: joinValues([item?.finish_product_gram_weight, item?.finish_product_gram_weight_unit_name]), // 成品克重及单位名称
      F_product_width_and_unit_name_B: joinValues([item?.finish_product_width, item?.finish_product_width_unit_name]), // 成品幅宽及单位名称
    }
  })
}
