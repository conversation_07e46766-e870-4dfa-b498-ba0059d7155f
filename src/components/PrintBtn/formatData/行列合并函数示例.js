// 表格数据： [1,1] 代表 rowspan,colspan
// [1,1] [1,1] [1,1] [1,1]
//   [1,1] [1,1] [1,1] [1,1]
//   [1,1] [1,1] [1,1] [1,1]
//   [1,1] [1,1] [1,1] [1,1]
// 如果要合并 每 2 行 的 第 1 列
// 那么要就需要变成
//   [2,1] [1,1] [1,1] [1,1]
//   [0,1] [1,1] [1,1] [1,1]
//   [2,1] [1,1] [1,1] [1,1]
//   [0,1] [1,1] [1,1] [1,1]
// 如果要合并 每行 的 前 2 列
// 那么要就需要变成
//   [1,2] [1,0] [1,1] [1,1]
//   [1,2] [1,0] [1,1] [1,1]
//   [1,2] [1,0] [1,1] [1,1]
//   [1,2] [1,0] [1,1] [1,1]

function _销售送货单模板3(data, col, colIndex, rowIndex, tableData, _printData) {
  if (colIndex % 3 !== 0)
    return [1, 1]

  // 枚举列颜色后缀
  const suffixes = ['A', 'B', 'C', 'D', 'E']
  const computeSuffixes = (index) => {
    return suffixes[Math.ceil(index / 3)]
  }
  const getColorName = (rowIndex, colIndex) => {
    return tableData[rowIndex]?.[`color_${computeSuffixes(colIndex)}`]
  }
  const thisRowColorName = getColorName(rowIndex, colIndex)
  if (rowIndex !== 0) {
    // 不是第一行，看看和上一行的颜色是否一样
    const lastRowColorName = getColorName(rowIndex - 1, colIndex)
    if (thisRowColorName === lastRowColorName) {
      // 一样的话就返回当前为被合并的
      return [0, 1]
    }
  }
  // 如果下一个马上就不一样，那么就返回当前是单独的
  const nextRowColorName = getColorName(rowIndex + 1, colIndex)
  if (thisRowColorName !== nextRowColorName)
    return [1, 1]

  // 查看和后面n行的颜色是否一样
  let count = 1
  for (let i = 1; i < tableData.length; i++) {
    const nextRowColorName = getColorName(rowIndex + i, colIndex)
    if (thisRowColorName === nextRowColorName) {
      count++
      // 如果到了最后一行，就该返回了，是什么无所谓，反正是正确答案
      if (rowIndex + i === tableData.length - 1)
        return [count, 1]
    }
    if (thisRowColorName !== nextRowColorName)
      return [count, 1]
  }
}
