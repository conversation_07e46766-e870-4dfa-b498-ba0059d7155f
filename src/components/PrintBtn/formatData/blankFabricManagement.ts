import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { dealBigMoney, removeSlash } from '@/util/printFn'

export default (data: any) => {
  let totalWeight = 0
  let totalPrice = 0
  const tables = data?.item_data?.map((item: any, index: number) => {
    totalWeight += formatWeightDiv(item.total_weight)
    totalWeight = Number(totalWeight.toFixed(2))
    totalPrice += formatPriceDiv(item.total_price) || 0
    totalPrice = Number(totalPrice.toFixed(2))

    const yarn_count_density = removeSlash(`${item.yarn_count}/${item.density}`) // 纱支密度
    const yarn_length_density = removeSlash(
      `${item.yarn_length}/${item.density}`,
    ) // 纱长密度
    const needle_size_density = removeSlash(
      `${item.needle_size}/${item.density}`,
    ) // 针寸数/密度

    return {
      ...item,
      index: index + 1, // 序号
      code: item.code, // 产品编号
      name: item.name, // 产品名称
      ingredient: item.ingredient, // 成分比例
      yarn_count_density,
      yarn_length_density,
      needle_size_density,
      grey_fabric_width: `${item.grey_fabric_width}${item.grey_fabric_width_unit_name}`, // 胚布门幅
      grey_fabric_gram_weight: `${item.grey_fabric_gram_weight}${item.grey_fabric_gram_weight_unit_name}`, // 胚布克重
      unit_name: item.unit_name, // 单位
      total_weight: formatWeightDiv(item.total_weight), // 胚布数量
      single_price: formatUnitPriceDiv(item.single_price), // 单价
      total_price: formatPriceDiv(item.total_price), // 金额
      remark: item.remark, // 备注
    }
  })
  return {
    ...data,
    sale_system_name: data?.sale_system_name, // 公司名
    sale_system_address: data.sale_system_addr,
    sale_system_addr: `${data.sale_system_addr} 电话:${data.sale_system_phone}`, // 地址   后端给
    supplier_name: data.supplier_name, // 乙方
    order_no: data.order_code, // 合同编号
    purchase_date: formatDate(data.purchase_time), // 订单日期
    receipt_date: formatDate(data.receiving_time), // 交货日期
    // customer: `张三 13265326554`, // 需方联系人、电话 写死
    customer: `${data?.sale_system_contacts} / ${data?.sale_system_phone}`,
    supplier: `${data.supplier_contact} ${data.supplier_contact_phone}`, // 供方联系人、电话  后端给
    tables, // 表格
    totalPrice, // 合计金额
    totalWeight, // 合计数量
    totalPriceText: dealBigMoney(totalPrice), // 合计金额大写
    remark: data.remark, // 备注
    include_tax: data.include_tax ? '含税' : '不含税', // 是否含税
  }
}
