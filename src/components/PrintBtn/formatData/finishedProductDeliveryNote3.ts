import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  number2text,
} from '@/common/format'

const prefix = ['color_', 'volume_number_', 'actually_weight_']
function computePrefix(index: number) {
  return prefix[index % 3]
}
// 0~14
const suffixes = ['A', 'B', 'C', 'D', 'E']
function computeSuffixes(index: number) {
  return suffixes[Math.ceil(index / 3)]
}

// 根据规格提取数据
function coverData(data: any): any[] | boolean {
  const datas: any = []
  data.items.map((item: any) => {
    // 通过成品编码判断是否为同一个成品
    const only = item?.product_code + item?.sale_price

    // 如果 fc_data_list 长度为 0，则跳过这条数据
    if (!item?.fc_data_list?.length)
      return // 跳过此项

    // 对比规格，相同的就push进去，否则就是新的规格，需要存到新项
    let isSave = false
    datas?.forEach((v: any) => {
      const itemOnly = v?.product_code + v?.sale_price

      if (only === itemOnly) {
        isSave = true
        // 如果item里没有fc_data_list，提示错误，并return false
        if (!item?.fc_data_list?.length || !v?.fc_data_list?.length) {
          // ElMessage.error('有细码未录入')
          return false
        }
        v?.fc_data_list.push(...item?.fc_data_list)
      }
    })
    if (!isSave)
      datas.push(item)
  })
  return datas
}

function sortData(data: any) {
  // 如果没有fc_data_list，则直接返回“该单据不支持打印送货单”
  if (!data?.[0].fc_data_list) {
    ElMessage.error('该单据不支持打印送货单')
    return false
  }
  // 复制一份data，避免修改原数组
  const newData = [...data]
  // 使用sort方法，根据自定义的比较函数进行排序
  newData.sort((a, b) => {
    // 如果a的fc_data_list的长度大于b的，那么a排在前面，返回负数
    if (a.fc_data_list.length > b.fc_data_list.length)
      return -1

    // 如果a的fc_data_list的长度小于b的，那么a排在后面，返回正数
    if (a.fc_data_list.length < b.fc_data_list.length)
      return 1

    // 如果a和b的fc_data_list的长度相等，那么不改变它们的顺序，返回0
    return 0
  })
  // 对每个data的fc_data_list内部进行排序，volume_number小的排前面
  for (const item of newData) {
    item.fc_data_list.sort((x: any, y: any) => {
      // 如果x的volume_number小于y的，那么x排在前面，返回负数
      if (x.volume_number < y.volume_number)
        return -1

      // 如果x的volume_number大于y的，那么x排在后面，返回正数
      if (x.volume_number > y.volume_number)
        return 1

      // 如果x和y的volume_number相等，那么不改变它们的顺序，返回0
      return 0
    })
  }
  // 返回排序后的新数组
  return newData
}

function sortDataByColorName(data: any) {
  // 复制一份data，避免修改原数组
  const newData = [...data]
  newData.forEach((item: any) => {
    item?.fc_data_list.sort((a: any, b: any) => {
      if (a.product_color_code > b.product_color_code)
        return -1

      // 如果a的fc_data_list的长度小于b的，那么a排在后面，返回正数
      if (a.product_color_code < b.product_color_code)
        return 1

      // 如果a和b的fc_data_list的长度相等，那么不改变它们的顺序，返回0
      return 0
    })
  })
  // 返回排序后的新数组
  return newData
}

function initRow() {
  return {
    color_A: '',
    color_B: '',
    color_C: '',
    color_D: '',
    color_E: '',
    volume_number_A: '',
    volume_number_B: '',
    volume_number_C: '',
    volume_number_D: '',
    volume_number_E: '',
    actually_weight_A: '',
    actually_weight_B: '',
    actually_weight_C: '',
    actually_weight_D: '',
    actually_weight_E: '',
  }
}

function getOrderInfo(data: any, tables: any, specification: string, item: any, allMoney: number, allRoll: number, allWeight: number, allErrorWeight: number) {
  const sale_price = formatUnitPriceDiv(item?.sale_price) || 0
  allMoney = currency(sale_price).multiply(allWeight - allErrorWeight).value
  return {
    ...data,
    // 营销体系名称
    sale_system_name: `${data?.sale_system_name}（送货单）`,
    // 营销体系地址
    sale_system_addr: data?.sale_system_addr,
    // 营销体系电话和传真
    sale_system_phone: data?.sale_system_phone,
    // 送货日期
    order_time: formatDate(data?.order_time),
    // 客户名称
    customer_name: data?.customer_name,
    // 规格
    specification,
    // 表格数据
    tables,
    // 仓库发货人
    contacts: data?.auditor_name,
    // 总计
    // roll: item?.roll, //匹数
    roll: formatPriceDiv(item?.roll), // 匹数
    // weight: formatWeightDiv(item?.weight), // 数量
    weight: formatWeightDiv(item?.weight), // 数量
    // sale_price: formatWeightDiv(item?.sale_price), // 单价
    // settle_price: formatPriceDiv(item?.settle_price),
    sale_price: sale_price || '0', // 单价
    settle_price: item?.settle_price,
    product_name: item?.product_name,
    allRoll,
    allWeight,
    allErrorWeight,
    allSettleWeight: allWeight - allErrorWeight,
    allMoney: sale_price ? allMoney : '0',
    chineseMoney: sale_price ? number2text(allMoney) : '零元整',
  }
}

function confirmData(data: any, table: any, item: any, allMoney: number, allRoll: number, allWeight: number, allErrorWeight: number) {
  const specification = item?.product_code
  const salePrice = formatUnitPriceDiv(item?.sale_price)
  allMoney = currency(allMoney).multiply(salePrice).value as number
  return getOrderInfo(
    data,
    table,
    specification,
    item,
    allMoney,
    allRoll,
    allWeight,
    allErrorWeight,
  )
}

/**
 * 合并颜色信息
 */
function mergeColorInfo(table: any[]): string[] {
  const colorMap: Map<string, any> = new Map()

  table.forEach((item: any) => {
    for (let i = 0; i < 15; i += 3) {
      const prefixA = computePrefix(i) + computeSuffixes(i)
      const prefixC = computePrefix(i + 2) + computeSuffixes(i)

      if (item?.[prefixA] === '')
        return
      const colorName = item?.[prefixA]
      if (colorMap.has(colorName)) {
        // 如果map中已经有该颜色了，就加数量
        const prev = colorMap.get(colorName)
        colorMap.delete(colorName)
        colorMap.set(colorName, {
          volume_number: Number(prev.volume_number) + 1,
          actually_weight: Number(prev.actually_weight) + Number(item?.[prefixC]),
        })
        continue
      }
      // 否则新增这个key
      colorMap.set(colorName, {
        volume_number: 1,
        actually_weight: Number(item?.[prefixC]),
      })
    }
  })

  table.forEach((item: any) => {
    for (let i = 0; i < 15; i += 3) {
      const prefixA = computePrefix(i) + computeSuffixes(i)
      if (item?.[prefixA] === '')
        return
      const colorName = item?.[prefixA]
      const active = colorMap.get(colorName)
      // 只有一条的情况，本身就是总计了，不需要再显示
      if (active.volume_number === 1)
        continue
      item[prefixA] = `<div style="font-size:8px">${colorName}<br/>总匹数:${active?.volume_number}<br/>总数量:${active?.actually_weight}</div>`
    }
  })

  return table
}

export default (data: any, needMergeColorInfo: boolean = false) => {
  if (!data?.items)
    return ElMessage.error('数据错误')

  const allData: any[] = []

  let datas = coverData(data)
  if (datas === false)
    return

  datas = sortData(datas)

  datas = sortDataByColorName(datas)

  if (!datas)
    return

  datas?.forEach((item: any) => {
    let tableData: any = []
    let allMoney = 0
    let allRoll = 0
    let allWeight = 0
    let allErrorWeight = 0
    while (true) {
      for (let i = 0; i < 10; i++)
        tableData.push(initRow())

      for (let i = 0; i < 15; i += 3) {
        const prefixA = computePrefix(i) + computeSuffixes(i)
        const prefixB = computePrefix(i + 1) + computeSuffixes(i)
        const prefixC = computePrefix(i + 2) + computeSuffixes(i)
        // 用来辅助计算数量
        const perfixD = `all_${computePrefix(i + 2)}${computeSuffixes(i)}`
        for (let j = 0; j < 10; j++) {
          // 如果数组长度没了，说明全部填充完毕
          if (item?.fc_data_list <= 0) {
            allData.push(
              confirmData(
                data,
                tableData,
                item,
                allMoney,
                allRoll,
                allWeight,
                allErrorWeight,
              ),
            )
            allMoney = 0
            allRoll = 0
            allWeight = 0
            allErrorWeight = 0
            return
          }
          const thisValue = item?.fc_data_list[0]

          // 如果当前行不为0且和上一行的颜色不同，则跳出当前列循环
          if (j !== 0 && tableData[j - 1][prefixA] !== '') {
            if (tableData[j - 1][prefixA] !== `${thisValue?.product_color_code}#${thisValue?.product_color_name}`)
              break
          }

          // 颜色
          tableData[j][prefixA]
            = `${thisValue?.product_color_code}#${thisValue?.product_color_name}`
            || ''
          // tableData[j][prefixA] = item?.fc_data_list[0]?.color || ''
          tableData[j][prefixB] = thisValue?.volume_number || ''

          // 实际数量 = 实际数量 - 结算误差数量
          const settle_error_weight = formatWeightDiv(
            thisValue?.settle_error_weight,
          )
          if (settle_error_weight === 0) {
            tableData[j][prefixC]
              = `${formatWeightDiv(thisValue?.actually_weight)}`
          }
          else {
            tableData[j][prefixC]
              = `${formatWeightDiv(thisValue?.actually_weight)}(-${formatWeightDiv(thisValue?.settle_error_weight)})`
          }
          tableData[j][perfixD]
            = formatWeightDiv(thisValue?.actually_weight)
            - formatWeightDiv(thisValue?.settle_error_weight)

          // allRoll只记次数，不记数量，多少数量都是一卷
          allRoll++
          allWeight = currency(
            formatWeightDiv(thisValue?.actually_weight) || 0,
          ).add(allWeight).value
          allErrorWeight = currency(
            formatWeightDiv(thisValue?.settle_error_weight) || 0,
          ).add(allErrorWeight).value
          item?.fc_data_list.splice(0, 1)
        }
      }

      // if (item?.fc_data_list.length <= 0)
      //   break

      // 到50次了，应该加一个表了
      allData.push(
        confirmData(
          data,
          tableData,
          item,
          allMoney,
          allRoll,
          allWeight,
          allErrorWeight,
        ),
      )
      tableData = []
      allMoney = 0
      allRoll = 0
      allWeight = 0

      if (item?.fc_data_list.length <= 0)
        break
    }
  })

  if (needMergeColorInfo) {
    allData?.forEach((item: any) => {
      item.tables = mergeColorInfo(item.tables)
    })
  }
  return allData
}
