import { processDataOut } from '@/common/handBinary'
import { formatDate } from '@/common/format'

function convertIndexToLetter(index: number): string {
  return String.fromCharCode(64 + index) // ASCII value of A is 65, B is 66, and so on.
}

export default (data: any) => {
  // 处理一下进制
  data = processDataOut(data)
  const allData: any[] = []
  // 拆出里面的原料，每个表一张单子

  data?.item_data?.map((item: any) => {
    const tables: any = []

    // 品种
    const assortment = `${item?.raw_material_code}${item?.raw_material_name}`

    item?.item_data?.map((value: any, index: number) => {
      tables.push({
        title: '原料组合',
        index: convertIndexToLetter(index + 1),
        yarn_count: value.yarn_count,
        raw_material_ingredient: value.raw_material_ingredient,
        yarn_ratio: value.yarn_ratio,
      })
    })

    allData.push({
      ...data,
      order_remark: data.remark,
      ...item,
      tables,
      assortment,
      // delivery_date: formatDate(item.delivery_date),
      dye_unit_follower_name_and_phone: `${data.dye_unit_follower_name}-${data.dye_unit_follower_phone}`,
    })
  })

  return allData
}
