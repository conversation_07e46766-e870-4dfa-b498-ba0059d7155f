import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import { every, isEmpty } from 'lodash-es'
import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv, number2text } from '@/common/format'

// 类型定义
interface DeliveryItem {
  product_code: string
  product_color_code: string
  product_color_name: string
  fc_data_list: Array<{
    dye_factory_dyelot_number: string // 缸号
    volume_number: number // 匹数
    actually_weight: number // 实际重量
    settle_error_weight: number // 结算误差重量
  }>
}

interface DeliveryData {
  items: DeliveryItem[]
  sale_system_name: string // 营销体系名称
  sale_system_addr: string // 营销体系地址
  sale_system_phone: string // 营销体系电话
  order_time: string // 订单时间
  customer_name: string // 客户名称
  auditor_name: string // 审核人名称
}

// 表格列前缀定义：用于生成如 dye_factory_dyelot_number_A 的字段名
const COLUMN_PREFIXES = ['dye_factory_dyelot_number_', 'volume_number_', 'actually_weight_'] as const
const COLUMN_SUFFIXES = ['A', 'B', 'C', 'D', 'E'] as const

/**
 * 计算字段前缀
 * @param index 列索引
 * @returns 对应的字段前缀
 */
function computePrefix(index: number): string {
  return COLUMN_PREFIXES[index % 3]
}

/**
 * 计算字段后缀
 * @param index 列索引
 * @returns 对应的字段后缀（A-E）
 */
function computeSuffixes(index: number): string {
  return COLUMN_SUFFIXES[Math.floor(index / 3)]
}

/**
 * 合并相同规格的数据
 * @param data 原始数据
 * @returns 合并后的数据数组或false（数据无效）
 */
function mergeSpecificationData(data: DeliveryData): DeliveryItem[] | false {
  const mergedData: DeliveryItem[] = []

  data.items.forEach((item) => {
    // 生成规格标识
    const specification = `${item.product_code}-${item.product_color_code}/${item.product_color_name}`

    // 跳过无效数据
    if (!item.fc_data_list?.length)
      return

    // 查找并合并相同规格的数据
    const existingItem = mergedData.find(v =>
      `${v.product_code}-${v.product_color_code}/${v.product_color_name}` === specification,
    )

    if (existingItem)
      existingItem.fc_data_list.push(...item.fc_data_list)
    else
      mergedData.push(item)
  })

  return mergedData
}

/**
 * 对数据进行排序
 * @param data 待排序的数据
 * @returns 排序后的数据或false（数据无效）
 */
function sortData(data: DeliveryItem[]): DeliveryItem[] | false {
  if (!data?.[0]?.fc_data_list) {
    ElMessage.error('该单据不支持打印送货单')
    return false
  }

  const sortedData = [...data]

  // 处理每个规格的数据
  for (const item of sortedData) {
    // 按缸号分组
    const groupByDyelot = item.fc_data_list.reduce((acc, curr) => {
      const dyelotNumber = curr.dye_factory_dyelot_number
      if (!acc[dyelotNumber])
        acc[dyelotNumber] = []
      acc[dyelotNumber].push(curr)
      return acc
    }, {} as Record<string, typeof item.fc_data_list>)

    // 对每组内的数据按匹号排序
    Object.values(groupByDyelot).forEach((group) => {
      group.sort((a, b) => a.volume_number - b.volume_number)
    })

    // 重新展平数据，保持缸号连续
    item.fc_data_list = Object.values(groupByDyelot).flat()
  }

  // 按数据量排序
  sortedData.sort((a, b) => b.fc_data_list.length - a.fc_data_list.length)

  return sortedData
}

/**
 * 初始化表格行数据
 * @returns 包含所有列的空行数据
 */
function initRow(): Record<string, string> {
  const row: Record<string, string> = {}
  COLUMN_SUFFIXES.forEach((suffix) => {
    COLUMN_PREFIXES.forEach((prefix) => {
      row[prefix + suffix] = ''
    })
  })
  return row
}

/**
 * 获取订单信息
 * @param data 原始数据
 * @param tables 表格数据
 * @param specification 规格信息
 * @param item 当前项数据
 * @param allMoney 总金额
 * @param allRoll 总卷数
 * @param allWeight 总重量
 */
function getOrderInfo(data: DeliveryData, tables: any[], specification: string, item: any, allMoney: number, allRoll: number, allWeight: number) {
  return {
    ...data,
    sale_system_name: data.sale_system_name,
    sale_system_addr: data.sale_system_addr,
    sale_system_phone: data.sale_system_phone,
    order_time: formatDate(data.order_time),
    customer_name: data.customer_name,
    specification,
    tables: tables.filter(e => !every(e, isEmpty)), // 清除空行-不然导致多出空白页,
    contacts: data.auditor_name,
    roll: formatPriceDiv(item.roll),
    weight: formatWeightDiv(item.weight),
    sale_price: formatUnitPriceDiv(item.sale_price),
    settle_price: item.settle_price,
    product_name: item.product_name,
    allRoll,
    allWeight,
    allMoney,
    chineseMoney: number2text(allMoney),
  }
}

/**
 * 确认并格式化数据
 */
function confirmData(data: DeliveryData, table: any[], item: any, allMoney: number, allRoll: number, allWeight: number) {
  const specification = `${item.product_code}-${item.product_color_code}/${item.product_color_name}`
  const salePrice = formatUnitPriceDiv(item.sale_price)
  allMoney = currency(allMoney).multiply(salePrice).value
  return getOrderInfo(data, table, specification, item, allMoney, allRoll, allWeight)
}

/**
 * 主函数：处理送货单数据
 * @param data 原始数据
 * @returns 处理后的数据数组
 */
export default function processDeliveryNote(data: DeliveryData) {
  // 1. 数据校验
  if (!data?.items) {
    ElMessage.error('数据错误')
    return
  }

  // 2. 数据预处理
  const allData: any[] = []
  let processedData = mergeSpecificationData(data)
  if (processedData === false)
    return

  processedData = sortData(processedData)
  if (!processedData)
    return

  // 3. 处理每个规格的数据
  processedData.forEach((item) => {
    let tableData: any[] = []
    let allMoney = 0
    let allRoll = 0
    let allWeight = 0

    // 循环处理直到该规格的所有数据处理完
    while (true) {
      // 3.1 初始化表格（10行）
      for (let i = 0; i < 10; i++)
        tableData.push(initRow())

      // 3.2 填充数据（5组x3列）
      for (let i = 0; i < 15; i += 3) {
        const prefixA = computePrefix(i) + computeSuffixes(i) // 缸号列
        const prefixB = computePrefix(i + 1) + computeSuffixes(i) // 匹数列
        const prefixC = computePrefix(i + 2) + computeSuffixes(i) // 重量列

        // 处理每行数据
        for (let j = 0; j < 10; j++) {
          // 数据处理完成则保存当前表格
          if (item.fc_data_list.length <= 0) {
            allData.push(confirmData(data, tableData, item, allMoney, allRoll, allWeight))
            return
          }

          // 填充数据
          const currentItem = item.fc_data_list[0]
          tableData[j][prefixA] = currentItem?.dye_factory_dyelot_number || ''
          tableData[j][prefixB] = currentItem?.volume_number || ''

          // 计算实际重量（减去结算误差）
          const actuallyWeight = formatWeightDiv(
            currency(currentItem?.actually_weight || 0)
              .subtract(currentItem?.settle_error_weight || 0)
              .value,
          )
          tableData[j][prefixC] = actuallyWeight

          // 更新统计数据
          allRoll++
          allWeight = currency(actuallyWeight || 0).add(allWeight).value
          allMoney = currency(actuallyWeight || 0).add(allMoney).value

          // 移除已处理的数据
          item.fc_data_list.splice(0, 1)
        }
      }

      // 3.3 检查是否还有未处理的数据
      if (item.fc_data_list.length <= 0) {
        allData.push(confirmData(data, tableData, item, allMoney, allRoll, allWeight))
        break
      }

      // 3.4 保存当前表格，准备处理下一页
      allData.push(confirmData(data, tableData, item, allMoney, allRoll, allWeight))
      tableData = []
      allMoney = 0
      allRoll = 0
      allWeight = 0
    }
  })

  return allData
}
