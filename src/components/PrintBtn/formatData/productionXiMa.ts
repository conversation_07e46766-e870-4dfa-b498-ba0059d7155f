import { formatDate, formatHashTag, formatWeightDiv } from '@/common/format'

const productionXiMa = (data: any) => {
  return data?.map((item: any) => {
    return {
      ...item,
      finish_product_craft: item.finish_product_craft, // 纺纱工艺
      density: item.density, // 密度
      product_kind_name: item.product_kind_name, // 布种类型
      bleach_name: item.bleach_name, // 漂染性
      finish_product_width: item.finish_product_width, // 幅宽
      finish_product_gram_weight: item.finish_product_gram_weight,
      weaving_organization_name: item.weaving_organization_name, // 组织
      product_code: item.product_code,
      product_code_and_color_code: `${item.product_code}-${item?.product_color_code}#`,
      product_name: item.product_name,
      yarn_count: item.yarn_count, // 纱织
      finish_product_ingredient: item.finish_product_ingredient, // 成分
      dyelot_number: item.dyelot_number || '',
      weight: `${formatWeightDiv(item.weight) || 0}${item.measurement_unit_name}`,
      product_color_name: item?.product_color_name,
      product_color_code: item?.product_color_code,
      product_color_code_and_name: formatHashTag(item?.product_color_code, item?.product_color_name),
      qr_code: item.qr_code, // 二维码
      bar_code: item.bar_code, // 条形码
      volume_number: item.volume_number, // 匹号
      print_date: formatDate(item.print_date), // 打印时间
    }
  })
}

export default productionXiMa
