import { isEmpty } from 'arcdash'
import { processDataOut } from '@/common/handBinary'

export default (data: any) => {
  data = processDataOut(data)
  const allData: any[] = []

  data.forEach((item: any) => {
    if (isEmpty(item?.item_fc_data)) {
      allData.push({
        ...item,
        FtotalWeight: item?.result_weight + item?.measurement_unit_name,
      })
      return
    }
    item?.item_fc_data?.map((i: any) => {
      allData.push({
        ...item,
        volume_number: i.volume_number,
        FtotalWeight: i?.weight + item?.measurement_unit_name,
      })
    })
  })

  return allData
}
