import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import { every, isEmpty } from 'lodash-es'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  number2text,
} from '@/common/format'

interface DyeLotItem {
  dye_factory_dyelot_number: string
  volume_number: number
  actually_weight: number
  settle_error_weight: number
  paper_tube_weight?: number
}

interface DeliveryItem {
  product_code: string
  product_color_code: string
  product_color_name: string
  fc_data_list: DyeLotItem[]
  sale_price?: number
  other_price?: number
  roll?: number
  weight?: number
  uniq?: string
}

interface DeliveryData {
  items: DeliveryItem[]
  sale_system_name: string
  sale_system_addr: string
  sale_system_phone: string
  order_time: string
  customer_name: string
  auditor_name: string
}

const COLUMN_PREFIXES = ['dye_factory_dyelot_number_', 'volume_number_', 'actually_weight_'] as const
const COLUMN_SUFFIXES = ['A', 'B', 'C', 'D', 'E'] as const
const MAX_ROWS = 10 // 自动生成表格时的最大行数
const MAX_COLUMNS = 15

function computePrefix(index: number) {
  return COLUMN_PREFIXES[index % 3]
}

function computeSuffixes(index: number) {
  return COLUMN_SUFFIXES[Math.floor(index / 3)]
}

// 处理表格数据显示-一页50个
function processTableData(item: DeliveryItem, tableData: any[], stats: {
  allMoney: number
  allRoll: number
  allWeight: number
  allPaperTubeWeight: number
  allErrorWeight: number
}) {
  for (let i = 0; i < MAX_COLUMNS; i += 3) {
    const prefixA = computePrefix(i) + computeSuffixes(i)
    const prefixB = computePrefix(i + 1) + computeSuffixes(i)
    const prefixC = computePrefix(i + 2) + computeSuffixes(i)

    for (let j = 0; j < MAX_ROWS; j++) {
      const currentItem = item.fc_data_list[0]
      const settleErrorWeight = formatWeightDiv(currentItem?.settle_error_weight)
      const actuallyWeight = formatWeightDiv(currentItem?.actually_weight)

      // 填充表格数据
      tableData[j][prefixA] = currentItem?.dye_factory_dyelot_number || ''
      tableData[j][prefixB] = currentItem?.volume_number || ''
      tableData[j][prefixC] = settleErrorWeight === 0
        ? `${actuallyWeight}`
        : `${actuallyWeight}(${settleErrorWeight > 0 ? '-' : '+'}${Math.abs(settleErrorWeight)})`
      tableData[j][`${prefixC}_val`] = actuallyWeight || 0 // 保留原重量-用于表格汇总

      // 更新统计数据
      stats.allRoll++
      stats.allWeight = currency(actuallyWeight || 0).add(stats.allWeight).value
      stats.allPaperTubeWeight = currency(formatWeightDiv(currentItem?.paper_tube_weight) || 0).add(stats.allPaperTubeWeight).value
      stats.allErrorWeight = currency(settleErrorWeight || 0).add(stats.allErrorWeight).value

      item.fc_data_list.splice(0, 1) // 移除已处理的数据
      if (item.fc_data_list.length <= 0)
        return { completed: true }
    }
  }

  return { completed: false }
}

function initRow() {
  const row: Record<string, string> = {}
  COLUMN_SUFFIXES.forEach((suffix) => {
    COLUMN_PREFIXES.forEach((prefix) => {
      row[prefix + suffix] = ''
    })
  })
  return row
}

// 同面料、色号、销售单价的合并在一起
function coverData(data: DeliveryData): DeliveryItem[] | false {
  const datas: DeliveryItem[] = []
  data.items.forEach((item) => {
    const specification = `${item?.product_code}-${item?.product_color_code}/${item?.product_color_name}-${item?.sale_price}`

    if (!item?.fc_data_list?.length)
      return

    const existingItem = datas.find(v =>
      `${v?.product_code}-${v?.product_color_code}/${v?.product_color_name}-${v?.sale_price}` === specification,
    )

    item.uniq = specification
    if (existingItem) {
      existingItem.other_price = currency(existingItem.other_price || 0).add(item.other_price || 0).value // 汇总单价
      existingItem.fc_data_list.push(...item.fc_data_list)
    }
    else { datas.push(item) }
  })
  return datas
}

// 排序
function sortData(data: DeliveryItem[]): DeliveryItem[] | false {
  if (!data?.[0]?.fc_data_list) {
    ElMessage.error('该单据不支持打印送货单')
    return false
  }

  const newData = [...data]

  for (const item of newData) {
    const groupByDyelot = item.fc_data_list.reduce((acc: any, curr: any) => {
      const dyelotNumber = curr.dye_factory_dyelot_number
      if (!acc[dyelotNumber])
        acc[dyelotNumber] = []

      acc[dyelotNumber].push(curr)
      return acc
    }, {})

    Object.values(groupByDyelot).forEach((group: any) => {
      group.sort((a: any, b: any) => (a.volume_number || 0) - (b.volume_number || 0))
    })

    item.fc_data_list = (Object.values(groupByDyelot) as DyeLotItem[][])
      .flat()
      .sort((a, b) => {
        if (a.dye_factory_dyelot_number < b.dye_factory_dyelot_number)
          return -1
        if (a.dye_factory_dyelot_number > b.dye_factory_dyelot_number)
          return 1
        return (a.volume_number || 0) - (b.volume_number || 0)
      })
  }

  newData.sort((a, b) => b.fc_data_list.length - a.fc_data_list.length)
  return newData
}

/**
 * 数据处理，金额计算
 */
function getOrderInfo(
  data: any,
  tables: any,
  specification: string,
  item: any,
  stats: any,
) {
  const { allRoll, allWeight, allPaperTubeWeight, allErrorWeight } = stats
  const sale_price = formatUnitPriceDiv(item?.sale_price) || 0// 销售金额
  const other_price = formatPriceDiv(item?.other_price) || 0 // 其他金额
  const totalMoney = currency(sale_price || 0).multiply((allWeight || 0) - (allErrorWeight || 0)).add(other_price || 0).value

  return {
    ...data,
    sale_system_name: `${data?.sale_system_name}（送货单）`,
    sale_system_addr: data?.sale_system_addr,
    sale_system_phone: data?.sale_system_phone,
    order_time: formatDate(data?.order_time),
    customer_name: data?.customer_name,
    specification,
    tables,
    contacts: data?.auditor_name,
    roll: formatPriceDiv(item?.roll),
    weight: formatWeightDiv(item?.weight),
    sale_price,
    settle_price: item?.settle_price,
    product_name: item?.product_name,
    uniq: item.uniq, // 色号、面料、单价字段
    allRoll,
    allWeight,
    allPaperTubeWeight,
    allErrorWeight,
    allSettleWeight: allWeight - allErrorWeight,
    allMoney: totalMoney,
    otherMoney: other_price || '0',
    chineseMoney: number2text(totalMoney),
  }
}

function confirmData(data: any, table: any[], item: any, stats: any) {
  const specification = `${item?.product_code}-${item?.product_color_code}/${item?.product_color_name}`
  return getOrderInfo(data, table, specification, item, stats)
}

export default function formatDeliveryNote(data: DeliveryData) {
  if (!data?.items) {
    ElMessage.error('数据错误')
    return
  }

  const allData: any[] = []
  let processedData = coverData(data)
  if (!processedData)
    return

  processedData = sortData(processedData)
  if (!processedData)
    return

  processedData.forEach((item: DeliveryItem) => {
    let tableData: any[] = []
    const stats = {
      allMoney: 0,
      allRoll: 0,
      allWeight: 0,
      allPaperTubeWeight: 0,
      allErrorWeight: 0,
    }

    while (true) {
      tableData = Array(MAX_ROWS).fill(null).map(() => initRow())
      const result = processTableData(item, tableData, stats)

      if (result.completed) {
        allData.push(confirmData(data, tableData, item, stats))
        break
      }

      allData.push(confirmData(data, tableData, item, stats))
      tableData = []
    }
  })

  // 重新处理汇总信息，同面料色号、单价的底部汇总信息一致
  allData.forEach((item) => {
    const findLatstSameItem = allData.findLast(v => v.uniq === item.uniq) // 最后一条相同的数据
    item.allRoll = findLatstSameItem.allRoll
    item.allWeight = findLatstSameItem.allWeight
    item.allPaperTubeWeight = findLatstSameItem.allPaperTubeWeight
    item.allErrorWeight = findLatstSameItem.allErrorWeight
    item.allSettleWeight = findLatstSameItem.allSettleWeight
    item.allMoney = findLatstSameItem.allMoney
    item.otherMoney = findLatstSameItem.otherMoney
    item.chineseMoney = findLatstSameItem.chineseMoney
    item.tables = item.tables.filter(e => !every(e, isEmpty)) // 清除空行-不然导致多出空白页
  })

  return allData
}
