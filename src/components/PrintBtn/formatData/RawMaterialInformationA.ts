import { isArray } from 'lodash-es'
import { joinValues } from 'arcdash'

function getPrintCode(data: any): string {
  return joinValues([data?.raw_matl_code, data?.code], '-')
}
export default (data: any) => {
  const allData = []
  const F_print_code = getPrintCode(data)

  if (isArray(data)) {
    data.forEach((item: any) => {
      allData.push({
        ...item,
        F_print_code,
        F_raw_matl_code_color_code: joinValues([item.raw_material_code, item.code], '-'),
      })
    })
  }
  else {
    allData.push({
      ...data,
      F_print_code,
      F_raw_matl_code_color_code: joinValues([data.raw_material_code, data.code], '-'),
    })
  }

  return allData
}
