/*
 * @LastEditTime: 2024-11-19 10:10:03
 * @Description:供方对账表处理
 */
import currency from 'currency.js'
import { formatHashTag, formatPriceSymbol, mergeWeightUnit, sumNum } from '@/common/format'
import { processDataOut } from '@/common/handBinary'

export default (data: any) => {
  data = processDataOut(data)
  const table: any[] = (data?.list || []).map((item: any) => {
    const amount = currency(item.weight).multiply(item.sale_price).value // 金额：结算数量*单价
    const otherReceivable = currency(item.offset_price).add(item.discount_price).add(item.chargeback_money).value // 其他付款：优惠+折扣+扣款
    const payableAmount = currency(amount).add(item.other_price).value// 应付金额：金额+其他应付
    return {
      ...item,
      product_code_name: formatHashTag(item.code, item.name), // 品名：编号#颜色
      product_color_code_name: formatHashTag(item?.product_color_code, item?.product_color_name), // 颜色：编号#颜色
      sale_price_format: formatPriceSymbol({ value: item.sale_price, suffix: '元' }), // 单价
      weight_format: formatPriceSymbol({ value: item.weight, suffix: item.measurement_unit_name === '-' ? '' : item.measurement_unit_name }), // 结算数量
      amount, // 金额
      amount_format: formatPriceSymbol({ value: amount, prefix: '￥' }), // 金额
      otherReceivable,
      other_price_format: formatPriceSymbol({ value: item.other_price, prefix: '￥' }), // 其他应付
      payableAmount,
      payableAmount_format: formatPriceSymbol({ value: payableAmount, prefix: '￥' }), // 应付金额
      settle_price_format: formatPriceSymbol({ value: item.settle_price, prefix: '￥' }), // 实付款
      otherReceivable_format: formatPriceSymbol({ value: otherReceivable, prefix: '￥' }), // 其他付款
      balance_format: formatPriceSymbol({ value: item.balance, prefix: '￥', showZero: true }), // 结余
    }
  })

  const printData = {
    ...data,
    dateRange: data?.summary?.start_time ? `${data?.summary.start_time}至${data?.summary.end_time}` : '-', // 日期处理
    should_pay_money_format: formatPriceSymbol({ value: data.summary?.should_pay_money || 0, showZero: true }), // 应付金额
    payed_money_format: formatPriceSymbol({ value: data.summary?.payed_money || 0, showZero: true }), // 已付金额
    end_period_format: formatPriceSymbol({ value: data.summary?.end_period || 0, showZero: true }), // 累欠金额/本期结余 - 新
    last_balance_price_format: formatPriceSymbol({ value: data.summary?.last_balance_price || 0, showZero: true }), // 前欠金额/上期结余
    table,
    // 汇总当前页-
    countPageTotal: sumNum,
    // 汇总当前页-前缀追加￥
    countPageTotalFormat: (list: any, key: string) => formatPriceSymbol({ value: sumNum(list, key), prefix: '￥' }),
    // 合并数量
    mergeWeightUnit,
  }

  return printData
}
