import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

import { dealBigMoney, operateData } from '@/util/printFn'

export default (data: any) => {
  let allRoll = 0
  let tables = data?.item_data?.map((item: any) => {
    const pro_name_color = `${item.product_code}#${item.product_name}-${item.product_color_code}#${item.product_color_name}-缸号：${item.dyelot_number}`
    allRoll += formatTwoDecimalsDiv(item.return_roll)
    allRoll = Number(allRoll.toFixed(2))
    return {
      ...item,
      pro_name_color, // 品名颜色
      measurement_unit_name: item.measurement_unit_name, // 单位
      sale_price: formatUnitPriceDiv(item.sale_price), // 单价
      settle_price: formatPriceDiv(item.total_price), // 金额
      xima: item?.item_data?.map((v: any) => {
        return {
          ...v,
          settle_weight: formatWeightDiv(v.sale_weight),
        }
      }),
    }
  })
  tables = operateData(tables, 'xima', 10, 'settle_weight')
  let allWeights = 0 // 总计戏码数量
  let allPrice = 0 // 总计金额
  tables.forEach((item: any) => {
    item.allCountPrice = (item.allCount * item.sale_price).toFixed(2)
    allPrice += item.allCount * item.sale_price
    allWeights += item.allCount
    allPrice = Number(allPrice.toFixed(2))
    allWeights = Number(allWeights.toFixed(2))
  })
  const data2 = {
    ...data,
    address: data.address, // 收货地址
    phone: data.phone, // 电话/监督号码
    order_no: data.order_no, // 单号
    customer_name: data.customer_name, // 客户
    // 货运
    order_time: formatDate(data?.order_time), // 日期
    tables, // 表格
    allPriceText: dealBigMoney(allPrice), // 总金额大写
    allRoll, // 合计戏码匹数
    allWeights, // 合计戏码数量
    allPrice, // 合计金额
    // 预收
    total_settle_money: formatPriceDiv(data?.total_should_pay_amount), // 应收
    remark: data.order_remark, // 备注
    creator_name: data.creator_name, // 创建人
    sale_user_name: data.salesman_name, // 销售员
    // 收货经手人
    // 客户签名
  }
  return data2
}
