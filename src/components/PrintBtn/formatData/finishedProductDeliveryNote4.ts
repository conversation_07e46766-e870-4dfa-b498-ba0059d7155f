import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import { pinyin } from 'pinyin-pro'
import { formatHashTag, sumNum } from '@/common/format'
import { dealBigMoney, operateData } from '@/util/printFn'
import { processDataOut } from '@/common/handBinary'
// 根据规格提取数据
function coverData(data: any): any[] | boolean {
  const datas: any = []
  data.items.map((item: any) => {
    // 拿到该项的缸号作为合并标识
    const dyelotNumber = item?.dyelot_number
    // 如果 fc_data_list 长度为 0，则跳过这条数据
    if (!item?.fc_data_list?.length)
      return // 跳过此项

    // 对比缸号，相同的就push进去，否则就是新的缸号，需要存到新项
    let isSave = false
    datas?.forEach((v: any) => {
      const itemDyelotNumber = v?.dyelot_number
      if (itemDyelotNumber === dyelotNumber) {
        isSave = true
        // 如果item里没有fc_data_list，提示错误，并return false
        if (!item?.fc_data_list?.length || !v?.fc_data_list?.length) {
          // ElMessage.error('有细码未录入')
          return false
        }
        v?.fc_data_list.push(...item?.fc_data_list)
      }
    })
    if (!isSave)
      datas.push(item)
  })
  return datas
}

function sortData(data: any) {
  // 如果没有fc_data_list，则直接返回“该单据不支持打印送货单”
  if (!data?.[0].fc_data_list) {
    ElMessage.error('该单据不支持打印送货单')
    return false
  }
  // 复制一份data，避免修改原数组
  const newData = [...data]
  // 使用sort方法，根据自定义的比较函数进行排序
  newData.sort((a, b) => {
    // 如果a的fc_data_list的长度大于b的，那么a排在前面，返回负数
    if (a.fc_data_list.length > b.fc_data_list.length)
      return -1

    // 如果a的fc_data_list的长度小于b的，那么a排在后面，返回正数
    if (a.fc_data_list.length < b.fc_data_list.length)
      return 1

    // 如果a和b的fc_data_list的长度相等，那么不改变它们的顺序，返回0
    return 0
  })
  // 对每个data的fc_data_list内部进行排序，volume_number小的排前面
  for (const item of newData) {
    item.fc_data_list.sort((x: any, y: any) => {
      // 如果x的volume_number小于y的，那么x排在前面，返回负数
      if (x.volume_number < y.volume_number)
        return -1

      // 如果x的volume_number大于y的，那么x排在后面，返回正数
      if (x.volume_number > y.volume_number)
        return 1

      // 如果x和y的volume_number相等，那么不改变它们的顺序，返回0
      return 0
    })
  }
  // 返回排序后的新数组
  return newData
}

// 是否为主单位
function isMainUnit(item: any) {
  return item.measurement_unit_id === item.auxiliary_unit_id
}

export default (data: any) => {
  if (!data?.items)
    return ElMessage.error('数据错误')
  const formatData = processDataOut(data)

  let datas: any = coverData(formatData) // 相同的面料色号数据合并
  if (datas === false)
    return

  datas = sortData(datas)

  if (!datas)
    return

  let tables = datas.map((e1: any) => {
    const fc_data_list = e1.fc_data_list.map((e2: any) => {
      return {
        ...e2,
        settle_weight: isMainUnit(e1) ? e2.settle_weight : e2.length, // 主副单位这里都用settle_weight展示
      }
    })
    return {
      ...e1,
      fc_data_list,
    }
  })
  tables = operateData(tables, 'fc_data_list', 10, 'settle_weight') // 处理细码显示

  tables = tables.map((e: any) => {
    const allWeights = sumNum(e.fc_data_list, 'settle_weight') // 行内合计数量
    const unit_price = isMainUnit(e) ? e.sale_price : e.length_cut_sale_price // 单价
    const product_code_name = formatHashTag(e.product_code, e.product_name)// 品名：编号#颜色
    const product_color_code_name = formatHashTag(e.product_color_code, e.product_color_name)// 颜色：编号#颜色
    const pro_name_color = `${product_code_name}-${product_color_code_name}${e.dyelot_number ? `-${e.dyelot_number}` : ''}`
    const formatItem = {
      ...e,
      unit_name: isMainUnit(e) ? e.measurement_unit_name : e.auxiliary_unit_name, // 单位
      unit_price, // 单价
      allWeights, // 行内合计数量
      allPrice: currency(allWeights).multiply(unit_price).value, // 行内金额
      pro_name_color, // 品名颜色
      product_code_name, // 品名
      product_color_code_name, // 颜色
    }
    return formatItem
  })
  const allPrice = sumNum(tables, 'allPrice') // 合计金额

  const sale_system_name = formatData?.sale_system_name || '浩拓智能' // 账套名称
  const namePinyin = pinyin(sale_system_name, { toneType: 'none' })
  const sale_system_name_pinyin = namePinyin.toUpperCase() // 账套名称拼音
  const allData = {
    ...formatData,
    allRoll: sumNum(formatData.items, 'roll'), // 总匹数
    allOtherPrice: sumNum(formatData.items, 'other_price'), // 其他金额
    allWeights: sumNum(tables, 'allWeights'), // 合计数量
    allPrice,
    allPriceText: dealBigMoney(allPrice), // 合计大写
    sale_system_name: `${sale_system_name}-销售码单`,
    sale_system_name_pinyin,
    tables,
  }
  return allData
}
