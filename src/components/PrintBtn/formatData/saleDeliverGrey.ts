import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

import { dealBigMoney, operateData } from '@/util/printFn'

export default (data: any) => {
  let allRoll = 0
  let tables = data?.item_data?.map((item: any) => {
    const pro_name_color = `${item.grey_fabric_code}#${item.grey_fabric_name}-${item.grey_fabric_code}#${item.gray_fabric_color_name}`
    return {
      ...item,
      pro_name_color, // 品名颜色
      measurement_unit_name: item.measurement_unit_name, // 单位
      sale_price: formatUnitPriceDiv(item.single_price), // 单价
      settle_price: formatPriceDiv(item.total_price), // 金额
      xima: item?.item_fc_data?.map((v: any) => {
        allRoll += formatTwoDecimalsDiv(v.roll)
        allRoll = Number(allRoll.toFixed(2))
        return {
          ...v,
          settle_weight: formatWeightDiv(v.weight),
        }
      }),
    }
  })
  //

  tables = operateData(tables, 'xima', 10, 'settle_weight')
  //

  let allWeights = 0 // 总计戏码数量
  let allPrice = 0 // 总计金额
  tables.forEach((item: any) => {
    item.allCountPrice = (item.allCount * item.sale_price).toFixed(2)
    allPrice += item.allCount * item.sale_price
    allWeights += item.allCount
    allPrice = Number(allPrice.toFixed(2))
    allWeights = Number(allWeights.toFixed(2))
  })
  const data2 = {
    ...data,
    process_factory_name: data.customer_name, // 加工厂名称
    receive_addr: data.sale_system_addr, // 收货地址
    receive_phone: data.biz_unit_phone, // 电话/监督号码
    order_no: data.document_code, // 单号
    customer_name: data.customer_name, // 客户
    // 货运
    order_time: formatDate(data?.create_time), // 日期
    tables, // 表格
    allPriceText: dealBigMoney(allPrice), // 总金额大写
    allRoll, // 合计戏码匹数
    allWeights, // 合计戏码数量
    allPrice, // 合计金额
    // 预收
    total_settle_money: formatPriceDiv(data.should_collect_money), // 应收
    total_collected_money: formatPriceDiv(data.actually_collect_money), // 实收
    total_uncollect_money: formatPriceDiv(data?.wait_collect_money), // 未收
    total_arrears_amount: formatPriceDiv(data?.total_wait_collect_money), // 欠款
    remark: data.remark, // 备注
    creator_name: data.creator_name, // 创建人
    sale_user_name: data.salesman_name, // 销售员
    // 收货经手人
    // 客户签名
  }
  return data2
}
