import { joinValues } from 'arcdash'
import { processDataOut } from '@/common/handBinary'
import { formatTwoDecimalsDiv } from '@/common/format'

export default (data: any) => {
  const { shrinkage_rate, inspect_avg_edge_width, inspect_avg_gram_weight, ...result } = data
  // 处理进制
  const newData: any = {
    ...processDataOut(result),
    shrinkage_rate,
    inspect_avg_edge_width,
    inspect_avg_gram_weight,
  }
  // 处理疵点
  const prefix = 'F_flaw_'
  newData?.defect_list?.map((item: any) => {
    newData[`${prefix}${item?.mark}`] = item?.name
  })
  if (newData.qc_item === null || newData.qc_item?.length === 0) {
    return {
      ...newData,
      inspect_ratio: joinValues([newData?.inspect_ratio || 0, '%']),
      // inspect_avg_edge_width: newData?.inspect_avg_edge_width.toString(),
      // inspect_avg_gram_weight: newData?.inspect_avg_gram_weight.toString(),
    }
  }
  const allData = []

  // 每个表最多10行
  let tables: any[] = []
  newData.qc_item?.map((item: any) => {
    tables.push({
      ...item,
      actually_weight: `${item?.stock_weight.toString()}/${item?.actually_weight.toString()}`, // 【ID1002028】3.质检报告的数量的显示修改为显示 库存数量/实际数量
      is_pass: item?.is_pass ? '合格' : '不合格',
    })
    if (tables.length === 10) {
      allData.push({
        ...newData,
        inspect_convert_avg_score: formatTwoDecimalsDiv(newData?.inspect_convert_avg_score || 0),
        inspect_ratio: joinValues([newData?.inspect_ratio || 0, '%']),
        tables,
      })
      tables = []
    }
  })
  if (tables.length !== 0) {
    allData.push({
      ...newData,
      inspect_convert_avg_score: formatTwoDecimalsDiv(newData?.inspect_convert_avg_score || 0),
      inspect_ratio: joinValues([newData?.inspect_ratio || 0, '%']),
      tables,
    })
  }

  // 遍历allData里的每一个tables，给每一个tables里的对象添加对应的index
  allData.map((item) => {
    item.tables.map((tableItem: any, index: number) => {
      tableItem.index = index + 1
      tableItem.convert_total_score = tableItem.convert_total_score || 0
    })
  })

  return allData
}
