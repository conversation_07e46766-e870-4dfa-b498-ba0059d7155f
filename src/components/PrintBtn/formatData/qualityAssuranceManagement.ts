import { joinValues } from 'arcdash'
import { processDataOut } from '@/common/handBinary'
import { formatTwoDecimalsDiv } from '@/common/format'

export default (data: any) => {
  // 处理进制
  data = processDataOut(data)

  // 处理疵点
  const prefix = 'F_flaw_'
  data?.defect_list?.map((item: any) => {
    data[`${prefix}${item?.mark}`] = item?.name
  })

  if (data.qc_item === null || data.qc_item?.length === 0) {
    return {
      ...data,
      inspect_ratio: joinValues([data?.inspect_ratio || 0, '%']),
    }
  }

  const allData = []

  // 每个表最多6行
  let tables: any[] = []
  data.qc_item?.map((item: any) => {
    tables.push({
      ...item,
      is_pass: item?.is_pass ? '合格' : '不合格',
    })
    if (tables.length === 6) {
      allData.push({
        ...data,
        inspect_convert_avg_score: formatTwoDecimalsDiv(data?.inspect_convert_avg_score || 0),
        inspect_ratio: joinValues([data?.inspect_ratio || 0, '%']),
        tables,
      })
      tables = []
    }
  })
  if (tables.length !== 0) {
    allData.push({
      ...data,
      inspect_convert_avg_score: formatTwoDecimalsDiv(data?.inspect_convert_avg_score || 0),
      inspect_ratio: joinValues([data?.inspect_ratio || 0, '%']),
      tables,
    })
  }

  // 遍历allData里的每一个tables，给每一个tables里的对象添加对应的index
  allData.map((item) => {
    item.tables.map((tableItem: any, index: number) => {
      tableItem.index = index + 1
      tableItem.convert_total_score = formatTwoDecimalsDiv(tableItem.convert_total_score || 0)
    })
  })

  return allData
}
