import { joinValues } from 'arcdash'
import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { dealBigMoney } from '@/util/printFn'

export default (data: any) => {
  let totalWeight = 0
  let totalPrice = 0
  // 数量为0的不打印
  const printList = (data?.item_data?.filter((item: any) => formatWeightDiv(item.weight) > 0)) || []
  const tables = printList.map((item: any, index: number) => {
    totalWeight += formatWeightDiv(item.weight)
    totalWeight = Number(totalWeight.toFixed(2))
    totalPrice += formatPriceDiv(item.total_price || 0)
    totalPrice = Number(totalPrice.toFixed(2))

    const obj = {
      ...item,
      index: index + 1, // 序号
      customer_account_num: item.customer_account_num, // 款号、批次
      product_code: item.product_code, // 产品编号
      product_code_color_code: `${item.product_code || ''}-${item.product_color_code || ''}`, // 产品编号
      F_material_code_and_color_code: joinValues([item.material_code, item.material_color_code], '-'),
      F_material_name_and_color_name: joinValues([item.material_name, item.material_color_name], '-'),
      ingredient_item: item.finish_product_ingredient, // 成分比例
      finish_product_gram_weight: `${item.finish_product_gram_weight}${item.finish_product_gram_weight_unit_name}`, // 克重
      finish_product_width: `${item.finish_product_width}${item.finish_product_width_unit_name}`, // 包边门幅
      color_name: item.product_color_name, // 颜色
      color_code: item.product_color_code, // 色号
      color_name_code: `${item.product_color_name}#${item.product_color_code}`, // 颜色
      measurement_unit_name: item.measurement_unit_name, // 单位
      weight: formatWeightDiv(item.weight), // 数量
      sx: `${formatPriceDiv(item.upper_limit)}%`, // 上限(%)
      xx: `${formatPriceDiv(item.lower_limit)}%`, // 下限(%)
      unit_price: formatUnitPriceDiv(item.unit_price), // 单价
      price: formatPriceDiv(item.total_price), // 金额
      weightFor: `${formatWeightDiv(item.paper_tube_weight)}&${formatWeightDiv(item.weight_error)}`, // 纸筒及空差
      other_price: formatPriceDiv(item.other_price), // 其他金额
      rate: data?.is_with_tax_rate ? `${formatPriceDiv(data?.sale_tax_rate)}%` : '-', // 税率 暂无字段
      detail_order_no: item?.detail_order_no, // 订单编号
    }
    if (item.plan_type === 1) {
      // 成品
      obj.product_code = item.product_code
      obj.ingredient_item = item.finish_product_ingredient
      obj.finish_product_gram_weight = item.finish_product_gram_weight_and_unit_name
      obj.finish_product_width = item.finish_product_width_and_unit_name
      obj.color_name = item.product_color_name
      obj.color_name_code = `${item.product_color_name}#${item.product_color_code}`
    }
    else if (item.plan_type === 2) {
      // 坯布
      obj.product_code = item.grey_fabric_code
      obj.ingredient_item = item.grey_fabric_composition
      obj.finish_product_gram_weight = item.grey_fabric_gram_weight_and_unit_name
      obj.finish_product_width = item.grey_fabric_width_and_unit_name
      obj.color_name = ''
      obj.color_name_code = ''
      obj.product_name = item.grey_fabric_name
    }
    else if (item.plan_type === 3) {
      // 原料
      obj.product_code = item.raw_material_code
      obj.ingredient_item = item.ingredient
      obj.finish_product_gram_weight = ''
      obj.finish_product_width = ''
      obj.color_name = item.raw_material_color_name
      obj.color_name_code = `${item.raw_material_color_name}#${item.raw_material_color_code}`
      obj.product_name = item.raw_material_name
    }
    return obj
  })
  return {
    ...data,
    sale_system_name: data.sale_system_name, // 公司名
    // receipt_address: `${data.sale_system_addr} 电话:${data.sale_system_phone}`, // 地址   后端给
    customer_contact: `${data?.contact_name}/${data.customer_phone}`, // 客户联系人+电话
    customer_name: data?.customer_name, // 联系人\电话
    customer_phone: data.customer_phone, // 电话
    sale_system_contact_system: `${data?.sale_system_contacts}/${data?.sale_system_phone}`, // 营销体系联系人+电话
    sale_system_contact_user: `${data?.sale_user_name}/${data?.sale_user_phone}`, // 销售员名称+电话
    sale_user_name: data?.sale_user_name, // 负责跟单、销售
    order_no: data?.order_no, // 合同编号
    order_time: formatDate(data?.order_time), // 订单日期
    receipt_time: formatDate(data?.receipt_time), // 发货日期
    rate: data?.is_with_tax_rate ? `${formatPriceDiv(data?.sale_tax_rate)}%` : '不含税', // 税率
    // 表格
    tables,
    totalPrice, // 合计金额
    totalWeight, // 合计数量
    totalPriceText: dealBigMoney(totalPrice), // 合计金额大写
    internal_remark: data?.internal_remark, // 备注
    customer_code: data?.voucher_number, // 客户合同编号
    creator_name: data?.creator_name, // 编制
  }
}
