import currency from 'currency.js'
import { formatDate, formatPriceDiv, formatWeightDiv } from '@/common/format'
import { dealBigMoney } from '@/util/printFn'

/**
 * 24-02-03 更新：一张合同一个成品
 */
export default (data: any) => {
  const allData: any[] = []
  // 取出工艺资料
  let qualityRequirement = ''
  try {
    const craft_requirement = JSON.parse(data?.craft_requirement || '{}')
    let qIndex = 1
    craft_requirement?.checkList?.map((item: any) => {
      qualityRequirement += `\n`
      qualityRequirement += `${qIndex}、${item.name}`
      qIndex++
    })

    const remarkTemp: any[] = []

    craft_requirement?.selectList?.map((item: any) => {
      if (item?.show_type === 2) {
        // 多选
        const selectList = item?.selectValue?.split(',') || []
        let indexInit = false
        selectList?.map((value: any) => {
          // 循环type_data，找到和value相等的id，然后取出name，拼接到qualityRequirement
          const findData = item?.type_data.find(
            (i: any) => i.id === Number(value),
          )
          if (findData) {
            if (!indexInit) {
              // 添加换行
              qualityRequirement += `\n`
              qualityRequirement += `${qIndex}、${item.name}：`
              qIndex++
              indexInit = true
            }
            qualityRequirement += `${findData?.name}、`
          }
        })
        // 去掉最后一个顿号
        if (qualityRequirement.endsWith('、'))
          qualityRequirement = qualityRequirement.slice(0, -1)
      }
      if (item?.show_type === 3) {
        // 备注
        remarkTemp.push(`${item.selectValue}`)
      }
    })

    // 统一把备注补进去
    if (remarkTemp.length > 0) {
      qualityRequirement += `\n`
      qualityRequirement += `${qIndex}、备注：`
      remarkTemp.map((item: any) => {
        qualityRequirement += `${item}  `
      })
      qIndex++
      qualityRequirement += `\n`
    }

    qualityRequirement += `\n`
    qualityRequirement += `${qIndex}、特殊要求：${
      data?.is_length_cut ? '剪版' : '不剪版'
    }`
  }
  catch (error) {

  }

  data?.items?.forEach((item: any, index: number) => {
    const productCode = item?.code // 产品编号
    const productName = item?.name // 产品名称
    const productGramWeight = item?.gram_weight // 克重
    const productWidth = item?.finish_product_width_and_unit_name // 成品门幅
    const dnfCraft = item?.dnf_craft // 加工流程

    const yarnCount = item.yarn_count // 纱支

    const dyeingLossAndUnit = `${currency(item?.dyeing_loss).divide(100).value} ${item?.unit}` // 染色损耗(带单位)

    const totalWeight = formatWeightDiv(item.weight)
    const totalPrice = formatPriceDiv(item.total_price) || 0

    //  匹数
    const FRoll = formatPriceDiv(item.piece_count) || 0

    let useDyeUnitUseOrderNo = item?.dye_unit_use_order_no || ''
    if (useDyeUnitUseOrderNo === '') {
      item?.use_fabric.map((value: any) => {
        useDyeUnitUseOrderNo += value?.gf_stock_info?.dye_unit_use_order_no || ''
      })
    }

    const tables = [
      {
        ...item,
        FRoll,
        color_matching_data: item?.color_matching_data, // 资料
        index: index + 1, // 序号
        color_name: item.color_name, // 颜色
        // 单位
        weight: formatWeightDiv(item.weight), // 成品数量
        weightWithUnit: `${formatWeightDiv(item.weight)} ${item.unit}`, // 成品数量
        // 单价
        // 金额
        remark: item.remark, // 备注
        // useBlankNumber,
        useDyeUnitUseOrderNo,
      },
    ]

    // 判断有没有这个产品编号
    const haveData = allData.findIndex(
      (i: any) => i.productCode === productCode,
    )

    if (haveData === -1) {
      allData.push({
        ...item,
        productCode,
        productName,
        productGramWeight,
        productWidth,
        dnfCraft,
        tables,
        yarnCount,
        totalPrice, // 合计金额
        totalWeight, // 合计数量
        totalPriceText: dealBigMoney(totalPrice), // 合计金额大写
        ...getCommonData(data),
        qualityRequirement,
        dyeing_loss: currency(item?.dyeing_loss).divide(100),
        dnf_loss: formatPriceDiv(item?.dnf_loss),
        dyeingLossAndUnit,
      })
    }
    else {
      allData[haveData].tables = [...allData[haveData].tables, ...tables]
      allData[haveData].totalWeight += totalWeight
    }
  })
  return Object.values(allData)
}

function getCommonData(data: any) {
  return {
    ...data,
    sale_system_name: data?.sale_system_name, // 公司名
    sale_system_address: data?.sale_system_addr, // 地址
    sale_system_addr: `${data.sale_system_addr} 电话:${data.sale_system_phone}`, // 地址   后端给
    supplier_name: data.dye_factory_name, // 乙方
    order_no: data.order_no, // 合同编号
    pay_method: `微信/短信`, // 下单方式 写死
    purchase_date: formatDate(data.dnf_date), // 订单日期-染整日期
    receipt_date: '暂无字段', // formatDate(data.receiving_time), // 交货日期 - 暂无字段
    customer: `${data.order_follower_name} ${data.order_follower_phone}`, // 需方联系人、电话 写死
    supplier: `${data.dye_factory_contact} ${data.dye_factory_phone}`, // 供方联系人、电话  后端给
    remark: data.remark, // 备注
    // 第三点工艺要求 需要处理数据
    // 第五点光源要求 需要处理数据
    // 缩水率牌子 需要处理数据
    text: '☑',
    test: '☐',
  }
}
