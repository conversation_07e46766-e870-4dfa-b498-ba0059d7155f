import { pinyin } from 'pinyin-pro'
import currency from 'currency.js'
import { formatCalculate, formatDate, formatHashTag, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

import { dealBigMoney, operateData } from '@/util/printFn'

export default (data: any) => {
  let allRoll = 0
  let tables = data?.sale_transfer_order_detail_info?.map((item: any) => {
    // const pro_name_color = `${item.product_code}#${item.product_name}-${item.product_color_code}#${item.product_color_name}-缸号：${item.dyelot_number}`
    const product_code_name = formatHashTag(item.product_code, item.product_name)// 品名：编号#颜色
    const product_color_code_name = formatHashTag(item.product_color_code, item.product_color_name)// 颜色：编号#颜色
    const pro_name_color = `${product_code_name}-${product_color_code_name}${item.dyelot_number ? `-${item.dyelot_number}` : ''}`
    allRoll += formatTwoDecimalsDiv(item.roll)
    allRoll = Number.parseFloat(allRoll.toFixed(2))
    return {
      ...item,
      pro_name_color, // 品名颜色
      product_code_name, // 品名
      product_color_code_name, // 颜色
      measurement_unit_name: item.measurement_unit_name, // 单位
      sale_price: formatUnitPriceDiv(item.sale_price), // 单价
      other_price: formatPriceDiv(item.other_price), // 其他金额
      settle_price: formatPriceDiv(item.total_price), // 金额
      total_sale_amount: formatPriceDiv(item.total_sale_amount), // 结算金额
      sale_settle_weight: formatWeightDiv(item.sale_settle_weight), // 结算数量
      xima: item?.sale_transfer_order_weight_info_list?.map((v: any) => {
        return {
          ...v,
          settle_weight: formatWeightDiv(v.sale_weight),
        }
      }),
    }
  })

  tables = operateData(tables, 'xima', 10, 'settle_weight')

  let allWeights = 0 // 总计戏码数量
  let allPrice = 0 // 总计金额（不含空差，其他金额）
  let allOtherPrice = 0 // 总计其他金额
  tables.forEach((item: any) => {
    item.allCountPrice = Number.parseFloat((item.allCount * item.sale_price).toFixed(2))
    allPrice += item.allCount * item.sale_price
    allWeights += item.allCount
    allOtherPrice += item.other_price
    allOtherPrice = Number.parseFloat(allOtherPrice.toFixed(2))
    allWeights = Number.parseFloat(allWeights.toFixed(2))
    // 处理细码显示一行“25、23.5”
    item.ximaTotalStr = item.xima.map(e => e.settle_weight).join('、')
    // 总空差
    const allSaleWeightError = item.xima.reduce((pre: any, cur: any) => {
      return currency(pre).add(cur.sale_weight_error).value
    }, 0)
    item.allSaleWeightError = formatWeightDiv(allSaleWeightError)
    item.allSaleSettleWeight = currency(item.allCount).subtract(item.allSaleWeightError).value // 结算数量
    item.allSalePrice = formatCalculate(item.allSaleSettleWeight * item.sale_price) // 结算金额
  })

  const tenant_management_name = data?.tenant_management_name || '浩拓智能' // 账套名称
  const namePinyin = pinyin(tenant_management_name, { toneType: 'none' })
  const tenant_management_name_pinyin = namePinyin.toUpperCase() // 账套名称拼音
  const allSettlePrice = formatPriceDiv(data.total_settle_money)// 结算金额（结算数量*单价）
  const allSettleOtherPrice = formatPriceDiv(data.total_settle_money + data.total_other_money) // 结算金额（结算数量*单价）+其他金额
  const data2 = {
    ...data,
    address: data.address, // 收货地址
    phone: data.phone, // 电话/监督号码
    order_no: data.order_no, // 单号
    customer_name: data.customer_name, // 客户
    tenant_management_name: `${tenant_management_name} - 销售码单`, // 账套名称
    tenant_management_name1: `${tenant_management_name}销售单`, // 账套名称
    tenant_management_name_pinyin, // 账套名称拼音
    // 货运
    order_time: formatDate(data?.order_time), // 日期
    tables, // 表格
    allPriceText: dealBigMoney(allPrice), // 总金额大写
    allRoll, // 合计戏码匹数
    allWeights, // 合计戏码数量
    allPrice: formatCalculate(allPrice), // 合计金额
    allOtherPrice, // 合计其他金额
    // 预收
    total_settle_money: formatPriceDiv(data.total_should_pay_amount), // 应收
    before_arrears_amount: formatPriceDiv(data.before_arrears_amount), // 前欠款
    total_arrears_amount: formatPriceDiv(data.total_arrears_amount), // 累计欠款
    remark: data.order_remark, // 备注
    creator_name: data.creator_name, // 创建人
    allSettlePrice, // 结算金额（结算数量*单价）
    allSettlePriceText: dealBigMoney(allSettlePrice),
    allSettleOtherPrice,
    allSettleOtherPriceText: dealBigMoney(allSettleOtherPrice),
    // sale_user_name: data.salesman_name, // 销售员
    // 收货经手人
    // 客户签名
  }
  return data2
}
