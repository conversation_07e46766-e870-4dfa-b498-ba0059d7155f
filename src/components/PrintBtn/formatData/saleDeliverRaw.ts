import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

import { dealBigMoney } from '@/util/printFn'
export default (data: any) => {
  let allPrice = 0 // 总计金额
  const tables = data?.items?.map((item: any) => {
    allPrice += item.total_price
    return {
      ...item,
      unit_price: formatUnitPriceDiv(item.unit_price || 0),
      total_weight: formatWeightDiv(item.total_weight || 0),
      total_price: formatPriceDiv(item.total_price || 0),
      whole_piece_weight: formatWeightDiv(item.whole_piece_weight || 0),
    }
  })

  const data2 = {
    ...data,
    process_factory_name: data.customer_name, // 加工厂名称
    receive_addr: data.receipt_address, // 收货地址
    receive_phone: data.receipt_phone, // 电话/监督号码
    order_no: data.order_no, // 单号
    customer_name: data.customer_name, // 客户
    // 货运
    order_time: formatDate(data?.sale_date), // 日期
    tables, // 表格
    allPriceText: dealBigMoney(allPrice), // 总金额大写
    allPrice, // 合计金额
    remark: data.remark, // 备注
    creator_name: data.creator_name, // 创建人
    sale_user_name: data.seller_name, // 销售员
  }
  return data2
}
