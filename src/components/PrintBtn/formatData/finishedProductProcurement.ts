import { formatDate, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { dealBigMoney } from '@/util/printFn'

export default (data: any) => {
  let totalWeight = 0
  let totalPrice = 0
  const tables = data?.items?.map((item: any, index: number) => {
    totalWeight += formatWeightDiv(item.total_weight)
    totalWeight = Number(totalWeight.toFixed(2))
    totalPrice += formatPriceDiv(item.total_price || 0)
    totalPrice = Number(totalPrice.toFixed(2))
    return {
      ...item,
      index: index + 1, // 序号
      finish_product_code: item.finish_product_code, // 产品编号
      finish_product_name: item.finish_product_name, // 产品名称
      finish_product_ingredient: item.finish_product_ingredient, // 成分比例
      finish_product_gram_weight: `${item.finish_product_gram_weight}${item.finish_product_gram_weight_unit_name}`, // 克重
      finish_product_width: `${item.finish_product_width}${item.finish_product_width_unit_name}`, // 包边门幅
      color: `${item.color_Name}#${item.color_code}`, // 颜色/色号
      unit: item.unit, // 单位
      total_weight: formatWeightDiv(item.total_weight), // 数量
      sx: `${formatPriceDiv(item.upper_limit)}%`, // 上限（%）
      xx: `${formatPriceDiv(item.lower_limit)}%`, // 下限（%）
      unit_price: formatUnitPriceDiv(item.unit_price), // 单价
      // rate: `${formatPriceDiv(data?.tax_rate)}%`, // 税率
      rate: data?.include_tax ? `${formatPriceDiv(data?.tax_rate)}%` : '', // 税率
      total_price: formatPriceDiv(item.total_price), // 金额
      remark: item.remark, // 备注
    }
  })
  return {
    sale_system_name: data?.sale_system_name, // 公司名
    sale_system_addr: `${data.sale_system_addr} 电话:${data.sale_system_phone}`, // 地址   后端给
    supplier_name: data.supplier_name, // 乙方
    order_no: data.order_num, // 合同编号
    pay_method: `微信/短信`, // 下单方式 写死
    purchase_date: formatDate(data.purchase_date), // 订单日期
    receipt_date: formatDate(data.receipt_date), // 交货日期
    // customer: `张三 13265326554`, // 需方联系人、电话 写死
    customer: `${data?.sale_system_contacts} / ${data?.sale_system_phone}`,
    supplier: `${data.supplier_contact} ${data.supplier_contact_phone}`, // 供方联系人、电话  后端给
    tables, // 表格
    isIncludestax: data?.include_tax ? '含税' : '不含税', // 含税
    totalPrice, // 合计金额
    totalWeight, // 合计数量
    totalPriceText: dealBigMoney(totalPrice), // 合计金额大写
    remark: data.remark, // 备注
  }
}
