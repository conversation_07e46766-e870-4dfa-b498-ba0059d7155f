import currency from 'currency.js'
import { formatDate, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'

/**
 * 24-02-03 更新：一张合同一个成品
 */
export default (data: any) => {
  return getCommonData(data)
}

function getCommonData(data: any) {
  const groupedArray = data.production_notify_material_ratio?.reduce((resultArray, item, index) => {
    const chunkIndex = Math.floor(index / 5)

    if (!resultArray[chunkIndex])
      resultArray[chunkIndex] = [] // start a new chunk

    resultArray[chunkIndex].push(item)

    return resultArray
  }, [])
  return groupedArray.map((group) => {
    return {
      ...data,
      tables: group.map((item, index) => {
        return {
          ...item,
          index: index + 1,
          yarn_ratio: formatTwoDecimalsDiv(item.yarn_ratio),
          use_yarn_quantity: formatWeightDiv(item.use_yarn_quantity),
          yarn_loss: formatTwoDecimalsDiv(item.yarn_loss),
        }
      }),
      weaving_specifications_name: data.weaving_specifications.map(item => item.weaving_specifications_name).join('、'),
      sale_system_name: data?.sale_system_name, // 公司名
      sale_system_address: data?.sale_system_addr, // 地址
      notify_date: formatDate(data.notify_date),
      receipt_grey_fabric_date: formatDate(data.receipt_grey_fabric_date),
      scheduling_weight: formatWeightDiv(data.scheduling_weight),
      // 加工数量 = 排产数量+变更数量
      加工数量: formatWeightDiv(currency(data.scheduling_weight).add(data.change_weight)),
    }
  })
}
