<script setup lang="ts">
import { computed, useSlots } from 'vue'
import { isEmpty } from 'arcdash'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import { isHasAuth } from '@/common/authConvert'
import QywxPopover from '@/components/SelectDialog/QywxPopover.vue'

export interface Props {
  required?: boolean
  label?: string
  copies?: number | string // 以290px为基础，占用份数
  checkAuth?: boolean // 是否需要权限校验
  id?: number
  verticalBlock?: boolean // 是否垂直布局
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  label: '',
  copies: 1,
  checkAuth: false,
  verticalBlock: false,
})

const userStore: any = useUserStore()
const canShow = computed(() => {
  if (!props.checkAuth)
    return true

  if (isEmpty(props.label)) {
    ElMessage.error('没设置labelName我怎么给你校验？')
    return false
  }

  return isHasAuth(props.label, userStore.user.data_access_scope)
})

const slots = useSlots()
// 全局加QyxwPopover组件 匹配客户，供应商，织厂名称 显示企业微信图标
const showQywx = computed(() => {
  if (!props.label?.match(/^(客户|供应商|织厂)名称[:：]?$/))
    return false
  const contentSlot = slots.content?.({})
  return contentSlot?.every((slot: any) => {
    return typeof slot.type === 'symbol' && slot.type.description === 'v-txt'
  }) ?? false
})
const gridSpan = computed(() => ({
  gridColumn: `span ${props.copies}`,
}))
</script>

<template>
  <div v-if="canShow" class="descriptions_item" :class="{ descriptions_item_vertical: verticalBlock }" :style="gridSpan">
    <div class="descriptions_item_label">
      <slot name="label">
        {{ props.label }}
      </slot>
      <div v-if="props.required" class="required">
        *
      </div>
    </div>
    <div class="descriptions_item_content">
      <slot name="content">
        <slot />
      </slot>
      <QywxPopover v-if="showQywx" class="ml-2 inline-block" />
    </div>
  </div>
</template>

<style lang="scss">
.descriptions_row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 0px 16px;
  overflow: hidden;
  flex-shrink: 0;
  .descriptions_item {
    display: grid;
    grid-template-columns: minmax(var(--minLabelWidth), auto) 1fr;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
    min-width: 0; // 防止内容溢出

    .descriptions_item_label {
      padding: 0 10px;
      padding-left: 0;
      position: relative;
      text-align: left;
      white-space: nowrap; // 防止标签换行
      overflow: hidden;
      text-overflow: ellipsis;

      &:empty {
        display: none;
      }

      .required {
        color: red;
        position: absolute;
        right: 4px;
        top: -4px;
      }
    }

    .descriptions_item_content {
      min-width: 0; // 防止内容溢出

      display: flex;
      align-items: center;
      .descriptions_item_form_item {
        margin-bottom: 0;
      }

      .el-form-item {
        display: block;
        margin: 0;
      }

      .el-select,
      .el-input,
      .vxe-input,
      .el-date-editor {
        width: 100% !important;
      }
    }
  }
  // item-竖向布局带背景
  &.vertical_min {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 10px 16px;
  }
  .descriptions_item_vertical {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    background-color: #f9f9fa;
    padding: 6px 10px;
    border-radius: 6px;
    .descriptions_item_label{
      color: #919191;
    }
    .descriptions_item_content {
      color: #000;
      margin-top: 2px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 响应式布局
@media screen and (max-width: 1366px) {
  .descriptions_row {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 6px 6px;
  }
}

@media screen and (max-width: 1024px) {
  .descriptions_row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 4px 8px;
  }
}
</style>
