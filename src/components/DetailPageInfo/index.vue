<template>
  <FildCard :title="props.title" :toolBar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
      <DescriptionsFormItem :copies="item.copies || 1" v-for="(item, index) in props.infoKeys" :key="index" :label="`${item.label}:`">
        <template v-slot:content>
          {{ item.preUnit || '' }}
          <span v-if="item.isDate">
            {{ formatDate(props.infoData[item.key]) }}
          </span>
          <span v-else-if="item.isPrice">
            {{ formatPriceDiv(props.infoData[item.key]) }}
          </span>
          <span v-else-if="item.isWeight">
            {{ formatWeightDiv(props.infoData[item.key]) }}
          </span>
          <span v-else-if="item.isTwo">
            {{ formatTwoDecimalsDiv(props.infoData[item.key]) }}
          </span>
          <span v-else>{{ props.infoData[item.key] }}</span>
          {{ item.unit || '' }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
</template>
<script setup lang="ts">
import FildCard from '@/components/FildCard.vue'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
interface Keys {
  copies?: number // 每项独占格数
  key: string
  label: string
  preUnit?: string // 前置单位一般都是 ‘￥’
  unit?: string // 后置单位
  isDate?: boolean // 日期转换
  isPrice?: boolean // 单价转换
  isWeight?: boolean // 数量转换
  isTwo?: boolean // 两位小数转换
}
export interface Props {
  infoData: object // 数据源
  infoKeys: Keys[] // 遍历配置项
  title?: string // 卡片头部信息
}

const props = withDefaults(defineProps<Props>(), {
  infoData: () => ({}),
  infoKeys: () => [],
  title: '基础信息',
})
</script>
